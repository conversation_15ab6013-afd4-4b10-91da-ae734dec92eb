<?php

namespace App\Mail;

use App\Models\Event;
use App\Models\EventAttendee;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class EventTicketPurchased extends Mailable
{
    use Queueable, SerializesModels;

    public User $user;
    public Event $event;
    public EventAttendee $attendee;

    /**
     * Create a new message instance.
     */
    public function __construct(User $user, Event $event, EventAttendee $attendee)
    {
        $this->user = $user;
        $this->event = $event;
        $this->attendee = $attendee;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: "Ingresso confirmado: {$this->event->name}",
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.event-ticket-purchased',
            with: [
                'user' => $this->user,
                'event' => $this->event,
                'attendee' => $this->attendee,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
