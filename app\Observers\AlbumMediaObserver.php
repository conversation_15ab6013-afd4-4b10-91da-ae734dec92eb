<?php

namespace App\Observers;

use App\Models\AlbumMedia;
use Illuminate\Support\Facades\Log;

class AlbumMediaObserver
{
    /**
     * Handle the AlbumMedia "created" event.
     */
    public function created(AlbumMedia $albumMedia): void
    {
        $this->syncAlbumMediaFiles($albumMedia);
    }

    /**
     * Handle the AlbumMedia "updated" event.
     */
    public function updated(AlbumMedia $albumMedia): void
    {
        $this->syncAlbumMediaFiles($albumMedia);
    }

    /**
     * Handle the AlbumMedia "deleted" event.
     */
    public function deleted(AlbumMedia $albumMedia): void
    {
        $this->cleanupAlbumMediaFiles($albumMedia);
    }

    /**
     * Sincroniza arquivos do album media para public/storage em produção
     */
    private function syncAlbumMediaFiles(AlbumMedia $albumMedia): void
    {
        // Apenas em produção
        if (!app()->environment('production')) {
            return;
        }

        try {
            // Sincronizar arquivo principal
            if ($albumMedia->file_path) {
                $this->syncFile($albumMedia->file_path);
            }

            // Sincronizar thumbnail
            if ($albumMedia->thumbnail_path) {
                $this->syncFile($albumMedia->thumbnail_path);
            }
        } catch (\Exception $e) {
            Log::error('Erro ao sincronizar arquivos do album media', [
                'album_media_id' => $albumMedia->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Remove arquivos do album media de public/storage
     */
    private function cleanupAlbumMediaFiles(AlbumMedia $albumMedia): void
    {
        // Apenas em produção
        if (!app()->environment('production')) {
            return;
        }

        try {
            // Remover arquivo principal
            if ($albumMedia->file_path) {
                $this->removeFile($albumMedia->file_path);
            }

            // Remover thumbnail
            if ($albumMedia->thumbnail_path) {
                $this->removeFile($albumMedia->thumbnail_path);
            }
        } catch (\Exception $e) {
            Log::error('Erro ao limpar arquivos do album media', [
                'album_media_id' => $albumMedia->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Sincroniza um arquivo específico
     */
    private function syncFile(string $path): void
    {
        $sourcePath = storage_path('app/public/' . $path);
        $publicPath = public_path('storage/' . $path);

        if (!file_exists($sourcePath)) {
            return;
        }

        // Criar diretório se não existir
        $publicDir = dirname($publicPath);
        if (!is_dir($publicDir)) {
            mkdir($publicDir, 0755, true);
        }

        // Copiar arquivo
        if (copy($sourcePath, $publicPath)) {
            Log::info("Arquivo de album media sincronizado automaticamente: {$path}");
        } else {
            Log::error("Falha ao sincronizar arquivo de album media: {$path}");
        }
    }

    /**
     * Remove um arquivo específico
     */
    private function removeFile(string $path): void
    {
        $publicPath = public_path('storage/' . $path);

        if (file_exists($publicPath)) {
            if (unlink($publicPath)) {
                Log::info("Arquivo de album media removido de public/storage: {$path}");
            } else {
                Log::error("Falha ao remover arquivo de album media: {$path}");
            }
        }
    }
}
