<div class="flex flex-col w-full">
    @if($showLocationError)
        <div class="w-full bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-xl mb-6 relative" role="alert">
            <strong class="font-bold">Atenção!</strong>
            <span class="block sm:inline">{{ $locationErrorMessage }}</span>
            @if(!auth()->user()->latitude || !auth()->user()->longitude)
                <div class="mt-2">
                    <p class="text-sm mb-2">
                        Sua localização será detectada automaticamente quando você permitir o acesso à sua localização no navegador.
                    </p>
                    <p class="text-sm">
                        Você também pode atualizar manualmente sua localização nas configurações do perfil:
                    </p>
                    <a href="{{ route('settings.profile') }}" class="mt-2 inline-block bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-4 rounded">
                        Ir para Configurações
                    </a>
                </div>
            @endif
        </div>
    @endif

    <div class="w-full bg-white dark:bg-zinc-800 rounded-lg shadow border border-neutral-200 dark:border-neutral-700 p-4 mb-6">
        <div class="flex justify-between items-center mb-2">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-300">Filtro de Distância</h3>
            <div class="flex items-center gap-2">
                <div class="flex items-center text-sm text-zinc-500">
                    <x-flux::icon icon="map-pin" class="w-4 h-4 mr-1" />
                    <span>Localização atualizada automaticamente</span>
                </div>
                <button
                    wire:click="reloadCandidates"
                    class="text-sm text-blue-500 hover:text-blue-600 flex items-center transition-colors duration-200"
                >
                    <x-flux::icon icon="arrow-path" class="w-4 h-4 mr-1" />
                    Recarregar
                </button>
            </div>
        </div>
        <div class="flex items-center gap-4">
            <input
                type="range"
                min="1"
                max="500"
                wire:model.live="maxDistance"
                class="w-full h-2 bg-zinc-200 rounded-lg appearance-none cursor-pointer dark:bg-zinc-700"
            >
            <span class="text-sm font-medium text-zinc-700 dark:text-zinc-300">{{ $maxDistance }} km</span>
        </div>
    </div>

    <div class="flex flex-col md:flex-row w-full gap-6">
        <div class="w-full md:w-2/5">
        @if($currentUser && isset($currentUser['user']))
            <div id="user_match" class="p-6 rounded-lg shadow border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-zinc-800 w-full text-center h-full"
                wire:key="current-user-{{ $currentUser['user']->id ?? 'none' }}">
            
            @php
                $avatar = $currentUser['user']->photos()->where('is_current', true)->first() ?? $currentUser['user']->photos()->first();
                $avatarUrl = $avatar ? Storage::url($avatar->photo_path) : asset('images/default-avatar.jpg');
            @endphp

            <img src="{{ $avatarUrl }}"
                 alt="Foto de {{ $currentUser['user']->name }}"
                 class="h-60 w-60 rounded-full object-cover border-2 {{ $currentUser['hasMatched'] ? 'border-green-400' : ($currentUser['hasLiked'] ? 'border-pink-400' : 'border-gray-400') }} mx-auto mb-4"
                 loading="lazy">

            <h2 class="text-xl font-bold text-gray-900 dark:text-gray-300">{{ $currentUser['user']->name }}</h2>
            <a href="{{ $currentUser['user']->username }}" class="text-blue-500 hover:text-blue-600">{{ $currentUser['user']->username }}</a>

            @if($currentUser['hasMatched'])
                <div class="inline-flex items-center px-3 py-1 mt-2 bg-green-100 text-green-800 rounded-full">
                    <x-flux::icon icon="check-badge" class="w-4 h-4 mr-1" />
                    <span class="text-sm font-medium">Match</span>
                </div>
            @endif

            <div class="flex items-center justify-center mt-2 text-zinc-500">
                <x-flux::icon icon="map-pin" class="w-4 h-4 mr-1" />
                <p class="text-sm">{{ number_format($currentUser['user']->distance, 1) }} km de distância</p>
            </div>

            <div class="flex justify-center mt-6 gap-4">
                <button
                    wire:click="pass({{ $currentUser['user']->id }})"
                    wire:key="pass-btn-{{ $currentUser['user']->id }}"
                    type="button"
                    class="bg-zinc-300 text-zinc-900 hover:bg-zinc-400 font-bold py-3 px-6 rounded-full flex items-center transition-colors duration-200"
                    x-data="{ processing: false }"
                    @click="processing = true; setTimeout(() => processing = false, 3000)"
                    :disabled="processing"
                    :class="{ 'opacity-50 cursor-not-allowed': processing }"
                >
                    <x-flux::icon icon="x-mark" class="w-5 h-5 mr-1" />
                    <span x-show="!processing">Pass</span>
                    <span x-show="processing">Processando...</span>
                </button>
                <button
                    wire:click="like({{ $currentUser['user']->id }})"
                    wire:key="like-btn-{{ $currentUser['user']->id }}"
                    type="button"
                    class="bg-pink-500 hover:bg-pink-600 text-white font-bold py-3 px-6 rounded-full flex items-center transition-colors duration-200"
                    x-data="{ processing: false }"
                    @click="processing = true; setTimeout(() => processing = false, 3000)"
                    :disabled="processing"
                    :class="{ 'opacity-50 cursor-not-allowed': processing }"
                >
                    <x-flux::icon icon="heart" class="w-5 h-5 mr-1" />
                    <span x-show="!processing">Like</span>
                    <span x-show="processing">Processando...</span>
                </button>
            </div>

            <div class="mt-4 text-center flex justify-center gap-4">
                <button
                    wire:click="nextUser()"
                    class="text-sm text-zinc-500 hover:text-zinc-700 flex items-center transition-colors duration-200"
                >
                    <x-flux::icon icon="arrow-right" class="w-4 h-4 mr-1" />
                    Próximo perfil
                </button>

                <button
                    wire:click="$refresh"
                    class="text-sm text-blue-500 hover:text-blue-600 flex items-center transition-colors duration-200"
                >
                    <x-flux::icon icon="arrow-path" class="w-4 h-4 mr-1" />
                    Atualizar
                </button>
            </div>
        </div>

    @elseif(!$showLocationError)
        <div class="bg-white dark:bg-zinc-800 rounded-lg shadow border border-neutral-200 dark:border-neutral-700 p-6 text-center h-full">
            <x-flux::icon icon="map" class="w-16 h-16 mx-auto text-zinc-400 mb-4" />
            <p class="text-zinc-600 dark:text-zinc-400 text-lg">Você já viu todo mundo por perto 🧭</p>
            <p class="text-zinc-500 dark:text-zinc-500 text-sm mt-2">Tente aumentar a distância máxima ou volte mais tarde.</p>
        </div>
    @endif
        </div>

        <div class="w-full md:w-3/5">
            @if(count($likedUsers) > 0)
            <div id="liked_users" class="p-6 rounded-lg shadow border border-neutral-200 dark:border-neutral-700 w-full bg-white dark:bg-zinc-800 mb-6"
                wire:key="liked-users-{{ count($likedUsers) }}">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-bold text-gray-900 dark:text-gray-300">Usuários que você curtiu:</h3>
                    <span class="text-sm text-zinc-500">{{ count($likedUsers) }} usuário(s)</span>
                </div>

                <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
                    @foreach($likedUsers as $likedData)
                        @php
                            $likedUser = $likedData['user'];
                            $likedAvatar = $likedUser->photos()->where('is_current', true)->first() ?? $likedUser->photos()->first();
                            $likedAvatarUrl = $likedAvatar ? Storage::url($likedAvatar->photo_path) : asset('images/default-avatar.jpg');
                        @endphp
                        <div wire:key="liked-user-{{ $likedUser->id }}" class="flex flex-col items-center">
                            <div class="relative">
                                <img src="{{ $likedAvatarUrl }}"
                                    alt="Foto de {{ $likedUser->name }}"
                                    class="h-14 w-14 rounded-full object-cover border-2 {{ $likedData['hasMatched'] ? 'border-green-400' : 'border-pink-400' }}">

                                @if($likedData['hasMatched'])
                                    <span class="absolute -top-1 -right-1 bg-green-500 rounded-full p-1">
                                        <x-flux::icon icon="check-badge" class="w-3 h-3 text-white" />
                                    </span>
                                @endif
                            </div>
                            <p class="text-xs mt-1 font-medium truncate w-full text-center">{{ $likedUser->name }}</p>
                            @if($likedData['hasMatched'])
                                <a href="{{ route('caixa_de_mensagens') }}?user={{ $likedUser->id }}" class="text-xs text-green-500 hover:text-green-600">Mensagem</a>
                            @endif
                        </div>
                    @endforeach
                </div>
            </div>
            @endif

            <div id="nearby_user" class="p-6 rounded-lg shadow border border-neutral-200 dark:border-neutral-700 w-full bg-white dark:bg-zinc-800 h-full"
                wire:key="nearby-users-{{ count($nearbyUsers) }}">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold text-gray-900 dark:text-gray-300">Usuários próximos:</h3>
                <span class="text-sm text-zinc-500">Ordenados por distância</span>
            </div>

            @if(count($nearbyUsers) > 0)
                <ul class="grid grid-cols-1 md:grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
                    @foreach($nearbyUsers as $userData)
                        @php
                            $user = $userData['user'];
                            $userAvatar = $user->photos()->where('is_current', true)->first() ?? $user->photos()->first();
                            $userAvatarUrl = $userAvatar ? Storage::url($userAvatar->photo_path) : asset('images/default-avatar.jpg');
                        @endphp
                        <li wire:key="nearby-user-{{ $user->id }}" class="flex items-center p-3 rounded-lg shadow border {{ $userData['hasMatched'] ? 'border-green-300 dark:border-green-700' : 'border-neutral-200 dark:border-neutral-700' }} bg-white dark:bg-zinc-800 hover:shadow-lg transition-shadow duration-200">
                            <div class="relative mr-3">
                                <img src="{{ $userAvatarUrl }}"
                                    alt="Foto de {{ $user->name }}"
                                    class="h-14 w-14 rounded-full object-cover border-2 {{ $userData['hasMatched'] ? 'border-green-400' : ($userData['hasLiked'] ? 'border-pink-400' : 'border-zinc-300') }}">
                                <livewire:user-status-indicator :userId="$user->id" />
                            </div>

                            <div class="flex-1">
                                <div class="flex items-center">
                                    <h4 class="font-semibold text-gray-900 dark:text-gray-300">{{ $user->name }}</h4>
                                    @if($userData['hasMatched'])
                                        <span class="ml-1 text-green-500">
                                            <x-flux::icon icon="check-badge" class="w-4 h-4" />
                                        </span>
                                    @endif
                                </div>
                                <a href="{{ $user->username }}" class="text-blue-500 hover:text-blue-600 text-sm">{{ $user->username }}</a>

                                <div class="flex items-center mt-1 text-zinc-500">
                                    <x-flux::icon icon="map-pin" class="w-4 h-4 mr-1" />
                                    <p class="text-sm">{{ number_format($user->distance, 1) }} km</p>
                                </div>
                            </div>

                            @if($userData['hasMatched'])
                                <a
                                    href="{{ route('caixa_de_mensagens') }}?user={{ $user->id }}"
                                    class="ml-2 bg-green-500 hover:bg-green-600 text-white font-medium py-1 px-2 rounded-full flex items-center"
                                >
                                    <x-flux::icon icon="chat-bubble-left-right" class="w-4 h-4" />
                                </a>
                            @endif
                        </li>
                    @endforeach
                </ul>
            @else
                <div class="text-center py-8 text-zinc-600 dark:text-zinc-400">
                    <x-flux::icon icon="users" class="w-12 h-12 mx-auto text-zinc-400 mb-3" />
                    <p>Nenhum usuário próximo encontrado dentro de {{ $maxDistance }}km.</p>
                    <p class="text-sm mt-2 text-zinc-500">Tente aumentar a distância máxima.</p>
                </div>
            @endif
        </div>
        </div>
    </div>

    @if (session()->has('match'))
        <div
            x-data="{ show: true }"
            x-show="show"
            x-transition:enter="transition ease-out duration-300"
            x-transition:enter-start="opacity-0 transform scale-90"
            x-transition:enter-end="opacity-100 transform scale-100"
            x-transition:leave="transition ease-in duration-300"
            x-transition:leave-start="opacity-100 transform scale-100"
            x-transition:leave-end="opacity-0 transform scale-90"
            class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75"
        >
            <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-2xl border border-neutral-200 dark:border-neutral-700 p-8 max-w-md w-full mx-4 text-center">
                <div class="relative">
                    <button
                        @click="show = false"
                        class="absolute top-0 right-0 text-zinc-500 hover:text-zinc-700"
                    >
                        <x-flux::icon icon="x-mark" class="w-6 h-6" />
                    </button>

                    <div class="py-6">
                        <div class="flex justify-center mb-4">
                            <div class="relative">
                                <x-flux::icon icon="heart" class="w-24 h-24 text-pink-500 animate-pulse" />
                            </div>
                        </div>

                        <h2 class="text-2xl font-bold text-pink-600 mb-2">É um Match!</h2>
                        <p class="text-zinc-600 dark:text-zinc-300 mb-6">
                            {{ is_array(session('match')) ? session('match')['message'] : session('match') }}
                        </p>

                        @if(is_array(session('match')) && isset(session('match')['user']))
                            <div class="flex justify-center mb-4">
                                @php
                                    $matchedUser = session('match')['user'];
                                    $matchedAvatar = $matchedUser->photos()->where('is_current', true)->first() ?? $matchedUser->photos()->first();
                                    $matchedAvatarUrl = $matchedAvatar ? Storage::url($matchedAvatar->photo_path) : asset('images/default-avatar.jpg');
                                @endphp
                                @if($matchedAvatarUrl)
                                    <img
                                        src="{{ $matchedAvatarUrl }}"
                                        alt="Foto de {{ $matchedUser->name }}"
                                        class="h-24 w-24 rounded-full object-cover border-4 border-pink-400"
                                    >
                                @else
                                    <div class="h-24 w-24 rounded-full bg-zinc-200 flex items-center justify-center border-4 border-pink-400">
                                        <x-flux::icon icon="user" class="w-12 h-12 text-zinc-400" />
                                    </div>
                                @endif
                            </div>
                        @endif

                        <div class="flex justify-center gap-4 mt-4">
                            <button
                                @click="show = false"
                                class="bg-zinc-200 hover:bg-zinc-300 text-zinc-800 font-medium py-2 px-4 rounded-full"
                            >
                                Continuar Explorando
                            </button>

                            @if(is_array(session('match')) && isset(session('match')['user']))
                                <a
                                    href="{{ route('caixa_de_mensagens') }}?user={{ session('match')['user']->id }}"
                                    class="bg-pink-500 hover:bg-pink-600 text-white font-medium py-2 px-4 rounded-full flex items-center"
                                >
                                    <x-flux::icon icon="chat-bubble-left-right" class="w-5 h-5 mr-1" />
                                    Enviar Mensagem
                                </a>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Sistema de recuperação para erros de snapshot
            window.addEventListener('error', function(event) {
                if (event.message && event.message.includes('Snapshot missing')) {
                    console.warn('Erro de snapshot detectado, recarregando página...');
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                }
            });

            // Intercepta erros do Livewire
            document.addEventListener('livewire:init', () => {
                Livewire.hook('request', ({ fail }) => {
                    fail(({ status, content, preventDefault }) => {
                        if (status === 422 || content.includes('snapshot')) {
                            console.warn('Erro de snapshot no Livewire, recarregando...');
                            preventDefault();
                            setTimeout(() => {
                                window.location.reload();
                            }, 1000);
                        }
                    });
                });
            });
        });
    </script>
</div>
