<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class StorageHelper
{
    /**
     * Gera URL segura para arquivos de storage
     */
    public static function getSecureUrl($path, $disk = 'public')
    {
        if (empty($path)) {
            return null;
        }

        try {
            // Verificar se o arquivo existe no storage
            if (!Storage::disk($disk)->exists($path)) {
                Log::warning("Arquivo não encontrado no storage: {$path}");
                return null;
            }

            // Em produção, verificar se precisa copiar arquivo para public/storage
            if (app()->environment('production')) {
                self::ensureFileInPublicStorage($path);
            }

            // Gerar URL
            $url = Storage::disk($disk)->url($path);

            // Log para debug em produção
            if (app()->environment('production')) {
                Log::debug("URL gerada para storage", [
                    'path' => $path,
                    'url' => $url,
                    'disk' => $disk,
                    'app_url' => config('app.url')
                ]);
            }

            return $url;
        } catch (\Exception $e) {
            Log::error("Erro ao gerar URL de storage", [
                'path' => $path,
                'disk' => $disk,
                'error' => $e->getMessage()
            ]);

            return null;
        }
    }

    /**
     * Garante que o arquivo existe em public/storage (para KingHost)
     */
    private static function ensureFileInPublicStorage($path)
    {
        try {
            $sourcePath = storage_path('app/public/' . $path);
            $publicPath = public_path('storage/' . $path);

            // Se o arquivo não existe no public/storage ou está desatualizado
            if (
                !file_exists($publicPath) ||
                (file_exists($sourcePath) && filemtime($sourcePath) > filemtime($publicPath))
            ) {

                // Criar diretório se não existir
                $publicDir = dirname($publicPath);
                if (!is_dir($publicDir)) {
                    mkdir($publicDir, 0755, true);
                }

                // Copiar arquivo
                if (file_exists($sourcePath)) {
                    copy($sourcePath, $publicPath);
                    Log::info("Arquivo copiado para public/storage: {$path}");
                }
            }
        } catch (\Exception $e) {
            Log::error("Erro ao copiar arquivo para public/storage", [
                'path' => $path,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Gera URL para imagem de post com fallback
     */
    public static function getPostImageUrl($imagePath)
    {
        if (empty($imagePath)) {
            return null;
        }

        // Em produção, garantir que o arquivo está em public/storage
        if (app()->environment('production')) {
            self::ensureFileInPublicStorage($imagePath);
        }

        $url = self::getSecureUrl($imagePath);

        if (!$url) {
            // Fallback: tentar URL direta se o Storage::url falhar
            $baseUrl = config('app.asset_url') ?: config('app.url');
            $url = $baseUrl . '/storage/' . $imagePath;

            Log::warning("Usando URL de fallback para imagem", [
                'path' => $imagePath,
                'fallback_url' => $url,
                'base_url' => $baseUrl
            ]);
        }

        return $url;
    }

    /**
     * Gera URL para avatar com fallback para imagem padrão
     */
    public static function getAvatarUrl($photoPath)
    {
        if (empty($photoPath)) {
            return asset('images/default-avatar.jpg');
        }

        $url = self::getSecureUrl($photoPath);

        return $url ?: asset('images/default-avatar.jpg');
    }

    /**
     * Verifica se o storage está configurado corretamente
     */
    public static function checkStorageHealth()
    {
        $issues = [];

        // Verificar se o disco público está configurado
        try {
            $disk = Storage::disk('public');
            $config = config('filesystems.disks.public');

            if (!$config) {
                $issues[] = 'Disco público não configurado';
            }

            // Verificar se o diretório root existe
            $root = $config['root'] ?? null;
            if (!$root || !is_dir($root)) {
                $issues[] = 'Diretório root do storage não existe: ' . $root;
            }

            // Verificar se é gravável
            if ($root && !is_writable($root)) {
                $issues[] = 'Diretório storage não é gravável: ' . $root;
            }

            // Verificar link simbólico
            $publicStorage = public_path('storage');
            if (!is_link($publicStorage)) {
                $issues[] = 'Link simbólico storage não existe';
            }

            // Verificar URL base
            $appUrl = config('app.url');
            $expectedUrl = $appUrl . '/storage';
            $diskUrl = $config['url'] ?? null;

            if ($diskUrl !== $expectedUrl) {
                $issues[] = "URL do disco incorreta. Esperado: {$expectedUrl}, Atual: {$diskUrl}";
            }
        } catch (\Exception $e) {
            $issues[] = 'Erro ao verificar configuração: ' . $e->getMessage();
        }

        return $issues;
    }

    /**
     * Tenta corrigir problemas comuns de storage
     */
    public static function fixCommonIssues()
    {
        $fixes = [];

        try {
            // Criar diretórios necessários
            $directories = [
                'posts/images',
                'posts/videos',
                'avatars',
                'covers',
                'albums',
                'groups',
                'events',
                'products'
            ];

            foreach ($directories as $dir) {
                if (!Storage::disk('public')->exists($dir)) {
                    Storage::disk('public')->makeDirectory($dir);
                    $fixes[] = "Diretório criado: {$dir}";
                }
            }

            // Verificar e criar link simbólico se necessário
            $publicStorage = public_path('storage');
            $storagePublic = storage_path('app/public');

            if (!is_link($publicStorage)) {
                if (is_dir($publicStorage)) {
                    // Remove diretório existente
                    \Illuminate\Support\Facades\File::deleteDirectory($publicStorage);
                }

                if (symlink($storagePublic, $publicStorage)) {
                    $fixes[] = 'Link simbólico criado';
                }
            }
        } catch (\Exception $e) {
            Log::error('Erro ao corrigir problemas de storage: ' . $e->getMessage());
        }

        return $fixes;
    }

    /**
     * Gera relatório de debug para produção
     */
    public static function generateDebugReport()
    {
        $report = [
            'timestamp' => now()->toISOString(),
            'environment' => app()->environment(),
            'app_url' => config('app.url'),
            'storage_config' => config('filesystems.disks.public'),
            'storage_health' => self::checkStorageHealth(),
            'directories' => [],
            'sample_urls' => []
        ];

        // Verificar diretórios
        $directories = ['posts/images', 'posts/videos', 'avatars', 'covers'];
        foreach ($directories as $dir) {
            $fullPath = storage_path('app/public/' . $dir);
            $report['directories'][$dir] = [
                'exists' => is_dir($fullPath),
                'writable' => is_dir($fullPath) ? is_writable($fullPath) : false,
                'files_count' => is_dir($fullPath) ? count(glob($fullPath . '/*')) : 0
            ];
        }

        // Gerar URLs de exemplo
        $sampleFiles = Storage::disk('public')->files('posts/images');
        foreach (array_slice($sampleFiles, 0, 3) as $file) {
            $report['sample_urls'][] = [
                'file' => $file,
                'url' => Storage::disk('public')->url($file),
                'exists' => Storage::disk('public')->exists($file)
            ];
        }

        return $report;
    }
}
