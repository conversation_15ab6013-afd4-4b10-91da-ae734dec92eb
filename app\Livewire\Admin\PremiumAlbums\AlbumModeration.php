<?php

namespace App\Livewire\Admin\PremiumAlbums;

use App\Models\Album;
use App\Services\PremiumAlbumService;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Auth;

class AlbumModeration extends Component
{
    use WithPagination;

    public $search = '';
    public $status = 'pending'; // pending, approved, suspended, all
    public $sortBy = 'created_at';
    public $sortDirection = 'desc';
    
    public $showApprovalModal = false;
    public $showRejectionModal = false;
    public $selectedAlbum = null;
    public $rejectionReason = '';

    protected $queryString = [
        'search' => ['except' => ''],
        'status' => ['except' => 'pending'],
        'sortBy' => ['except' => 'created_at'],
        'sortDirection' => ['except' => 'desc'],
    ];

    public function mount()
    {
        // Verificar se é admin
        if (Auth::user()->role !== 'administrador') {
            abort(403, 'Acesso negado.');
        }
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingStatus()
    {
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortBy === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $field;
            $this->sortDirection = 'asc';
        }
        $this->resetPage();
    }

    public function confirmApproval($albumId)
    {
        $this->selectedAlbum = Album::findOrFail($albumId);
        $this->showApprovalModal = true;
    }

    public function confirmRejection($albumId)
    {
        $this->selectedAlbum = Album::findOrFail($albumId);
        $this->showRejectionModal = true;
    }

    public function approveAlbum()
    {
        if (!$this->selectedAlbum) {
            return;
        }

        try {
            $albumService = new PremiumAlbumService();
            $result = $albumService->approveAlbum($this->selectedAlbum, Auth::user());

            if ($result['success']) {
                session()->flash('success', $result['message']);
                $this->resetPage();
            } else {
                session()->flash('error', 'Erro ao aprovar álbum.');
            }

        } catch (\Exception $e) {
            session()->flash('error', $e->getMessage());
        } finally {
            $this->closeModals();
        }
    }

    public function rejectAlbum()
    {
        if (!$this->selectedAlbum) {
            return;
        }

        $this->validate([
            'rejectionReason' => 'required|string|min:10|max:500'
        ]);

        try {
            $albumService = new PremiumAlbumService();
            $result = $albumService->rejectAlbum($this->selectedAlbum, Auth::user(), $this->rejectionReason);

            if ($result['success']) {
                session()->flash('success', $result['message']);
                $this->resetPage();
            } else {
                session()->flash('error', 'Erro ao rejeitar álbum.');
            }

        } catch (\Exception $e) {
            session()->flash('error', $e->getMessage());
        } finally {
            $this->closeModals();
        }
    }

    public function closeModals()
    {
        $this->showApprovalModal = false;
        $this->showRejectionModal = false;
        $this->selectedAlbum = null;
        $this->rejectionReason = '';
        $this->resetValidation();
    }

    public function getAlbumsProperty()
    {
        $query = Album::premium()
                     ->with(['user', 'medias'])
                     ->withCount(['purchases', 'reviews']);

        // Filtro de busca
        if ($this->search) {
            $query->where(function($q) {
                $q->where('name', 'like', '%' . $this->search . '%')
                  ->orWhere('description', 'like', '%' . $this->search . '%')
                  ->orWhereHas('user', function($userQuery) {
                      $userQuery->where('name', 'like', '%' . $this->search . '%')
                               ->orWhere('username', 'like', '%' . $this->search . '%');
                  });
            });
        }

        // Filtro de status
        switch ($this->status) {
            case 'pending':
                $query->where('admin_approved', false)->where('status', '!=', 'suspended');
                break;
            case 'approved':
                $query->where('admin_approved', true)->where('status', 'published');
                break;
            case 'suspended':
                $query->where('status', 'suspended');
                break;
            // 'all' não adiciona filtro
        }

        // Ordenação
        $query->orderBy($this->sortBy, $this->sortDirection);

        return $query->paginate(12);
    }

    public function getStatsProperty()
    {
        return [
            'pending' => Album::premium()->where('admin_approved', false)->where('status', '!=', 'suspended')->count(),
            'approved' => Album::premium()->where('admin_approved', true)->where('status', 'published')->count(),
            'suspended' => Album::premium()->where('status', 'suspended')->count(),
            'total' => Album::premium()->count(),
        ];
    }

    public function getStatusBadgeClass($album)
    {
        if ($album->status === 'suspended') {
            return 'bg-red-100 text-red-800';
        } elseif ($album->admin_approved && $album->status === 'published') {
            return 'bg-green-100 text-green-800';
        } else {
            return 'bg-yellow-100 text-yellow-800';
        }
    }

    public function getStatusText($album)
    {
        if ($album->status === 'suspended') {
            return 'Suspenso';
        } elseif ($album->admin_approved && $album->status === 'published') {
            return 'Aprovado';
        } else {
            return 'Pendente';
        }
    }

    public function render()
    {
        return view('livewire.admin.premium-albums.album-moderation', [
            'albums' => $this->albums,
            'stats' => $this->stats,
        ])->layout('layouts.app');
    }
}
