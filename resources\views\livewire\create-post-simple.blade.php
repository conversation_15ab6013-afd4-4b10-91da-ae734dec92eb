<div class="p-6 border border-neutral-200 dark:border-neutral-700 shadow-md rounded-lg">
    <form wire:submit.prevent="store" enctype="multipart/form-data">
        <textarea
            class="w-full rounded-lg border-gray-300 dark:border-gray-700 dark:bg-zinc-700 dark:text-gray-300 p-3"
            rows="3"
            placeholder="O que você está pensando?"
            wire:model="content"
        ></textarea>
        
        <div class="flex justify-between mt-3">
            <div class="flex space-x-4">
                <input wire:model="image" type="file" accept="image/*">
                <input wire:model="video" type="file" accept="video/*">
            </div>
            <button type="submit" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg">
                Postar
            </button>
        </div>
    </form>
</div>
