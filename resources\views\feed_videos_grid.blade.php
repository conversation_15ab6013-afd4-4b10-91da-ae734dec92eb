<?php
use Illuminate\Support\Facades\Storage;
?>

<x-layouts.app :title="__('Feed de Vídeos')">
    <div class="bg-white dark:bg-zinc-700 min-h-screen py-6 sm:py-8 lg:py-12">
        <div class="mx-auto max-w-screen-2xl px-4 md:px-8">
            
            <!-- Header with View Toggle -->
            <div class="flex items-center justify-between mb-6">
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Feed de Vídeos</h1>
                <div class="flex items-center space-x-2">
                    <span class="text-sm text-gray-600 dark:text-gray-300">Modo:</span>
                    <span class="text-sm text-gray-500 dark:text-gray-400">Grid</span>
                    <a href="{{ route('feed_videos') }}"
                       class="inline-flex items-center px-3 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors"
                       wire:navigate>
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4m-4 0l5.172 5.172a2.828 2.828 0 010 4L1 17.172"></path>
                        </svg>
                        Ver Fullscreen
                    </a>
                </div>
            </div>

            @if($posts->count() > 0)
                <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 md:gap-6 xl:gap-8">
                    @foreach ($posts as $post)
                        @if($post->video)
                            <!-- video - start -->
                            <div class="group relative overflow-hidden rounded-lg bg-gray-100 shadow-lg">
                                <a href="{{ route('feed_videos') }}?post={{ $post->id }}"
                                   class="block relative aspect-video"
                                   wire:navigate>
                                    <video 
                                        src="{{ Storage::url($post->video) }}" 
                                        class="absolute inset-0 h-full w-full object-cover transition duration-200 group-hover:scale-110"
                                        muted
                                        preload="metadata"
                                        poster="{{ Storage::url($post->video) }}#t=1"
                                    ></video>

                                    <!-- Play Button Overlay -->
                                    <div class="absolute inset-0 flex items-center justify-center bg-black/20 group-hover:bg-black/30 transition-colors">
                                        <div class="bg-white/90 rounded-full p-3 group-hover:bg-white transition-colors">
                                            <svg class="w-6 h-6 text-gray-800" fill="currentColor" viewBox="0 0 24 24">
                                                <path d="M8 5v14l11-7z"/>
                                            </svg>
                                        </div>
                                    </div>

                                    <div class="pointer-events-none absolute inset-0 bg-gradient-to-t from-gray-800 via-transparent to-transparent opacity-50"></div>
                                </a>

                                <div class="absolute bottom-0 left-0 right-0 p-4">
                                    @if($post->user)
                                        <div class="flex items-center mb-2">
                                            <img 
                                                src="{{ $post->user->userPhotos->first() ? Storage::url($post->user->userPhotos->first()->photo_path) : asset('images/default-avatar.jpg') }}" 
                                                alt="{{ $post->user->name }}"
                                                class="w-8 h-8 rounded-full border-2 border-white/20 mr-2"
                                            />
                                            <p class="text-xs text-white/80">{{ $post->user->name }}</p>
                                        </div>
                                    @endif
                                    @if($post->title)
                                        <h3 class="text-sm font-medium text-white mb-1">{{ Str::limit($post->title, 50) }}</h3>
                                    @endif
                                    @if($post->content)
                                        <p class="text-xs text-white/80 line-clamp-2">{{ Str::limit(strip_tags($post->content), 80) }}</p>
                                    @endif
                                </div>
                            </div>
                            <!-- video - end -->
                        @endif
                    @endforeach
                </div>
            @else
                <div class="flex flex-col items-center justify-center py-12">
                    <div class="text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">Nenhum vídeo encontrado</h3>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Ainda não há posts com vídeos para exibir.</p>
                    </div>
                </div>
            @endif
        </div>
    </div>
</x-layouts.app>
