<?php

namespace App\Services;

use App\Models\UserActivity;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Request;

class ActivityTrackingService
{
    /**
     * Registra evento de login
     */
    public static function trackLogin($user)
    {
        UserActivity::create([
            'user_id' => $user->id,
            'session_id' => session()->getId(),
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
            'activity_type' => 'login',
            'url' => Request::url(),
            'route_name' => Request::route()?->getName(),
            'device_type' => self::detectDeviceType(Request::userAgent()),
            'additional_data' => [
                'login_method' => 'web',
                'user_role' => $user->role,
                'timestamp' => now()->toIso8601String(),
            ],
        ]);
    }

    /**
     * Registra evento de logout
     */
    public static function trackLogout($user)
    {
        UserActivity::create([
            'user_id' => $user->id,
            'session_id' => session()->getId(),
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
            'activity_type' => 'logout',
            'url' => Request::url(),
            'route_name' => Request::route()?->getName(),
            'device_type' => self::detectDeviceType(Request::userAgent()),
            'additional_data' => [
                'session_duration' => self::calculateSessionDuration($user),
                'timestamp' => now()->toIso8601String(),
            ],
        ]);
    }

    /**
     * Registra criação de post
     */
    public static function trackPostCreated($post)
    {
        UserActivity::create([
            'user_id' => $post->user_id,
            'session_id' => session()->getId(),
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
            'activity_type' => 'post_created',
            'url' => Request::url(),
            'route_name' => Request::route()?->getName(),
            'device_type' => self::detectDeviceType(Request::userAgent()),
            'additional_data' => [
                'post_id' => $post->id,
                'has_image' => !empty($post->image),
                'has_video' => !empty($post->video),
                'content_length' => strlen($post->content ?? ''),
                'group_id' => $post->group_id,
                'timestamp' => now()->toIso8601String(),
            ],
        ]);
    }

    /**
     * Registra like em post
     */
    public static function trackPostLiked($post, $user)
    {
        UserActivity::create([
            'user_id' => $user->id,
            'session_id' => session()->getId(),
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
            'activity_type' => 'post_liked',
            'url' => Request::url(),
            'route_name' => Request::route()?->getName(),
            'device_type' => self::detectDeviceType(Request::userAgent()),
            'additional_data' => [
                'post_id' => $post->id,
                'post_author_id' => $post->user_id,
                'timestamp' => now()->toIso8601String(),
            ],
        ]);
    }

    /**
     * Registra envio de mensagem
     */
    public static function trackMessageSent($message)
    {
        UserActivity::create([
            'user_id' => $message->sender_id,
            'session_id' => session()->getId(),
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
            'activity_type' => 'message_sent',
            'url' => Request::url(),
            'route_name' => Request::route()?->getName(),
            'device_type' => self::detectDeviceType(Request::userAgent()),
            'additional_data' => [
                'message_id' => $message->id,
                'conversation_id' => $message->conversation_id,
                'recipient_id' => $message->recipient_id,
                'message_length' => strlen($message->content ?? ''),
                'has_attachment' => !empty($message->attachment),
                'timestamp' => now()->toIso8601String(),
            ],
        ]);
    }

    /**
     * Registra entrada em grupo
     */
    public static function trackGroupJoined($group, $user)
    {
        UserActivity::create([
            'user_id' => $user->id,
            'session_id' => session()->getId(),
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
            'activity_type' => 'group_joined',
            'url' => Request::url(),
            'route_name' => Request::route()?->getName(),
            'device_type' => self::detectDeviceType(Request::userAgent()),
            'additional_data' => [
                'group_id' => $group->id,
                'group_name' => $group->name,
                'group_type' => $group->type ?? 'public',
                'timestamp' => now()->toIso8601String(),
            ],
        ]);
    }

    /**
     * Registra inscrição em evento
     */
    public static function trackEventRegistration($event, $user)
    {
        UserActivity::create([
            'user_id' => $user->id,
            'session_id' => session()->getId(),
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
            'activity_type' => 'event_registered',
            'url' => Request::url(),
            'route_name' => Request::route()?->getName(),
            'device_type' => self::detectDeviceType(Request::userAgent()),
            'additional_data' => [
                'event_id' => $event->id,
                'event_title' => $event->title,
                'event_price' => $event->price,
                'event_date' => $event->date_time,
                'timestamp' => now()->toIso8601String(),
            ],
        ]);
    }

    /**
     * Registra compra na loja
     */
    public static function trackPurchase($order)
    {
        UserActivity::create([
            'user_id' => $order->user_id,
            'session_id' => session()->getId(),
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
            'activity_type' => 'purchase_made',
            'url' => Request::url(),
            'route_name' => Request::route()?->getName(),
            'device_type' => self::detectDeviceType(Request::userAgent()),
            'additional_data' => [
                'order_id' => $order->id,
                'total_amount' => $order->total,
                'items_count' => $order->items->count(),
                'payment_method' => $order->payment_method,
                'timestamp' => now()->toIso8601String(),
            ],
        ]);
    }

    /**
     * Registra busca realizada
     */
    public static function trackSearch($query, $results_count = null)
    {
        UserActivity::create([
            'user_id' => Auth::id(),
            'session_id' => session()->getId(),
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
            'activity_type' => 'search_performed',
            'url' => Request::url(),
            'route_name' => Request::route()?->getName(),
            'device_type' => self::detectDeviceType(Request::userAgent()),
            'additional_data' => [
                'search_query' => $query,
                'results_count' => $results_count,
                'search_type' => 'user_search', // ou 'post_search', etc.
                'timestamp' => now()->toIso8601String(),
            ],
        ]);
    }

    /**
     * Registra visualização de perfil
     */
    public static function trackProfileView($profileUser, $viewerUser = null)
    {
        $viewerUser = $viewerUser ?: Auth::user();
        
        UserActivity::create([
            'user_id' => $viewerUser?->id,
            'session_id' => session()->getId(),
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
            'activity_type' => 'profile_viewed',
            'url' => Request::url(),
            'route_name' => Request::route()?->getName(),
            'device_type' => self::detectDeviceType(Request::userAgent()),
            'additional_data' => [
                'profile_user_id' => $profileUser->id,
                'profile_username' => $profileUser->username,
                'is_own_profile' => $viewerUser && $viewerUser->id === $profileUser->id,
                'timestamp' => now()->toIso8601String(),
            ],
        ]);
    }

    /**
     * Detecta o tipo de dispositivo baseado no User Agent
     */
    private static function detectDeviceType(?string $userAgent): string
    {
        if (!$userAgent) return 'unknown';

        $userAgent = strtolower($userAgent);

        if (strpos($userAgent, 'mobile') !== false || 
            strpos($userAgent, 'android') !== false || 
            strpos($userAgent, 'iphone') !== false) {
            return 'mobile';
        }

        if (strpos($userAgent, 'tablet') !== false || 
            strpos($userAgent, 'ipad') !== false) {
            return 'tablet';
        }

        return 'desktop';
    }

    /**
     * Calcula duração da sessão
     */
    private static function calculateSessionDuration($user): ?int
    {
        $lastLogin = UserActivity::where('user_id', $user->id)
            ->where('activity_type', 'login')
            ->orderBy('created_at', 'desc')
            ->first();

        if ($lastLogin) {
            return now()->diffInMinutes($lastLogin->created_at);
        }

        return null;
    }
}
