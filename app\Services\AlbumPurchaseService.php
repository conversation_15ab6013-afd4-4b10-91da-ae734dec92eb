<?php

namespace App\Services;

use App\Models\Album;
use App\Models\AlbumPurchase;
use App\Models\AlbumEarning;
use App\Models\User;
use App\Models\Wallet;
use App\Models\WalletTransaction;
use App\Models\PremiumAlbumSetting;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

class AlbumPurchaseService
{
    /**
     * Processa a compra de um álbum.
     */
    public function purchaseAlbum(Album $album, User $buyer, string $paymentMethod = 'wallet')
    {
        // Validações iniciais
        $this->validatePurchase($album, $buyer);

        DB::beginTransaction();
        
        try {
            // Calcular valores
            $amount = $album->price;
            $commissionRate = $album->commission_rate ?? PremiumAlbumSetting::getCommissionRate();
            $commission = ($amount * $commissionRate) / 100;
            $sellerAmount = $amount - $commission;

            // Criar registro de compra
            $purchase = AlbumPurchase::create([
                'album_id' => $album->id,
                'buyer_id' => $buyer->id,
                'seller_id' => $album->user_id,
                'amount' => $amount,
                'commission' => $commission,
                'seller_amount' => $sellerAmount,
                'payment_method' => $paymentMethod,
                'status' => AlbumPurchase::STATUS_PENDING,
            ]);

            // Processar pagamento
            if ($paymentMethod === 'wallet') {
                $this->processWalletPayment($purchase, $buyer);
            } else {
                // Para Stripe, retornar dados para criar sessão
                return $this->prepareStripePayment($purchase);
            }

            // Finalizar compra
            $this->completePurchase($purchase);

            DB::commit();

            Log::info('Compra de álbum realizada com sucesso', [
                'purchase_id' => $purchase->id,
                'album_id' => $album->id,
                'buyer_id' => $buyer->id,
                'amount' => $amount,
                'payment_method' => $paymentMethod
            ]);

            return [
                'success' => true,
                'purchase' => $purchase,
                'message' => 'Álbum comprado com sucesso!'
            ];

        } catch (Exception $e) {
            DB::rollBack();
            
            Log::error('Erro ao processar compra de álbum', [
                'album_id' => $album->id,
                'buyer_id' => $buyer->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Valida se a compra pode ser realizada.
     */
    private function validatePurchase(Album $album, User $buyer)
    {
        // Verificar se o álbum é premium
        if (!$album->is_premium) {
            throw new Exception('Este álbum não é premium.');
        }

        // Verificar se está publicado e aprovado
        if ($album->status !== 'published' || !$album->admin_approved) {
            throw new Exception('Este álbum não está disponível para compra.');
        }

        // Verificar se o comprador não é o próprio criador
        if ($album->user_id === $buyer->id) {
            throw new Exception('Você não pode comprar seu próprio álbum.');
        }

        // Verificar se já comprou
        if ($album->isPurchasedBy($buyer)) {
            throw new Exception('Você já possui este álbum.');
        }

        // Verificar se o usuário pode comprar (não é visitante)
        if ($buyer->role === 'visitante') {
            throw new Exception('Apenas usuários VIP podem comprar álbuns premium.');
        }
    }

    /**
     * Processa pagamento via carteira.
     */
    private function processWalletPayment(AlbumPurchase $purchase, User $buyer)
    {
        $wallet = $buyer->wallet;
        
        if (!$wallet) {
            throw new Exception('Carteira não encontrada.');
        }

        if ($wallet->balance < $purchase->amount) {
            throw new Exception('Saldo insuficiente na carteira.');
        }

        // Debitar da carteira do comprador
        $wallet->debit(
            $purchase->amount,
            'album_purchase',
            $purchase->id,
            AlbumPurchase::class,
            "Compra do álbum: {$purchase->album->name}"
        );
    }

    /**
     * Prepara dados para pagamento via Stripe.
     */
    private function prepareStripePayment(AlbumPurchase $purchase)
    {
        return [
            'success' => false,
            'requires_stripe' => true,
            'purchase' => $purchase,
            'stripe_data' => [
                'amount' => $purchase->amount * 100, // Stripe usa centavos
                'currency' => 'brl',
                'description' => "Compra do álbum: {$purchase->album->name}",
                'metadata' => [
                    'purchase_id' => $purchase->id,
                    'album_id' => $purchase->album_id,
                    'buyer_id' => $purchase->buyer_id,
                ]
            ]
        ];
    }

    /**
     * Finaliza a compra após pagamento bem-sucedido.
     */
    public function completePurchase(AlbumPurchase $purchase)
    {
        // Marcar compra como completada
        $purchase->markAsCompleted();

        // Creditar valor para o vendedor
        $this->creditSeller($purchase);

        // Criar registro de ganhos
        $this->createEarningsRecord($purchase);

        // Atualizar estatísticas do álbum
        $purchase->album->incrementSales($purchase->seller_amount);

        // Enviar notificações
        $this->sendNotifications($purchase);
    }

    /**
     * Credita o valor para o vendedor.
     */
    private function creditSeller(AlbumPurchase $purchase)
    {
        $seller = $purchase->seller;
        $wallet = $seller->wallet;

        if ($wallet) {
            $wallet->credit(
                $purchase->seller_amount,
                'album_sale',
                $purchase->id,
                AlbumPurchase::class,
                "Venda do álbum: {$purchase->album->name}"
            );
        }
    }

    /**
     * Cria registro de ganhos.
     */
    private function createEarningsRecord(AlbumPurchase $purchase)
    {
        AlbumEarning::create([
            'user_id' => $purchase->seller_id,
            'album_id' => $purchase->album_id,
            'purchase_id' => $purchase->id,
            'gross_amount' => $purchase->amount,
            'commission' => $purchase->commission,
            'net_amount' => $purchase->seller_amount,
            'status' => AlbumEarning::STATUS_PAID,
            'paid_at' => now(),
        ]);
    }

    /**
     * Envia notificações relacionadas à compra.
     */
    private function sendNotifications(AlbumPurchase $purchase)
    {
        // TODO: Implementar notificações
        // - Notificar vendedor sobre a venda
        // - Notificar comprador sobre acesso liberado
        // - Email de confirmação
    }

    /**
     * Processa webhook do Stripe para completar compra.
     */
    public function handleStripeWebhook($sessionId, $paymentIntentId)
    {
        $purchase = AlbumPurchase::where('stripe_session_id', $sessionId)->first();
        
        if (!$purchase) {
            throw new Exception('Compra não encontrada para esta sessão Stripe.');
        }

        DB::beginTransaction();
        
        try {
            $purchase->update([
                'stripe_payment_intent_id' => $paymentIntentId,
            ]);

            $this->completePurchase($purchase);
            
            DB::commit();
            
            return $purchase;
            
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
}
