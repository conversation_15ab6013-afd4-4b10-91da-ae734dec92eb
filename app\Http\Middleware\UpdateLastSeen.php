<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Auth;

class UpdateLastSeen
{
    public function handle(Request $request, Closure $next)
    {
        if (Auth::check()) {
            $user = Auth::user();
            $userId = $user->id;

            // Usar cache para reduzir atualizações frequentes no banco de dados
            // Atualiza apenas a cada 30 segundos para o mesmo usuário
            $cacheKey = "user_last_seen_{$userId}";

            if (!Cache::has($cacheKey)) {
                $user->forceFill([
                    'last_seen' => now(),
                    // Mantém o status atual se for dnd, senão define como online
                    'status' => $user->status === 'dnd' ? 'dnd' : 'online',
                ])->saveQuietly();

                // Armazena em cache por 30 segundos
                Cache::put($cacheKey, true, now()->addSeconds(30));
            }
        }

        return $next($request);
    }
}
