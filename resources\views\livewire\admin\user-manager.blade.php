<div>
    <div class="bg-white dark:bg-zinc-800 shadow-md rounded-lg ">
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-xl font-semibold text-gray-800 dark:text-white">Gerenciamento de Usuários</h2>

            <x-flux::button wire:click="$dispatch('openModal', ['create'])" color="primary">
                <x-flux::icon icon="plus" class="w-5 h-5 mr-2" />
                Novo Usuário
            </x-flux::button>
        </div>

        <!-- Filtros e Busca -->
        <div class="mb-6 grid grid-cols-1 gap-4 md:grid-cols-4">
            <x-flux::input
                wire:model.live.debounce.300ms="search"
                placeholder="Buscar usuários..."
                icon="magnifying-glass"
            />

            <x-flux::select wire:model.live="roleFilter">
                <option value="">Todos os papéis</option>
                <option value="visitante">Visitante</option>
                <option value="vip">VIP</option>
                <option value="admin">Administrador</option>
            </x-flux::select>

            <x-flux::select wire:model.live="statusFilter">
                <option value="active">Usuários Ativos</option>
                <option value="inactive">Usuários Inativos</option>
                <option value="all">Todos os Status</option>
            </x-flux::select>

            <x-flux::select wire:model.live="perPage">
                <option value="10">10 por página</option>
                <option value="25">25 por página</option>
                <option value="50">50 por página</option>
                <option value="100">100 por página</option>
            </x-flux::select>
        </div>

        <!-- Tabela de Usuários -->
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-zinc-700">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer" wire:click="sortBy('id')">
                            ID
                            @if($sortBy === 'id')
                                <x-flux::icon name="{{ $sortDirection === 'asc' ? 'arrow-up' : 'arrow-down' }}" class="h-3 w-3 inline" />
                            @endif
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer" wire:click="sortBy('name')">
                            Nome
                            @if($sortBy === 'name')
                                <x-flux::icon name="{{ $sortDirection === 'asc' ? 'arrow-up' : 'arrow-down' }}" class="h-3 w-3 inline" />
                            @endif
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer" wire:click="sortBy('email')">
                            Email
                            @if($sortBy === 'email')
                                <x-flux::icon name="{{ $sortDirection === 'asc' ? 'arrow-up' : 'arrow-down' }}" class="h-3 w-3 inline" />
                            @endif
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer" wire:click="sortBy('role')">
                            Papel
                            @if($sortBy === 'role')
                                <x-flux::icon name="{{ $sortDirection === 'asc' ? 'arrow-up' : 'arrow-down' }}" class="h-3 w-3 inline" />
                            @endif
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer" wire:click="sortBy('active')">
                            Status
                            @if($sortBy === 'active')
                                <x-flux::icon name="{{ $sortDirection === 'asc' ? 'arrow-up' : 'arrow-down' }}" class="h-3 w-3 inline" />
                            @endif
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            VIP Início
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            VIP Fim
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer" wire:click="sortBy('created_at')">
                            Cadastro
                            @if($sortBy === 'created_at')
                                <x-flux::icon name="{{ $sortDirection === 'asc' ? 'arrow-up' : 'arrow-down' }}" class="h-3 w-3 inline" />
                            @endif
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Ações
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-zinc-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @forelse($users as $user)
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                {{ $user->id }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-8 w-8">
                                        @if($user->userPhotos->count() > 0)
                                            <img class="h-8 w-8 rounded-full object-cover" src="{{ Storage::url($user->userPhotos->first()->photo_path) }}" alt="{{ $user->name }}">
                                        @else
                                            <div class="h-8 w-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                                                <x-flux::icon name="user" class="h-6 w-6 text-gray-500 dark:text-gray-400" />
                                            </div>
                                        @endif
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900 dark:text-white">
                                            {{ $user->name }}
                                        </div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400">
                                            <a href="/{{ $user->username }}" class="hover:underline text-indigo-600 dark:text-indigo-400" target="_blank">
                                                {{ $user->username }}
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                {{ $user->email }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                    {{ $user->role === 'admin' ? 'bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200' :
                                       ($user->role === 'vip' ? 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200' :
                                       'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200') }}">
                                    {{ ucfirst($user->role) }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                    {{ $user->active ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200' }}">
                                    {{ $user->active ? 'On' : 'Off' }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                @if($user->activeVipSubscription)
                                    <span class="text-green-600 dark:text-green-400 font-medium">
                                        {{ $user->activeVipSubscription->activated_at ? $user->activeVipSubscription->activated_at->format('d/m/Y H:i') : '-' }}
                                    </span>
                                @else
                                    <span class="text-gray-400">-</span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                @if($user->activeVipSubscription)
                                    <span class="text-red-600 dark:text-red-400 font-medium">
                                        {{ $user->activeVipSubscription->expires_at ? $user->activeVipSubscription->expires_at->format('d/m/Y H:i') : '-' }}
                                    </span>
                                    @if($user->activeVipSubscription->expires_at && $user->activeVipSubscription->expires_at->isPast())
                                        <span class="ml-1 text-xs text-red-500">(Expirado)</span>
                                    @elseif($user->activeVipSubscription->expires_at)
                                        <span class="ml-1 text-xs text-green-500">({{ $user->activeVipSubscription->expires_at->diffForHumans() }})</span>
                                    @endif
                                @else
                                    <span class="text-gray-400">-</span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                {{ $user->created_at->format('d/m/Y') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex justify-end space-x-2">
                                    <x-flux::button
                                        wire:click="$dispatch('openModal', ['edit', { userId: {{ $user->id }} }])"
                                        color="secondary"
                                        size="xs"
                                    >
                                        <x-flux::icon icon="pencil" class="w-4 h-4" />
                                    </x-flux::button>

                                    @if($user->id !== auth()->id())
                                        @if($user->active)
                                            <x-flux::button
                                                wire:click="$dispatch('openModal', ['delete', { userId: {{ $user->id }} }])"
                                                color="danger"
                                                size="xs"
                                                title="Desativar usuário"
                                            >
                                                <x-flux::icon icon="user-minus" class="w-4 h-4" />
                                            </x-flux::button>
                                        @else
                                            <x-flux::button
                                                wire:click="reactivateUser({{ $user->id }})"
                                                color="success"
                                                size="xs"
                                                title="Reativar usuário"
                                            >
                                                <x-flux::icon icon="user-plus" class="w-4 h-4" />
                                            </x-flux::button>
                                        @endif
                                    @endif
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="9" class="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400">
                                Nenhum usuário encontrado.
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Paginação -->
        <div class="mt-4">
            {{ $users->links() }}
        </div>
    </div>

    <!-- Modal de Criação/Edição -->
    <div
        x-data="{ open: false }"
        x-init="
            window.addEventListener('openModal', event => {
                if (event.detail[0] === 'edit') {
                    open = true;
                    $wire.edit(event.detail[1].userId);
                } else if (event.detail[0] === 'create') {
                    open = true;
                    $wire.createUser();
                }
            });
        "
    >
        <div
            x-show="open"
            x-cloak
            @keydown.escape.window="open = false"
            class="fixed inset-0 z-50 overflow-y-auto"
        >
            <!-- Backdrop -->
            <div
                class="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
                x-show="open"
                x-transition:enter="transition ease-out duration-300"
                x-transition:enter-start="opacity-0"
                x-transition:enter-end="opacity-100"
                x-transition:leave="transition ease-in duration-200"
                x-transition:leave-start="opacity-100"
                x-transition:leave-end="opacity-0"
                @click="open = false"
            ></div>

            <!-- Modal Content -->
            <div class="flex items-center justify-center min-h-screen p-4">
                <div
                    class="bg-white dark:bg-zinc-800 rounded-lg shadow-xl max-w-md w-full p-6 relative mx-auto"
                    x-show="open"
                    x-transition:enter="transition ease-out duration-300"
                    x-transition:enter-start="opacity-0 translate-y-4"
                    x-transition:enter-end="opacity-100 translate-y-0"
                    x-transition:leave="transition ease-in duration-200"
                    x-transition:leave-start="opacity-100 translate-y-0"
                    x-transition:leave-end="opacity-0 translate-y-4"
                    @click.away="open = false"
                >
                    <!-- Header -->
                    <div class="mb-4">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                            {{ $isEditing ? 'Editar Usuário' : 'Novo Usuário' }}
                        </h3>
                    </div>

                    <!-- Body -->
                    <form wire:submit.prevent="save" class="space-y-4" x-on:saved.window="open = false">
                        <div class="grid grid-cols-1 gap-4">
                            <div>
                                <x-flux::input
                                    wire:model.live="name"
                                    label="Nome"
                                    placeholder="Nome do usuário"
                                    required
                                />
                            </div>

                            <div>
                                <x-flux::input
                                    wire:model.live="email"
                                    label="Email"
                                    type="email"
                                    placeholder="Email do usuário"
                                    required
                                />
                            </div>

                            <div>
                                <x-flux::select
                                    wire:model.live="role"
                                    label="Papel"
                                    required
                                >
                                    <option value="visitante">Visitante</option>
                                    <option value="vip">VIP</option>
                                    <option value="admin">Administrador</option>
                                </x-flux::select>
                            </div>

                            <div>
                                <x-flux::checkbox
                                    wire:model.live="active"
                                    label="Usuário Ativo"
                                />
                            </div>
                        </div>

                        <!-- Footer -->
                        <div class="flex justify-end space-x-3 mt-6">
                            <button
                                type="button"
                                @click="open = false"
                                class="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-400"
                            >
                                Cancelar
                            </button>
                            <button
                                type="submit"
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                {{ $isEditing ? 'Salvar Alterações' : 'Criar Usuário' }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Confirmação de Exclusão -->
    <div
        x-data="{ open: false }"
        x-init="
            window.addEventListener('openModal', event => {
                if (event.detail[0] === 'delete') {
                    open = true;
                    $wire.confirmDelete(event.detail[1].userId);
                }
            });
        "
    >
        <div
            x-show="open"
            x-cloak
            @keydown.escape.window="open = false"
            class="fixed inset-0 z-50 overflow-y-auto"
        >
            <!-- Backdrop -->
            <div
                class="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
                x-show="open"
                x-transition:enter="transition ease-out duration-300"
                x-transition:enter-start="opacity-0"
                x-transition:enter-end="opacity-100"
                x-transition:leave="transition ease-in duration-200"
                x-transition:leave-start="opacity-100"
                x-transition:leave-end="opacity-0"
                @click="open = false"
            ></div>

            <!-- Modal Content -->
            <div class="flex items-center justify-center min-h-screen p-4">
                <div
                    class="bg-white dark:bg-zinc-800 rounded-lg shadow-xl max-w-md w-full p-6 relative mx-auto"
                    x-show="open"
                    x-transition:enter="transition ease-out duration-300"
                    x-transition:enter-start="opacity-0 translate-y-4"
                    x-transition:enter-end="opacity-100 translate-y-0"
                    x-transition:leave="transition ease-in duration-200"
                    x-transition:leave-start="opacity-100 translate-y-0"
                    x-transition:leave-end="opacity-0 translate-y-4"
                    @click.away="open = false"
                >
                    <!-- Header -->
                    <div class="mb-4">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                            Confirmar Desativação
                        </h3>
                    </div>

                    <!-- Body -->
                    <div class="mb-4">
                        <p class="text-gray-700 dark:text-gray-300">
                            Tem certeza que deseja desativar este usuário? O usuário não poderá mais fazer login, mas todos os dados serão preservados e o usuário pode ser reativado posteriormente.
                        </p>
                        <div class="mt-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md">
                            <p class="text-sm text-yellow-800 dark:text-yellow-200">
                                <strong>Nota:</strong> Posts, mensagens, transações e outros dados do usuário serão mantidos para preservar a integridade do sistema.
                            </p>
                        </div>
                    </div>

                    <!-- Footer -->
                    <div class="flex justify-end space-x-3 mt-6">
                        <button
                            type="button"
                            @click="open = false"
                            class="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-400"
                        >
                            Cancelar
                        </button>
                        <button
                            type="button"
                            @click="$wire.delete().then(() => { open = false })"
                            class="px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500"
                        >
                            Desativar
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
