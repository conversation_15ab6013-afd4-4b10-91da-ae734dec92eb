<div class="py-8">
    <h2 class="text-2xl font-bold mb-4 flex items-center gap-2">
        <x-flux:icon name="briefcase" class="w-6 h-6 text-primary" /> <PERSON><PERSON><PERSON><PERSON>
    </h2>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        @forelse($job_vacancies as $job)
            <div class="bg-white dark:bg-zinc-900 rounded-lg shadow p-6 flex flex-col justify-between border border-neutral-700 hover:shadow-lg transition">
                <div>
                    <div class="flex items-center gap-2 mb-2">
                        <x-flux:icon name="{{$job->category->icon ?? 'briefcase'}}" class="w-5 h-5 text-primary" />
                        <span class="text-xs font-semibold uppercase text-primary">{{$job->category->name}}</span>
                    </div>
                    <a href="{{route('job_vacancies.show', $job->slug)}}" class="text-xl font-bold hover:text-primary transition">{{$job->title}}</a>
                    <div class="mt-2 text-sm text-zinc-500 dark:text-zinc-300 line-clamp-3">{{$job->description}}</div>
                    <div class="mt-2 flex flex-wrap gap-2 text-xs">
                        <span class="bg-zinc-100 dark:bg-zinc-800 px-2 py-1 rounded">{{$job->contract_type}}</span>
                        <span class="bg-zinc-100 dark:bg-zinc-800 px-2 py-1 rounded">{{$job->work_mode}}</span>
                        <span class="bg-zinc-100 dark:bg-zinc-800 px-2 py-1 rounded">{{$job->experience_level}}</span>
                        @if($job->is_featured)
                            <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded font-bold">Destaque</span>
                        @endif
                    </div>
                </div>
                <div class="mt-4 flex items-center justify-between">
                    <span class="font-semibold text-primary">{{$job->formatted_salary}}</span>
                    <a href="{{route('job_vacancies.show', $job->slug)}}" class="flux:button flux:button-primary">Ver detalhes</a>
                </div>
            </div>
        @empty
            <div class="col-span-3 text-center text-zinc-500 py-12">Nenhuma vaga encontrada.</div>
        @endforelse
    </div>
    <div class="mt-8 p-4 bg-blue-50 dark:bg-blue-900 rounded flex items-center gap-4">
        <x-flux:icon name="star" class="w-8 h-8 text-yellow-400" />
        <div>
            <span class="font-bold text-primary">Usuários VIP têm prioridade nas candidaturas!</span>
            <p class="text-sm text-zinc-600 dark:text-zinc-300">Qualquer pessoa pode se candidatar, mas membros VIP têm análise prioritária e mais chances de destaque.</p>
        </div>
    </div>
</div>
