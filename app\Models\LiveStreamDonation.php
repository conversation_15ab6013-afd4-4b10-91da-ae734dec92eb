<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LiveStreamDonation extends Model
{
    use HasFactory;

    protected $fillable = [
        'live_stream_id',
        'user_id',
        'sender_id',
        'amount',
        'charm_type',
        'message',
        'status',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
    ];

    /**
     * Live stream que a doação pertence
     */
    public function liveStream()
    {
        return $this->belongsTo(LiveStream::class);
    }

    /**
     * Usuário que recebeu a doação
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Usuário que enviou a doação
     */
    public function sender()
    {
        return $this->belongsTo(User::class, 'sender_id');
    }

    /**
     * Verifica se a doação foi completada
     */
    public function isCompleted()
    {
        return $this->status === 'completed';
    }

    /**
     * Scope para doações completadas
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Obtém o nome do charm baseado no tipo
     */
    public function getCharmNameAttribute()
    {
        $charmTypes = [
            'flower' => 'Flor',
            'kiss' => 'Beijo',
            'drink' => 'Drink',
            'ticket' => 'Ingresso'
        ];

        return $charmTypes[$this->charm_type] ?? $this->charm_type;
    }
}
