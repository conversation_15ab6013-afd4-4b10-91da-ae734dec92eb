<?php

namespace App\Services;

use App\Models\Order;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class WalletCreditService
{
    /**
     * Process wallet credit from an order
     */
    public function processWalletCredit(Order $order): bool
    {
        try {
            // Validate order before processing
            $validationErrors = $this->validateOrderForWalletCredit($order);
            if (!empty($validationErrors)) {
                Log::warning('Order validation failed for wallet credit processing', [
                    'order_id' => $order->id,
                    'errors' => $validationErrors
                ]);
                return false;
            }

            DB::beginTransaction();

            // Get wallet credit products from the order
            $walletCreditProducts = $this->getWalletCreditProductsFromOrder($order);

            if (empty($walletCreditProducts)) {
                Log::info('No wallet credit products found in order', ['order_id' => $order->id]);
                DB::commit();
                return true; // Not an error, just no wallet credit products
            }

            $user = $order->user;
            $totalCreditAmount = collect($walletCreditProducts)->sum('total_credit');

            // Validate total amount
            if ($totalCreditAmount <= 0) {
                Log::warning('Invalid total credit amount', [
                    'order_id' => $order->id,
                    'total_credit_amount' => $totalCreditAmount
                ]);
                DB::rollBack();
                return false;
            }

            Log::info('Processing wallet credit', [
                'order_id' => $order->id,
                'user_id' => $user->id,
                'total_credit_amount' => $totalCreditAmount,
                'wallet_credit_products_count' => count($walletCreditProducts)
            ]);

            // Add credit to user's wallet
            $this->addCreditToWallet($user, $totalCreditAmount, $order, $walletCreditProducts);

            DB::commit();

            Log::info('Wallet credit processed successfully', [
                'order_id' => $order->id,
                'user_id' => $user->id,
                'total_credit_added' => $totalCreditAmount
            ]);

            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to process wallet credit', [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Get wallet credit products from order
     */
    protected function getWalletCreditProductsFromOrder(Order $order): array
    {
        $walletCreditProducts = [];

        foreach ($order->items as $item) {
            $product = $item->product;

            if ($product && $product->addsWalletCredit() && $product->hasValidWalletCreditConfig()) {
                $creditPerUnit = $product->getWalletCreditAmount();
                $totalCredit = $creditPerUnit * $item->quantity;

                $walletCreditProducts[] = [
                    'id' => $product->id,
                    'name' => $product->name,
                    'price' => $item->price,
                    'quantity' => $item->quantity,
                    'credit_per_unit' => $creditPerUnit,
                    'total_credit' => $totalCredit
                ];
            }
        }

        return $walletCreditProducts;
    }

    /**
     * Add credit to user's wallet
     */
    protected function addCreditToWallet(User $user, float $totalCreditAmount, Order $order, array $walletCreditProducts): void
    {
        $wallet = $user->wallet;

        if (!$wallet) {
            throw new \Exception('Wallet not found for user');
        }

        // Add funds to wallet
        $transaction = $wallet->addFunds(
            $totalCreditAmount,
            'product_credit',
            'Crédito de produtos - Pedido #' . $order->id,
            $order->id,
            Order::class
        );

        Log::info('Credit added to wallet', [
            'user_id' => $user->id,
            'wallet_id' => $wallet->id,
            'transaction_id' => $transaction->id,
            'amount_added' => $totalCreditAmount,
            'new_balance' => $wallet->balance,
            'products' => $walletCreditProducts
        ]);
    }

    /**
     * Check if order contains wallet credit products
     */
    public function orderContainsWalletCreditProducts(Order $order): bool
    {
        foreach ($order->items as $item) {
            if ($item->product && $item->product->addsWalletCredit() && $item->product->hasValidWalletCreditConfig()) {
                return true;
            }
        }
        return false;
    }

    /**
     * Validate wallet credit configuration for a product
     */
    public function validateWalletCreditProduct($product): array
    {
        $errors = [];

        if (!$product->addsWalletCredit()) {
            return $errors;
        }

        if (!$product->hasValidWalletCreditConfig()) {
            $errors[] = 'Produto configurado para adicionar crédito mas sem valor válido definido.';
        }

        if ($product->wallet_credit_amount <= 0) {
            $errors[] = 'O valor do crédito deve ser maior que zero.';
        }

        if ($product->wallet_credit_amount > 10000) {
            $errors[] = 'O valor do crédito não pode ser superior a R$ 10.000,00.';
        }

        return $errors;
    }

    /**
     * Validate order before processing wallet credit
     */
    public function validateOrderForWalletCredit(Order $order): array
    {
        $errors = [];

        if (!$order->isPaid()) {
            $errors[] = 'Pedido deve estar pago para processar crédito de carteira.';
        }

        if (!$order->user) {
            $errors[] = 'Usuário não encontrado para o pedido.';
        }

        if ($order->user && !$order->user->wallet) {
            $errors[] = 'Carteira não encontrada para o usuário.';
        }

        // Verificar se já foi processado
        if ($this->hasWalletCreditBeenProcessed($order)) {
            $errors[] = 'Crédito de carteira já foi processado para este pedido.';
        }

        return $errors;
    }

    /**
     * Check if wallet credit has already been processed for an order
     */
    protected function hasWalletCreditBeenProcessed(Order $order): bool
    {
        if (!$order->user || !$order->user->wallet) {
            return false;
        }

        return $order->user->wallet->transactions()
            ->where('type', 'product_credit')
            ->where('reference_id', $order->id)
            ->where('reference_type', Order::class)
            ->exists();
    }
}
