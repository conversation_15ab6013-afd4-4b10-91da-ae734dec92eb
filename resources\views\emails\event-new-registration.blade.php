<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nova Inscrição - {{ $event->name }}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #E60073;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #E60073;
            margin-bottom: 10px;
        }
        .title {
            color: #333;
            font-size: 28px;
            margin: 0;
        }
        .subtitle {
            color: #666;
            font-size: 16px;
            margin: 10px 0 0 0;
        }
        .alert {
            background: linear-gradient(135deg, #E60073, #00FFF7);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 25px 0;
            text-align: center;
        }
        .event-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 25px 0;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        .info-row:last-child {
            border-bottom: none;
        }
        .info-label {
            font-weight: bold;
            color: #666;
        }
        .info-value {
            color: #333;
        }
        .user-info {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 20px;
            margin: 25px 0;
        }
        .user-info h3 {
            color: #1976d2;
            margin-top: 0;
        }
        .status-badge {
            display: inline-block;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-confirmed {
            background: #d4edda;
            color: #155724;
        }
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 14px;
        }
        .button {
            display: inline-block;
            background: linear-gradient(135deg, #E60073, #00FFF7);
            color: white;
            padding: 12px 25px;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            margin: 15px 0;
            transition: transform 0.2s;
        }
        .button:hover {
            transform: translateY(-2px);
        }
        @media (max-width: 600px) {
            .info-row {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🎯 Swing Curitiba Admin</div>
            <h1 class="title">Nova Inscrição!</h1>
            <p class="subtitle">Um novo participante se inscreveu em um evento</p>
        </div>

        <div class="alert">
            <h2 style="margin: 0;">📝 Nova inscrição confirmada</h2>
            <p style="margin: 10px 0 0 0;">Um usuário acabou de se inscrever e confirmar o pagamento para um evento.</p>
        </div>

        <div class="event-info">
            <h3>📅 Informações do Evento</h3>
            <div class="info-row">
                <span class="info-label">Nome do Evento:</span>
                <span class="info-value">{{ $event->name }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Data:</span>
                <span class="info-value">{{ $event->formatted_date }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Horário:</span>
                <span class="info-value">{{ $event->formatted_start_time }}</span>
            </div>
            @if($event->location)
            <div class="info-row">
                <span class="info-label">Local:</span>
                <span class="info-value">{{ $event->location }}</span>
            </div>
            @endif
            <div class="info-row">
                <span class="info-label">Preço:</span>
                <span class="info-value">R$ {{ number_format($event->price, 2, ',', '.') }}</span>
            </div>
        </div>

        <div class="user-info">
            <h3>👤 Informações do Participante</h3>
            <div class="info-row">
                <span class="info-label">Nome:</span>
                <span class="info-value">{{ $user->name }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Email:</span>
                <span class="info-value">{{ $user->email }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Username:</span>
                <span class="info-value">{{ '@' . $user->username }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Data da Inscrição:</span>
                <span class="info-value">{{ $attendee->created_at->format('d/m/Y H:i') }}</span>
            </div>
        </div>

        <div class="event-info">
            <h3>💳 Informações do Pagamento</h3>
            <div class="info-row">
                <span class="info-label">Status do Pagamento:</span>
                <span class="info-value">
                    @php
                        $statusClass = match($attendee->payment_status) {
                            'completed' => 'status-confirmed',
                            'pending' => 'status-pending',
                            'failed' => 'status-failed',
                            default => 'status-pending'
                        };
                        $statusText = match($attendee->payment_status) {
                            'completed' => 'Confirmado',
                            'pending' => 'Pendente',
                            'failed' => 'Falhou',
                            default => 'Desconhecido'
                        };
                    @endphp
                    <span class="status-badge {{ $statusClass }}">{{ $statusText }}</span>
                </span>
            </div>
            <div class="info-row">
                <span class="info-label">Valor Pago:</span>
                <span class="info-value">R$ {{ number_format($attendee->amount_paid, 2, ',', '.') }}</span>
            </div>
            @if($attendee->payment_method)
            <div class="info-row">
                <span class="info-label">Método de Pagamento:</span>
                <span class="info-value">{{ ucfirst($attendee->payment_method) }}</span>
            </div>
            @endif
            @if($attendee->ticket_code)
            <div class="info-row">
                <span class="info-label">Código do Ingresso:</span>
                <span class="info-value" style="font-family: monospace; font-weight: bold;">{{ $attendee->ticket_code }}</span>
            </div>
            @endif
        </div>

        <div style="text-align: center;">
            <a href="{{ url('/admin/eventos/' . $event->id . '/participantes') }}" class="button">
                Ver Lista de Participantes
            </a>
        </div>

        <p><strong>Ações recomendadas:</strong></p>
        <ul>
            <li>Verificar se há vagas suficientes no evento</li>
            <li>Confirmar se todas as informações estão corretas</li>
            <li>Acompanhar o status do pagamento se ainda estiver pendente</li>
            <li>Preparar materiais adicionais se necessário</li>
        </ul>

        <div class="footer">
            <p><strong>Swing Curitiba - Painel Administrativo</strong><br>
            Sistema de gerenciamento de eventos<br>
            <a href="{{ url('/admin') }}">Acessar Painel Admin</a></p>
            
            <p style="font-size: 12px; color: #999; margin-top: 15px;">
                Este é um email automático enviado para administradores.<br>
                Para configurar notificações, acesse o painel administrativo.
            </p>
        </div>
    </div>
</body>
</html>
