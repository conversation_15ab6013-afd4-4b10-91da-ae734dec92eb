<?php

namespace App\Livewire;

use App\Models\FollowRequest;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class FollowRequestsPage extends Component
{
    public $followRequests = [];

    public function mount()
    {
        $this->loadFollowRequests();
    }

    public function loadFollowRequests()
    {
        $this->followRequests = FollowRequest::where('receiver_id', Auth::id())
            ->where('status', 'pending')
            ->with('sender.userPhotos')
            ->orderBy('created_at', 'desc')
            ->get();
    }

    public function accept($requestId)
    {
        $request = FollowRequest::find($requestId);
        
        if ($request && $request->receiver_id === Auth::id()) {
            $request->update(['status' => 'accepted']);
            
            // Criar o relacionamento de seguidor
            Auth::user()->followers()->attach($request->sender_id);
            
            $this->loadFollowRequests();
            
            $this->dispatch('toast', [
                'type' => 'success',
                'message' => 'Solicitação aceita com sucesso!'
            ]);
        }
    }

    public function reject($requestId)
    {
        $request = FollowRequest::find($requestId);
        
        if ($request && $request->receiver_id === Auth::id()) {
            $request->update(['status' => 'rejected']);
            
            $this->loadFollowRequests();
            
            $this->dispatch('toast', [
                'type' => 'info',
                'message' => 'Solicitação rejeitada.'
            ]);
        }
    }

    public function render()
    {
        return view('livewire.follow-requests-page');
    }
}
