<div class="max-w-4xl mx-auto p-6">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-300 mb-2">Configurações do Sistema</h1>
        <p class="text-gray-400"><PERSON><PERSON><PERSON><PERSON> as configurações globais dos álbuns premium</p>
        
        <!-- System Stats -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
            <div class="bg-zinc-800 border border-zinc-600 rounded-lg p-4 text-center">
                <div class="text-lg font-bold text-gray-300">{{ $systemStats['total_settings'] }}</div>
                <div class="text-sm text-gray-400">Configurações</div>
            </div>
            <div class="bg-zinc-800 border border-zinc-600 rounded-lg p-4 text-center">
                <div class="text-lg font-bold text-gray-300">{{ $systemStats['cache_status'] }}</div>
                <div class="text-sm text-gray-400">Status do Cache</div>
            </div>
            <div class="bg-zinc-800 border border-zinc-600 rounded-lg p-4 text-center">
                <div class="text-lg font-bold text-gray-300">{{ $systemStats['last_updated'] }}</div>
                <div class="text-sm text-gray-400">Última Atualização</div>
            </div>
        </div>
    </div>

    <form wire:submit.prevent="saveSettings">
        <!-- Pricing Settings -->
        <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg class="mb-6"">
            <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.header">
                <h2 class="text-xl font-semibold text-gray-300">Configurações de Preços</h2>
            </div>
            <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.body">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <flux:label for="commission_rate">Taxa de Comissão (%)</flux:label>
                        <flux:input 
                            wire:model="commission_rate" 
                            id="commission_rate" 
                            type="number" 
                            step="0.1"
                            min="0"
                            max="50"
                            class="mt-1"
                        />
                        @error('commission_rate') <span class="text-red-400 text-sm">{{ $message }}</span> @enderror
                        <p class="text-gray-400 text-sm mt-1">Porcentagem que a plataforma retém de cada venda</p>
                    </div>

                    <div>
                        <flux:label for="min_price">Preço Mínimo (R$)</flux:label>
                        <flux:input 
                            wire:model="min_price" 
                            id="min_price" 
                            type="number" 
                            step="0.01"
                            min="1"
                            class="mt-1"
                        />
                        @error('min_price') <span class="text-red-400 text-sm">{{ $message }}</span> @enderror
                    </div>

                    <div>
                        <flux:label for="max_price">Preço Máximo (R$)</flux:label>
                        <flux:input 
                            wire:model="max_price" 
                            id="max_price" 
                            type="number" 
                            step="0.01"
                            min="10"
                            class="mt-1"
                        />
                        @error('max_price') <span class="text-red-400 text-sm">{{ $message }}</span> @enderror
                    </div>
                </div>
            </div>
        </div>

        <!-- File Settings -->
        <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg class="mb-6"">
            <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.header">
                <h2 class="text-xl font-semibold text-gray-300">Configurações de Arquivos</h2>
            </div>
            <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.body">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <flux:label for="max_files_per_album">Máximo de Arquivos por Álbum</flux:label>
                        <flux:input 
                            wire:model="max_files_per_album" 
                            id="max_files_per_album" 
                            type="number" 
                            min="1"
                            max="100"
                            class="mt-1"
                        />
                        @error('max_files_per_album') <span class="text-red-400 text-sm">{{ $message }}</span> @enderror
                    </div>

                    <div>
                        <flux:label for="max_file_size">Tamanho Máximo por Arquivo (MB)</flux:label>
                        <flux:input 
                            wire:model="max_file_size" 
                            id="max_file_size" 
                            type="number" 
                            min="1"
                            max="100"
                            class="mt-1"
                        />
                        @error('max_file_size') <span class="text-red-400 text-sm">{{ $message }}</span> @enderror
                    </div>
                </div>

                <div>
                    <flux:label for="allowed_file_types">Tipos de Arquivo Permitidos</flux:label>
                    <flux:input 
                        wire:model="allowed_file_types" 
                        id="allowed_file_types" 
                        placeholder="jpg,jpeg,png,gif,webp,mp4,mov,avi,webm"
                        class="mt-1"
                    />
                    @error('allowed_file_types') <span class="text-red-400 text-sm">{{ $message }}</span> @enderror
                    <p class="text-gray-400 text-sm mt-1">Extensões separadas por vírgula</p>
                </div>
            </div>
        </div>

        <!-- Moderation Settings -->
        <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg class="mb-6"">
            <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.header">
                <h2 class="text-xl font-semibold text-gray-300">Configurações de Moderação</h2>
            </div>
            <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.body">
                <div class="space-y-4">
                    <div class="flex items-center">
                        <input 
                            type="checkbox" 
                            wire:model="auto_approve" 
                            id="auto_approve"
                            class="rounded border-gray-600 text-purple-600 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-500 focus:ring-opacity-50"
                        />
                        <flux:label for="auto_approve" class="ml-2">Aprovação Automática</flux:label>
                    </div>
                    <p class="text-gray-400 text-sm">Se habilitado, álbuns serão publicados automaticamente sem moderação</p>

                    <div class="flex items-center">
                        <input 
                            type="checkbox" 
                            wire:model="require_verification" 
                            id="require_verification"
                            class="rounded border-gray-600 text-purple-600 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-500 focus:ring-opacity-50"
                        />
                        <flux:label for="require_verification" class="ml-2">Exigir Verificação de Email</flux:label>
                    </div>
                    <p class="text-gray-400 text-sm">Usuários devem ter email verificado para criar álbuns premium</p>
                </div>
            </div>
        </div>

        <!-- Notification Settings -->
        <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg class="mb-6"">
            <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.header">
                <h2 class="text-xl font-semibold text-gray-300">Configurações de Notificações</h2>
            </div>
            <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.body">
                <div class="space-y-4">
                    <div class="flex items-center">
                        <input 
                            type="checkbox" 
                            wire:model="notify_new_albums" 
                            id="notify_new_albums"
                            class="rounded border-gray-600 text-purple-600 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-500 focus:ring-opacity-50"
                        />
                        <flux:label for="notify_new_albums" class="ml-2">Notificar Novos Álbuns</flux:label>
                    </div>

                    <div class="flex items-center">
                        <input 
                            type="checkbox" 
                            wire:model="notify_sales" 
                            id="notify_sales"
                            class="rounded border-gray-600 text-purple-600 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-500 focus:ring-opacity-50"
                        />
                        <flux:label for="notify_sales" class="ml-2">Notificar Vendas</flux:label>
                    </div>

                    <div class="flex items-center">
                        <input 
                            type="checkbox" 
                            wire:model="notify_reviews" 
                            id="notify_reviews"
                            class="rounded border-gray-600 text-purple-600 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-500 focus:ring-opacity-50"
                        />
                        <flux:label for="notify_reviews" class="ml-2">Notificar Avaliações</flux:label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex justify-between items-center">
            <div class="space-x-2">
                <flux:button 
                    type="button"
                    wire:click="resetToDefaults"
                    variant="outline"
                    color="gray"
                >
                    Restaurar Padrões
                </flux:button>

                <flux:button 
                    type="button"
                    wire:click="clearCache"
                    variant="outline"
                    color="blue"
                >
                    Limpar Cache
                </flux:button>
            </div>

            <flux:button 
                type="submit"
                color="purple"
                :disabled="$isLoading"
            >
                @if($isLoading)
                    <flux:icon name="arrow-path" class="w-4 h-4 animate-spin mr-2" />
                    Salvando...
                @else
                    <flux:icon name="check" class="w-4 h-4 mr-2" />
                    Salvar Configurações
                @endif
            </flux:button>
        </div>
    </form>

    <!-- Help Section -->
    <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg class="mt-8"">
        <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.header">
            <h2 class="text-xl font-semibold text-gray-300">Ajuda</h2>
        </div>
        <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.body">
            <div class="space-y-4 text-sm text-gray-400">
                <div>
                    <h4 class="font-medium text-gray-300 mb-1">Taxa de Comissão</h4>
                    <p>Porcentagem que a plataforma retém de cada venda. O restante vai para o criador do álbum.</p>
                </div>

                <div>
                    <h4 class="font-medium text-gray-300 mb-1">Limites de Preço</h4>
                    <p>Define o range de preços que os criadores podem definir para seus álbuns.</p>
                </div>

                <div>
                    <h4 class="font-medium text-gray-300 mb-1">Configurações de Arquivo</h4>
                    <p>Controla quantos arquivos podem ser enviados por álbum e quais tipos são aceitos.</p>
                </div>

                <div>
                    <h4 class="font-medium text-gray-300 mb-1">Aprovação Automática</h4>
                    <p>Se desabilitada, todos os álbuns precisarão ser aprovados manualmente por um administrador.</p>
                </div>
            </div>
        </div>
    </div>
</div>
