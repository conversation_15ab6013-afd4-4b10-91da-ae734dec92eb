<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\User;
use App\Models\AlbumMedia;
use Illuminate\Support\Facades\Auth;

class MediaPreview extends Component
{
    public User $user;
    public $recentMedias = [];
    public $totalCount = 0;

    public function mount(User $user)
    {
        $this->user = $user;
        $this->loadRecentMedias();
    }

    public function loadRecentMedias()
    {
        $query = AlbumMedia::where('user_id', $this->user->id)
            ->with('album');

        // Se não é o próprio usuário, mostrar apenas mídias de álbuns públicos
        if (!Auth::check() || Auth::id() !== $this->user->id) {
            $query->whereHas('album', function($q) {
                $q->where('privacy', 'public');
            });
        }

        $this->totalCount = $query->count();
        $this->recentMedias = $query->ordered()->take(9)->get();
    }

    public function render()
    {
        return view('livewire.media-preview');
    }
}
