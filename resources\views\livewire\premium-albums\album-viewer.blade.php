<div class="max-w-7xl mx-auto p-6">
    <!-- Album Header -->
    <div class="mb-8">
        <div class="flex flex-col lg:flex-row gap-8">
            <!-- Album Cover -->
            <div class="lg:w-1/3">
                <div class="relative aspect-square bg-zinc-800 rounded-lg overflow-hidden">
                    @if($medias->count() > 0)
                        @php $firstMedia = $medias->first(); @endphp
                        @if($firstMedia->type === 'photo')
                            <img 
                                src="{{ route('premium-albums.media', [$album, $firstMedia->id]) }}" 
                                alt="{{ $album->name }}"
                                class="w-full h-full object-cover"
                            />
                        @else
                            <div class="w-full h-full flex items-center justify-center">
                                <flux:icon name="film" class="w-16 h-16 text-gray-400" />
                            </div>
                        @endif
                    @else
                        <div class="w-full h-full flex items-center justify-center">
                            <flux:icon name="photo" class="w-16 h-16 text-gray-400" />
                        </div>
                    @endif

                    <!-- Premium Badge -->
                    <div class="absolute top-4 left-4">
                        <span class="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-3 py-1 text-sm font-bold rounded-full">
                            PREMIUM
                        </span>
                    </div>

                    @if(!$canAccess)
                    <!-- Preview Overlay -->
                    <div class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                        <div class="text-center text-white">
                            <flux:icon name="lock-closed" class="w-12 h-12 mx-auto mb-2" />
                            <p class="text-sm">Preview Limitado</p>
                        </div>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Album Info -->
            <div class="lg:w-2/3">
                <div class="mb-4">
                    <h1 class="text-3xl font-bold text-gray-300 mb-2">{{ $album->name }}</h1>
                    
                    <!-- Creator Info -->
                    <div class="flex items-center mb-4">
                        <img 
                            src="{{ $album->user->avatar_url ?? '/default-avatar.png' }}" 
                            alt="{{ $album->user->name }}"
                            class="w-10 h-10 rounded-full mr-3"
                        />
                        <div>
                            <p class="text-gray-300 font-medium">{{ $album->user->name }}</p>
                            <p class="text-sm text-gray-400">@{{ $album->user->username }}</p>
                        </div>
                    </div>

                    <!-- Stats -->
                    <div class="flex items-center space-x-6 mb-4">
                        @if($album->rating > 0)
                        <div class="flex items-center">
                            <flux:icon name="star" class="w-5 h-5 text-yellow-400 mr-1" />
                            <span class="text-gray-300">{{ number_format($album->rating, 1) }}</span>
                            <span class="text-gray-400 ml-1">({{ $album->reviews_count }} avaliações)</span>
                        </div>
                        @endif
                        
                        <div class="flex items-center">
                            <flux:icon name="shopping-cart" class="w-5 h-5 text-gray-400 mr-1" />
                            <span class="text-gray-300">{{ $album->sales_count }} vendas</span>
                        </div>
                        
                        <div class="flex items-center">
                            <flux:icon name="photo" class="w-5 h-5 text-gray-400 mr-1" />
                            <span class="text-gray-300">{{ $medias->count() }} arquivos</span>
                        </div>
                    </div>

                    <!-- Description -->
                    @if($album->description)
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-gray-300 mb-2">Descrição</h3>
                        <p class="text-gray-400 leading-relaxed">{{ $album->description }}</p>
                    </div>
                    @endif

                    <!-- Tags -->
                    @if($album->tags)
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-gray-300 mb-2">Tags</h3>
                        <div class="flex flex-wrap gap-2">
                            @foreach(explode(',', $album->tags) as $tag)
                            <span class="bg-zinc-700 text-gray-300 px-3 py-1 text-sm rounded-full">
                                #{{ trim($tag) }}
                            </span>
                            @endforeach
                        </div>
                    </div>
                    @endif
                </div>

                <!-- Purchase Section -->
                @if(!$isPurchased && $album->user_id !== auth()->id())
                <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg bg-gradient-to-r from-purple-900 to-pink-900 border-purple-600">
                    <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg p-6 text-center">
                        <div class="text-3xl font-bold text-white mb-2">
                            R$ {{ number_format($album->price, 2, ',', '.') }}
                        </div>
                        <p class="text-purple-200 mb-4">
                            Acesso completo a {{ $album->medias()->count() }} arquivos exclusivos
                        </p>
                        
                        @if(!$canAccess)
                        <p class="text-sm text-purple-200 mb-4">
                            Você está vendo apenas {{ $album->preview_limit }} arquivos de preview
                        </p>
                        @endif

                        <flux:button 
                            wire:click="openPurchaseModal"
                            color="white"
                            size="lg"
                            class="w-full"
                        >
                            <flux:icon name="shopping-cart" class="w-5 h-5 mr-2" />
                            Comprar Álbum
                        </flux:button>
                    </div>
                </div>
                @elseif($isPurchased)
                <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg bg-green-900 border-green-600">
                    <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg p-6 text-center">
                        <flux:icon name="check-circle" class="w-8 h-8 text-green-400 mx-auto mb-2" />
                        <p class="text-green-200 mb-2">Você possui este álbum!</p>
                        <p class="text-sm text-green-300">Acesso completo liberado</p>
                        
                        @if($canReview)
                        <flux:button 
                            wire:click="openReviewModal"
                            variant="outline"
                            color="green"
                            size="sm"
                            class="mt-3"
                        >
                            Avaliar Álbum
                        </flux:button>
                        @endif
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Media Gallery -->
    <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg mb-8">
        <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 px-6 py-4 border-b border-zinc-200 dark:border-zinc-700">
            <h2 class="text-xl font-semibold text-gray-300">
                @if($canAccess)
                    Galeria Completa ({{ $medias->count() }} arquivos)
                @else
                    Preview ({{ $medias->count() }} de {{ $album->medias()->count() }} arquivos)
                @endif
            </h2>
        </div>
        
        <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.body">
            @if($medias->count() > 0)
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                @foreach($medias as $media)
                <div class="relative aspect-square bg-zinc-800 rounded-lg overflow-hidden group cursor-pointer">
                    @if($media->type === 'photo')
                        <img 
                            src="{{ route('premium-albums.media', [$album, $media->id]) }}" 
                            alt="Mídia do álbum"
                            class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                            onclick="openLightbox('{{ route('premium-albums.media', [$album, $media->id]) }}')"
                        />
                    @else
                        <div class="w-full h-full flex items-center justify-center bg-zinc-700">
                            <flux:icon name="film" class="w-8 h-8 text-gray-400" />
                        </div>
                        <div class="absolute bottom-2 left-2">
                            <span class="bg-black bg-opacity-75 text-white px-2 py-1 text-xs rounded">
                                VIDEO
                            </span>
                        </div>
                    @endif
                    
                    <!-- Hover Overlay -->
                    <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
                        <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <flux:icon name="magnifying-glass-plus" class="w-6 h-6 text-white" />
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
            @else
            <div class="text-center py-8">
                <flux:icon name="photo" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p class="text-gray-400">Nenhuma mídia disponível</p>
            </div>
            @endif
        </div>
    </div>

    <!-- Reviews Section -->
    <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg">
        <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 px-6 py-4 border-b border-zinc-200 dark:border-zinc-700">
            <div class="flex justify-between items-center">
                <h2 class="text-xl font-semibold text-gray-300">
                    Avaliações ({{ $reviews->total() }})
                </h2>
                
                @if($canReview)
                <flux:button 
                    wire:click="openReviewModal"
                    color="purple"
                    size="sm"
                >
                    Avaliar
                </flux:button>
                @endif
            </div>
        </div>
        
        <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.body">
            @if($reviews->count() > 0)
            <div class="space-y-6">
                @foreach($reviews as $review)
                <div class="border-b border-gray-700 pb-4 last:border-b-0">
                    <div class="flex items-start space-x-3">
                        <img 
                            src="{{ $review->user->avatar_url ?? '/default-avatar.png' }}" 
                            alt="{{ $review->user->name }}"
                            class="w-10 h-10 rounded-full"
                        />
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-1">
                                <span class="font-medium text-gray-300">{{ $review->user->name }}</span>
                                <div class="flex items-center">
                                    @for($i = 1; $i <= 5; $i++)
                                    <flux:icon 
                                        name="star" 
                                        class="w-4 h-4 {{ $i <= $review->rating ? 'text-yellow-400' : 'text-gray-600' }}" 
                                    />
                                    @endfor
                                </div>
                                @if($review->is_verified_purchase)
                                <span class="bg-green-600 text-white px-2 py-1 text-xs rounded">Compra Verificada</span>
                                @endif
                            </div>
                            <p class="text-gray-400 text-sm mb-1">{{ $review->created_at->diffForHumans() }}</p>
                            <p class="text-gray-300">{{ $review->comment }}</p>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>

            <div class="mt-6">
                {{ $reviews->links() }}
            </div>
            @else
            <div class="text-center py-8">
                <flux:icon name="chat-bubble-left-right" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p class="text-gray-400">Nenhuma avaliação ainda</p>
                @if($canReview)
                <p class="text-sm text-gray-500 mt-2">Seja o primeiro a avaliar este álbum!</p>
                @endif
            </div>
            @endif
        </div>
    </div>

    <!-- Purchase Modal -->
    @if($showPurchaseModal)
    <flux:modal wire:model="showPurchaseModal">
        <flux:modal.header>
            <h2 class="text-lg font-semibold text-gray-300">Comprar Álbum</h2>
        </flux:modal.header>
        
        <flux:modal.body>
            <div class="text-center mb-6">
                <h3 class="text-xl font-bold text-gray-300 mb-2">{{ $album->name }}</h3>
                <div class="text-3xl font-bold text-purple-400 mb-4">
                    R$ {{ number_format($album->price, 2, ',', '.') }}
                </div>
            </div>

            <div class="space-y-4">
                <div>
                    <flux:label>Método de Pagamento</flux:label>
                    <div class="mt-2 space-y-2">
                        <label class="flex items-center">
                            <input type="radio" wire:model="paymentMethod" value="wallet" class="mr-2">
                            <span class="text-gray-300">Carteira (Saldo: R$ {{ number_format($userWalletBalance, 2, ',', '.') }})</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" wire:model="paymentMethod" value="stripe" class="mr-2">
                            <span class="text-gray-300">Cartão de Crédito</span>
                        </label>
                    </div>
                </div>

                @if($paymentMethod === 'wallet' && $userWalletBalance < $album->price)
                <div class="bg-red-900 border border-red-600 rounded-lg p-3">
                    <p class="text-red-200 text-sm">Saldo insuficiente na carteira.</p>
                </div>
                @endif
            </div>
        </flux:modal.body>
        
        <flux:modal.footer>
            <flux:button wire:click="closePurchaseModal" variant="outline">
                Cancelar
            </flux:button>
            <flux:button 
                wire:click="purchaseAlbum" 
                color="purple"
                :disabled="$isProcessing || ($paymentMethod === 'wallet' && $userWalletBalance < $album->price)"
            >
                @if($isProcessing)
                    <flux:icon name="arrow-path" class="w-4 h-4 animate-spin mr-2" />
                    Processando...
                @else
                    Confirmar Compra
                @endif
            </flux:button>
        </flux:modal.footer>
    </flux:modal>
    @endif

    <!-- Review Modal -->
    @if($showReviewModal)
    <flux:modal wire:model="showReviewModal">
        <flux:modal.header>
            <h2 class="text-lg font-semibold text-gray-300">Avaliar Álbum</h2>
        </flux:modal.header>
        
        <flux:modal.body>
            <form wire:submit.prevent="submitReview">
                <div class="space-y-4">
                    <div>
                        <flux:label>Avaliação</flux:label>
                        <div class="flex items-center space-x-1 mt-2">
                            @for($i = 1; $i <= 5; $i++)
                            <button 
                                type="button"
                                wire:click="$set('reviewRating', {{ $i }})"
                                class="focus:outline-none"
                            >
                                <flux:icon 
                                    name="star" 
                                    class="w-6 h-6 {{ $i <= $reviewRating ? 'text-yellow-400' : 'text-gray-600' }} hover:text-yellow-400 transition-colors" 
                                />
                            </button>
                            @endfor
                        </div>
                        @error('reviewRating') <span class="text-red-400 text-sm">{{ $message }}</span> @enderror
                    </div>

                    <div>
                        <flux:label for="reviewComment">Comentário</flux:label>
                        <flux:textarea 
                            wire:model="reviewComment" 
                            id="reviewComment" 
                            placeholder="Compartilhe sua opinião sobre este álbum..."
                            rows="4"
                            class="mt-1"
                        />
                        @error('reviewComment') <span class="text-red-400 text-sm">{{ $message }}</span> @enderror
                    </div>
                </div>
            </form>
        </flux:modal.body>
        
        <flux:modal.footer>
            <flux:button wire:click="closeReviewModal" variant="outline">
                Cancelar
            </flux:button>
            <flux:button wire:click="submitReview" color="purple">
                Enviar Avaliação
            </flux:button>
        </flux:modal.footer>
    </flux:modal>
    @endif
</div>

@push('scripts')
<script>
function openLightbox(src) {
    // Implementar lightbox para visualização de imagens
    window.open(src, '_blank');
}
</script>
@endpush
