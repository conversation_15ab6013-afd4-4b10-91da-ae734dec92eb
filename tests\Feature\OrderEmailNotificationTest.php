<?php

namespace Tests\Feature;

use App\Mail\OrderStatusChanged;
use App\Mail\OrderPendingReminder;
use App\Models\Order;
use App\Models\User;
use App\Models\Product;
use App\Models\OrderItem;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Tests\TestCase;

class OrderEmailNotificationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Mail::fake();
    }

    /** @test */
    public function it_sends_email_when_order_status_changes()
    {
        // Arrange
        $user = User::factory()->create();
        $product = Product::factory()->create(['price' => 100.00]);
        
        $order = Order::create([
            'user_id' => $user->id,
            'total' => 100.00,
            'status' => Order::STATUS_PENDING,
        ]);

        OrderItem::create([
            'order_id' => $order->id,
            'product_id' => $product->id,
            'quantity' => 1,
            'price' => 100.00,
        ]);

        // Act
        $order->update(['status' => Order::STATUS_PROCESSING]);

        // Assert
        Mail::assertQueued(OrderStatusChanged::class, function ($mail) use ($user) {
            return $mail->hasTo($user->email);
        });
    }

    /** @test */
    public function it_sends_admin_copy_when_order_status_changes()
    {
        // Arrange
        $user = User::factory()->create();
        $admin = User::factory()->create(['role' => 'administrador']);
        $product = Product::factory()->create(['price' => 100.00]);
        
        $order = Order::create([
            'user_id' => $user->id,
            'total' => 100.00,
            'status' => Order::STATUS_PENDING,
        ]);

        OrderItem::create([
            'order_id' => $order->id,
            'product_id' => $product->id,
            'quantity' => 1,
            'price' => 100.00,
        ]);

        // Act
        $order->update(['status' => Order::STATUS_PROCESSING]);

        // Assert - Verifica se email foi enviado para admin
        Mail::assertQueued(\App\Mail\OrderStatusChangedAdminCopy::class);
    }

    /** @test */
    public function pending_orders_command_sends_notifications()
    {
        // Arrange
        $user = User::factory()->create();
        $admin = User::factory()->create(['role' => 'administrador']);
        $product = Product::factory()->create(['price' => 100.00]);
        
        // Criar pedido pendente há mais de 7 dias
        $order = Order::create([
            'user_id' => $user->id,
            'total' => 100.00,
            'status' => Order::STATUS_PENDING,
            'created_at' => now()->subDays(8),
        ]);

        OrderItem::create([
            'order_id' => $order->id,
            'product_id' => $product->id,
            'quantity' => 1,
            'price' => 100.00,
        ]);

        // Act
        $this->artisan('orders:check-pending', ['--days' => 7])
             ->assertExitCode(0);

        // Assert
        Mail::assertQueued(OrderPendingReminder::class, function ($mail) use ($user) {
            return $mail->hasTo($user->email);
        });
    }

    /** @test */
    public function it_does_not_send_email_for_non_status_changes()
    {
        // Arrange
        $user = User::factory()->create();
        $product = Product::factory()->create(['price' => 100.00]);
        
        $order = Order::create([
            'user_id' => $user->id,
            'total' => 100.00,
            'status' => Order::STATUS_PENDING,
        ]);

        OrderItem::create([
            'order_id' => $order->id,
            'product_id' => $product->id,
            'quantity' => 1,
            'price' => 100.00,
        ]);

        // Act - Atualizar campo que não é status
        $order->update(['notes' => 'Observação teste']);

        // Assert
        Mail::assertNotQueued(OrderStatusChanged::class);
    }

    /** @test */
    public function pending_command_respects_days_parameter()
    {
        // Arrange
        $user = User::factory()->create();
        $product = Product::factory()->create(['price' => 100.00]);
        
        // Criar pedido pendente há 5 dias (menos que 7)
        $order = Order::create([
            'user_id' => $user->id,
            'total' => 100.00,
            'status' => Order::STATUS_PENDING,
            'created_at' => now()->subDays(5),
        ]);

        OrderItem::create([
            'order_id' => $order->id,
            'product_id' => $product->id,
            'quantity' => 1,
            'price' => 100.00,
        ]);

        // Act
        $this->artisan('orders:check-pending', ['--days' => 7])
             ->assertExitCode(0);

        // Assert - Não deve enviar email pois não atingiu 7 dias
        Mail::assertNotQueued(OrderPendingReminder::class);
    }
}
