<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lembrete: Pedido Pendente</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f4f4f4;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #fff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
            color: white;
            padding: 30px;
            text-align: center;
            border-radius: 10px 10px 0 0;
            margin: -30px -30px 30px -30px;
        }
        .reminder-alert {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
            font-size: 18px;
            font-weight: bold;
        }
        .info-box {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }
        .info-box h3 {
            margin-top: 0;
            color: #E60073;
        }
        .pending-badge {
            background-color: #ffc107;
            color: #333;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
            display: inline-block;
            margin: 10px 0;
        }
        .order-items {
            background-color: #f8f9fa;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .item {
            border-bottom: 1px solid #dee2e6;
            padding: 10px 0;
        }
        .item:last-child {
            border-bottom: none;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 12px;
        }
        .btn {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(135deg, #E60073, #00FFF7);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            margin: 20px 10px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .btn-warning {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
        }
        .urgency-box {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 15px 0;
        }
        .payment-info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .next-steps {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⏰ Lembrete de Pedido Pendente</h1>
            <p>Olá, {{ $user->name }}!</p>
        </div>

        <div class="reminder-alert">
            ⚠️ Seu pedido está pendente há {{ $daysPending }} {{ $daysPending == 1 ? 'dia' : 'dias' }}
        </div>

        <div class="info-box">
            <h3>📋 Informações do Pedido</h3>
            <p><strong>Número do Pedido:</strong> #{{ $order->id }}</p>
            <p><strong>Data do Pedido:</strong> {{ $order->created_at->format('d/m/Y H:i:s') }}</p>
            <p><strong>Valor Total:</strong> R$ {{ number_format($order->total, 2, ',', '.') }}</p>
            <p><strong>Status:</strong> <span class="pending-badge">Pendente</span></p>
        </div>

        <div class="urgency-box">
            <h3>🚨 Ação Necessária</h3>
            <p>Seu pedido está aguardando há <strong>{{ $daysPending }} {{ $daysPending == 1 ? 'dia' : 'dias' }}</strong> e precisa de atenção.</p>
            @if($daysPending >= 7)
                <p><strong style="color: #dc3545;">⚠️ URGENTE:</strong> Pedidos pendentes por mais de 7 dias podem ser cancelados automaticamente.</p>
            @endif
        </div>

        @if($order->items && $order->items->count() > 0)
        <div class="info-box">
            <h3>🛍️ Itens do Pedido</h3>
            <div class="order-items">
                @foreach($order->items as $item)
                <div class="item">
                    <strong>{{ $item->product->name ?? 'Produto' }}</strong><br>
                    Quantidade: {{ $item->quantity }} | 
                    Preço: R$ {{ number_format($item->price, 2, ',', '.') }}
                </div>
                @endforeach
            </div>
        </div>
        @endif

        <div class="payment-info">
            <h3>💳 Informações de Pagamento</h3>
            <p>Possíveis motivos para o pedido estar pendente:</p>
            <ul>
                <li>Pagamento não processado ou rejeitado</li>
                <li>Verificação de segurança em andamento</li>
                <li>Problemas com o método de pagamento</li>
                <li>Aguardando confirmação manual</li>
            </ul>
        </div>

        <div class="next-steps">
            <h3>✅ Próximos Passos</h3>
            <p><strong>O que você pode fazer:</strong></p>
            <ul>
                <li>Verificar o status do pagamento</li>
                <li>Entrar em contato com nosso suporte</li>
                <li>Tentar efetuar o pagamento novamente</li>
                <li>Verificar se há problemas com seu cartão/conta</li>
            </ul>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <a href="{{ route('shop.user.orders') }}" class="btn">Ver Detalhes do Pedido</a>
            <a href="{{ route('support.index') }}" class="btn btn-warning">Entrar em Contato</a>
        </div>

        @if($daysPending >= 5)
        <div style="background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px; padding: 15px; margin: 20px 0; color: #721c24;">
            <h3 style="margin-top: 0;">⚠️ Aviso Importante</h3>
            <p>Seu pedido está pendente há {{ $daysPending }} dias. Para evitar o cancelamento automático, recomendamos que entre em contato conosco o mais breve possível.</p>
        </div>
        @endif

        <div class="info-box">
            <h3>📞 Precisa de Ajuda?</h3>
            <p>Nossa equipe de suporte está pronta para ajudar:</p>
            <p>• <strong>Email:</strong> <EMAIL></p>
            <p>• <strong>Sistema de Tickets:</strong> Acesse sua conta e abra um ticket</p>
            <p>• <strong>Horário de Atendimento:</strong> Segunda a Sexta, 9h às 18h</p>
        </div>

        <div class="footer">
            <p>Obrigado por escolher o {{ config('app.name') }}!</p>
            <p>Este é um lembrete automático para pedidos pendentes.</p>
            <p>Data: {{ now()->format('d/m/Y H:i:s') }}</p>
        </div>
    </div>
</body>
</html>
