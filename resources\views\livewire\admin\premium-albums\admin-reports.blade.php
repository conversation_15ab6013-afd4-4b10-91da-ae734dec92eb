<div class="max-w-7xl mx-auto p-6">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-300 mb-2">Relatórios Administrativos</h1>
        <p class="text-gray-400">Análise completa do sistema de álbuns premium</p>
    </div>

    <!-- Period Filter -->
    <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg class="mb-6"">
        <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.body">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <flux:label for="period">Período</flux:label>
                    <flux:select wire:model.live="period" id="period" class="mt-1">
                        <option value="today">Hoje</option>
                        <option value="week">Esta Semana</option>
                        <option value="month">Este Mês</option>
                        <option value="year">Este Ano</option>
                        <option value="all">Todos os Tempos</option>
                    </flux:select>
                </div>

                @if($period === 'custom')
                <div>
                    <flux:label for="startDate">Data Inicial</flux:label>
                    <flux:input wire:model.live="startDate" type="date" id="startDate" class="mt-1" />
                </div>

                <div>
                    <flux:label for="endDate">Data Final</flux:label>
                    <flux:input wire:model.live="endDate" type="date" id="endDate" class="mt-1" />
                </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Tabs -->
    <div class="mb-6">
        <nav class="flex space-x-8">
            <button 
                wire:click="setActiveTab('overview')"
                class="py-2 px-1 border-b-2 font-medium text-sm {{ $activeTab === 'overview' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300' }}"
            >
                Visão Geral
            </button>
            <button 
                wire:click="setActiveTab('sales')"
                class="py-2 px-1 border-b-2 font-medium text-sm {{ $activeTab === 'sales' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300' }}"
            >
                Vendas
            </button>
            <button 
                wire:click="setActiveTab('creators')"
                class="py-2 px-1 border-b-2 font-medium text-sm {{ $activeTab === 'creators' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300' }}"
            >
                Criadores
            </button>
            <button 
                wire:click="setActiveTab('albums')"
                class="py-2 px-1 border-b-2 font-medium text-sm {{ $activeTab === 'albums' ? 'border-purple-500 text-purple-400' : 'border-transparent text-gray-400 hover:text-gray-300' }}"
            >
                Álbuns
            </button>
        </nav>
    </div>

    <!-- Overview Tab -->
    @if($activeTab === 'overview' && isset($overviewStats))
    <div class="space-y-6">
        <!-- Stats Cards -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="bg-zinc-800 border border-zinc-600 rounded-lg p-4">
                <div class="text-2xl font-bold text-purple-400">{{ $overviewStats['total_albums'] }}</div>
                <div class="text-sm text-gray-400">Total de Álbuns</div>
            </div>
            <div class="bg-yellow-900 border border-yellow-600 rounded-lg p-4">
                <div class="text-2xl font-bold text-yellow-400">{{ $overviewStats['pending_albums'] }}</div>
                <div class="text-sm text-yellow-200">Pendentes</div>
            </div>
            <div class="bg-green-900 border border-green-600 rounded-lg p-4">
                <div class="text-2xl font-bold text-green-400">{{ $overviewStats['total_sales'] }}</div>
                <div class="text-sm text-green-200">Vendas</div>
            </div>
            <div class="bg-blue-900 border border-blue-600 rounded-lg p-4">
                <div class="text-2xl font-bold text-blue-400">{{ $overviewStats['active_creators'] }}</div>
                <div class="text-sm text-blue-200">Criadores Ativos</div>
            </div>
        </div>

        <!-- Revenue Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg">
                <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.header">
                    <h3 class="text-lg font-semibold text-gray-300">Receita Total</h3>
                </div>
                <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.body">
                    <div class="text-3xl font-bold text-green-400 mb-2">
                        R$ {{ number_format($overviewStats['total_revenue'], 2, ',', '.') }}
                    </div>
                    <p class="text-gray-400 text-sm">Receita bruta do período</p>
                </div>
            </div>

            <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg">
                <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.header">
                    <h3 class="text-lg font-semibold text-gray-300">Comissão da Plataforma</h3>
                </div>
                <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.body">
                    <div class="text-3xl font-bold text-purple-400 mb-2">
                        R$ {{ number_format($overviewStats['platform_commission'], 2, ',', '.') }}
                    </div>
                    <p class="text-gray-400 text-sm">Comissões arrecadadas</p>
                </div>
            </div>

            <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg">
                <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.header">
                    <h3 class="text-lg font-semibold text-gray-300">Preço Médio</h3>
                </div>
                <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.body">
                    <div class="text-3xl font-bold text-cyan-400 mb-2">
                        R$ {{ number_format($overviewStats['avg_album_price'], 2, ',', '.') }}
                    </div>
                    <p class="text-gray-400 text-sm">Preço médio dos álbuns</p>
                </div>
            </div>
        </div>

        <!-- Top Creator -->
        @if($overviewStats['top_creator'])
        <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg">
            <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.header">
                <h3 class="text-lg font-semibold text-gray-300">Top Criador do Período</h3>
            </div>
            <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.body">
                <div class="flex items-center space-x-4">
                    <img 
                        src="{{ $overviewStats['top_creator']->avatar_url ?? '/default-avatar.png' }}" 
                        alt="{{ $overviewStats['top_creator']->name }}"
                        class="w-16 h-16 rounded-full"
                    />
                    <div>
                        <h4 class="text-xl font-semibold text-gray-300">{{ $overviewStats['top_creator']->name }}</h4>
                        <p class="text-gray-400">@{{ $overviewStats['top_creator']->username }}</p>
                        <p class="text-green-400 font-medium">
                            R$ {{ number_format($overviewStats['top_creator']->total_revenue ?? 0, 2, ',', '.') }} em vendas
                        </p>
                    </div>
                </div>
            </div>
        </div>
        @endif
    </div>
    @endif

    <!-- Sales Tab -->
    @if($activeTab === 'sales' && isset($salesData))
    <div class="space-y-6">
        <!-- Payment Methods -->
        <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg">
            <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.header">
                <h3 class="text-lg font-semibold text-gray-300">Métodos de Pagamento</h3>
            </div>
            <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.body">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    @foreach($salesData['payment_methods'] as $method)
                    <div class="bg-zinc-800 rounded-lg p-4">
                        <div class="flex justify-between items-center">
                            <div>
                                <h4 class="font-medium text-gray-300">
                                    {{ $method->payment_method === 'wallet' ? 'Carteira' : 'Cartão de Crédito' }}
                                </h4>
                                <p class="text-sm text-gray-400">{{ $method->count }} transações</p>
                            </div>
                            <div class="text-right">
                                <div class="text-lg font-bold text-green-400">
                                    R$ {{ number_format($method->revenue, 2, ',', '.') }}
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Recent Sales -->
        <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg">
            <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.header">
                <h3 class="text-lg font-semibold text-gray-300">Vendas Recentes</h3>
            </div>
            <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.body">
                @if($salesData['recent_sales']->count() > 0)
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-700">
                        <thead>
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Álbum</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Comprador</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Vendedor</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Valor</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Método</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Data</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-700">
                            @foreach($salesData['recent_sales'] as $sale)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-300">{{ $sale->album->name }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-300">{{ $sale->buyer->name }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-300">{{ $sale->seller->name }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-green-400">
                                        R$ {{ number_format($sale->amount, 2, ',', '.') }}
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 py-1 text-xs font-medium rounded-full {{ $sale->payment_method === 'wallet' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800' }}">
                                        {{ $sale->payment_method === 'wallet' ? 'Carteira' : 'Cartão' }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                                    {{ $sale->purchased_at->format('d/m/Y H:i') }}
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                @else
                <div class="text-center py-8">
                    <flux:icon name="shopping-cart" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p class="text-gray-400">Nenhuma venda no período selecionado</p>
                </div>
                @endif
            </div>
        </div>
    </div>
    @endif

    <!-- Creators Tab -->
    @if($activeTab === 'creators' && isset($creatorsData))
    <div class="space-y-6">
        <!-- Top Creators -->
        <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg">
            <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.header">
                <h3 class="text-lg font-semibold text-gray-300">Top Criadores por Receita</h3>
            </div>
            <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.body">
                @if($creatorsData['top_creators']->count() > 0)
                <div class="space-y-4">
                    @foreach($creatorsData['top_creators'] as $index => $creator)
                    <div class="flex items-center space-x-4 p-4 bg-zinc-800 rounded-lg">
                        <div class="text-2xl font-bold text-purple-400">{{ $index + 1 }}</div>
                        <img 
                            src="{{ $creator->avatar_url ?? '/default-avatar.png' }}" 
                            alt="{{ $creator->name }}"
                            class="w-12 h-12 rounded-full"
                        />
                        <div class="flex-1">
                            <h4 class="font-medium text-gray-300">{{ $creator->name }}</h4>
                            <p class="text-sm text-gray-400">@{{ $creator->username }}</p>
                        </div>
                        <div class="text-right">
                            <div class="text-lg font-bold text-green-400">
                                R$ {{ number_format($creator->total_revenue ?? 0, 2, ',', '.') }}
                            </div>
                            <div class="text-sm text-gray-400">
                                {{ $creator->total_sales }} vendas • {{ $creator->published_albums }} álbuns
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
                @else
                <div class="text-center py-8">
                    <flux:icon name="user-group" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p class="text-gray-400">Nenhum criador encontrado</p>
                </div>
                @endif
            </div>
        </div>
    </div>
    @endif

    <!-- Albums Tab -->
    @if($activeTab === 'albums' && isset($albumsData))
    <div class="space-y-6">
        <!-- Top Albums -->
        <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg">
            <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.header">
                <h3 class="text-lg font-semibold text-gray-300">Álbuns Mais Vendidos</h3>
            </div>
            <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.body">
                @if($albumsData['top_albums']->count() > 0)
                <div class="space-y-4">
                    @foreach($albumsData['top_albums'] as $index => $album)
                    <div class="flex items-center space-x-4 p-4 bg-zinc-800 rounded-lg">
                        <div class="text-2xl font-bold text-purple-400">{{ $index + 1 }}</div>
                        <div class="w-16 h-16 bg-zinc-700 rounded-lg overflow-hidden">
                            @if($album->medias->count() > 0)
                                @php $firstMedia = $album->medias->first(); @endphp
                                @if($firstMedia->type === 'photo')
                                    <img 
                                        src="{{ route('premium-albums.media', [$album, $firstMedia->id]) }}" 
                                        alt="{{ $album->name }}"
                                        class="w-full h-full object-cover"
                                    />
                                @endif
                            @endif
                        </div>
                        <div class="flex-1">
                            <h4 class="font-medium text-gray-300">{{ $album->name }}</h4>
                            <p class="text-sm text-gray-400">por {{ $album->user->name }}</p>
                        </div>
                        <div class="text-right">
                            <div class="text-lg font-bold text-green-400">
                                R$ {{ number_format($album->total_revenue ?? 0, 2, ',', '.') }}
                            </div>
                            <div class="text-sm text-gray-400">
                                {{ $album->sales_count }} vendas • R$ {{ number_format($album->price, 2, ',', '.') }}
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
                @else
                <div class="text-center py-8">
                    <flux:icon name="photo" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p class="text-gray-400">Nenhum álbum encontrado</p>
                </div>
                @endif
            </div>
        </div>
    </div>
    @endif
</div>
