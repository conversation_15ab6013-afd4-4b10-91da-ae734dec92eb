<?php

namespace Tests\Unit;

use App\Models\User;
use App\Models\Product;
use App\Models\Order;
use App\Models\OrderItem;
use App\Services\VipPurchaseService;
use App\Services\VipSubscriptionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class VipPurchaseServiceTest extends TestCase
{
    use RefreshDatabase;

    protected $vipPurchaseService;
    protected $mockVipSubscriptionService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->mockVipSubscriptionService = Mockery::mock(VipSubscriptionService::class);
        $this->vipPurchaseService = new VipPurchaseService($this->mockVipSubscriptionService);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /** @test */
    public function it_can_identify_vip_products()
    {
        // Create a VIP product
        $vipProduct = new Product([
            'name' => 'VIP Monthly',
            'grants_vip_access' => true,
            'vip_duration_days' => 30
        ]);

        $this->assertTrue($vipProduct->grantsVipAccess());
        $this->assertEquals(30, $vipProduct->getVipDurationDays());
    }

    /** @test */
    public function it_can_identify_non_vip_products()
    {
        // Create a regular product
        $regularProduct = new Product([
            'name' => 'Regular Product',
            'grants_vip_access' => false
        ]);

        $this->assertFalse($regularProduct->grantsVipAccess());
        $this->assertEquals(0, $regularProduct->getVipDurationDays());
    }

    /** @test */
    public function it_can_format_vip_benefits()
    {
        $product = new Product([
            'vip_benefits' => json_encode(['Benefit 1', 'Benefit 2', 'Benefit 3'])
        ]);

        $benefits = $product->getVipBenefitsArray();
        $this->assertCount(3, $benefits);
        $this->assertEquals('Benefit 1', $benefits[0]);

        $formatted = $product->getVipBenefitsFormatted();
        $this->assertEquals('Benefit 1, Benefit 2, Benefit 3', $formatted);
    }

    /** @test */
    public function it_handles_empty_vip_benefits()
    {
        $product = new Product([
            'vip_benefits' => null
        ]);

        $benefits = $product->getVipBenefitsArray();
        $this->assertEmpty($benefits);

        $formatted = $product->getVipBenefitsFormatted();
        $this->assertEquals('Acesso VIP padrão', $formatted);
    }

    /** @test */
    public function it_can_scope_vip_products()
    {
        // This test would require database setup, so we'll just test the method exists
        $this->assertTrue(method_exists(Product::class, 'scopeVipProducts'));
    }
}
