<div class="p-6 space-y-6">
    <!-- Cabeçalho -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-300">Analytics de Usuários</h1>
            <p class="text-gray-400 mt-1">Monitoramento de atividade e comportamento dos usuários</p>
        </div>
        
        <div class="flex gap-3">
            <flux:button wire:click="exportData" variant="outline" size="sm">
                <flux:icon name="arrow-down-tray" class="w-4 h-4 mr-2" />
                Exportar CSV
            </flux:button>
        </div>
    </div>

    <!-- Filtros -->
    <div class="bg-zinc-900 border border-zinc-700 rounded-lg p-4">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <flux:select wire:model.live="dateRange" label="Período">
                    <option value="1">Último dia</option>
                    <option value="7">Últimos 7 dias</option>
                    <option value="30">Últimos 30 dias</option>
                    <option value="90">Últimos 90 dias</option>
                </flux:select>
            </div>
            
            <div>
                <flux:select wire:model.live="activityType" label="Tipo de Atividade">
                    <option value="all">Todas</option>
                    <option value="page_view">Visualizações</option>
                    <option value="click">Cliques</option>
                    <option value="form_submit">Formulários</option>
                </flux:select>
            </div>
            
            <div>
                <flux:select wire:model.live="selectedUser" label="Usuário">
                    <option value="">Todos os usuários</option>
                    @foreach($usersForFilter as $user)
                        <option value="{{ $user->id }}">{{ $user->name }} (@{{ $user->username }})</option>
                    @endforeach
                </flux:select>
            </div>
            
            <div>
                <flux:input wire:model.live="selectedUrl" label="URL" placeholder="Filtrar por URL..." />
            </div>
        </div>
    </div>

    <!-- Cards de Estatísticas -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="bg-zinc-900 border border-zinc-700 rounded-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-400 text-sm">Visualizações</p>
                    <p class="text-2xl font-bold text-white">{{ number_format($totalPageViews) }}</p>
                </div>
                <flux:icon name="eye" class="w-8 h-8 text-blue-500" />
            </div>
        </div>
        
        <div class="bg-zinc-900 border border-zinc-700 rounded-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-400 text-sm">Cliques</p>
                    <p class="text-2xl font-bold text-white">{{ number_format($totalClicks) }}</p>
                </div>
                <flux:icon name="cursor-arrow-rays" class="w-8 h-8 text-green-500" />
            </div>
        </div>
        
        <div class="bg-zinc-900 border border-zinc-700 rounded-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-400 text-sm">Usuários Únicos</p>
                    <p class="text-2xl font-bold text-white">{{ number_format($totalUsers) }}</p>
                </div>
                <flux:icon name="users" class="w-8 h-8 text-purple-500" />
            </div>
        </div>
        
        <div class="bg-zinc-900 border border-zinc-700 rounded-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-400 text-sm">Tempo Médio (seg)</p>
                    <p class="text-2xl font-bold text-white">{{ $avgTimeOnPage }}</p>
                </div>
                <flux:icon name="clock" class="w-8 h-8 text-yellow-500" />
            </div>
        </div>
    </div>

    <!-- Gráficos e Tabelas -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Páginas Mais Visitadas -->
        <div class="bg-zinc-900 border border-zinc-700 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-gray-300 mb-4">Páginas Mais Visitadas</h3>
            <div class="space-y-3">
                @forelse($topPages as $page)
                    <div class="flex justify-between items-center py-2 border-b border-zinc-700 last:border-b-0">
                        <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-white truncate">
                                {{ $page->page_title ?: 'Sem título' }}
                            </p>
                            <p class="text-xs text-gray-400 truncate">{{ $page->url }}</p>
                        </div>
                        <span class="text-sm font-semibold text-blue-400 ml-3">
                            {{ number_format($page->views) }}
                        </span>
                    </div>
                @empty
                    <p class="text-gray-400 text-center py-4">Nenhum dado disponível</p>
                @endforelse
            </div>
        </div>

        <!-- Usuários Mais Ativos -->
        <div class="bg-zinc-900 border border-zinc-700 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-gray-300 mb-4">Usuários Mais Ativos</h3>
            <div class="space-y-3">
                @forelse($topUsers as $userActivity)
                    <div class="flex justify-between items-center py-2 border-b border-zinc-700 last:border-b-0">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-purple-700 rounded-full flex items-center justify-center">
                                <span class="text-xs font-bold text-white">
                                    {{ substr($userActivity->user->name ?? 'A', 0, 1) }}
                                </span>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-white">
                                    {{ $userActivity->user->name ?? 'Usuário removido' }}
                                </p>
                                <p class="text-xs text-gray-400">
                                    @{{ $userActivity->user->username ?? 'N/A' }}
                                </p>
                            </div>
                        </div>
                        <span class="text-sm font-semibold text-green-400">
                            {{ number_format($userActivity->activities) }}
                        </span>
                    </div>
                @empty
                    <p class="text-gray-400 text-center py-4">Nenhum dado disponível</p>
                @endforelse
            </div>
        </div>
    </div>

    <!-- Estatísticas por Dispositivo -->
    @if(!empty($deviceStats))
    <div class="bg-zinc-900 border border-zinc-700 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-gray-300 mb-4">Dispositivos</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            @foreach($deviceStats as $device => $count)
                <div class="text-center">
                    <div class="text-2xl font-bold text-white">{{ number_format($count) }}</div>
                    <div class="text-sm text-gray-400 capitalize">{{ $device }}</div>
                </div>
            @endforeach
        </div>
    </div>
    @endif

    <!-- Log de Atividades -->
    <div class="bg-zinc-900 border border-zinc-700 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-gray-300 mb-4">Log de Atividades Recentes</h3>
        
        <div class="overflow-x-auto">
            <table class="w-full text-sm">
                <thead>
                    <tr class="border-b border-zinc-700">
                        <th class="text-left py-3 px-2 text-gray-300">Data/Hora</th>
                        <th class="text-left py-3 px-2 text-gray-300">Usuário</th>
                        <th class="text-left py-3 px-2 text-gray-300">Atividade</th>
                        <th class="text-left py-3 px-2 text-gray-300">Página</th>
                        <th class="text-left py-3 px-2 text-gray-300">Dispositivo</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($activities as $activity)
                        <tr class="border-b border-zinc-800 hover:bg-zinc-800">
                            <td class="py-3 px-2 text-gray-300">
                                {{ $activity->created_at->format('d/m H:i') }}
                            </td>
                            <td class="py-3 px-2">
                                @if($activity->user)
                                    <div class="text-white">{{ $activity->user->name }}</div>
                                    <div class="text-xs text-gray-400">@{{ $activity->user->username }}</div>
                                @else
                                    <span class="text-gray-400">Anônimo</span>
                                @endif
                            </td>
                            <td class="py-3 px-2">
                                <span class="px-2 py-1 rounded text-xs font-medium
                                    @if($activity->activity_type === 'page_view') bg-blue-900 text-blue-300
                                    @elseif($activity->activity_type === 'click') bg-green-900 text-green-300
                                    @elseif($activity->activity_type === 'form_submit') bg-purple-900 text-purple-300
                                    @else bg-gray-900 text-gray-300
                                    @endif">
                                    {{ ucfirst(str_replace('_', ' ', $activity->activity_type)) }}
                                </span>
                            </td>
                            <td class="py-3 px-2">
                                <div class="text-white text-xs">{{ $activity->page_title ?: 'Sem título' }}</div>
                                @if($activity->element_text)
                                    <div class="text-gray-400 text-xs truncate max-w-xs">
                                        {{ $activity->element_text }}
                                    </div>
                                @endif
                            </td>
                            <td class="py-3 px-2 text-gray-300 capitalize">
                                {{ $activity->device_type ?: 'N/A' }}
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="5" class="py-8 text-center text-gray-400">
                                Nenhuma atividade encontrada para os filtros selecionados
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Paginação -->
        <div class="mt-4">
            {{ $activities->links() }}
        </div>
    </div>
</div>
