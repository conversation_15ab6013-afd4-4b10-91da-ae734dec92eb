<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white dark:bg-zinc-800 shadow-md rounded-lg p-6">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                    Participantes Confirmados
                </h1>
                <p class="text-gray-600 dark:text-gray-300 mt-1">
                    {{ $event->name }}
                </p>
                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    {{ $event->formatted_date }} às {{ $event->formatted_start_time }}
                </p>
            </div>
            <div class="flex gap-3">
                <flux:button href="{{ route('events.show', $event->slug) }}" variant="outline">
                    <flux:icon name="arrow-left" class="h-4 w-4 mr-2" />
                    Voltar ao Evento
                </flux:button>
            </div>
        </div>
    </div>

    <!-- Estatísticas -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="bg-white dark:bg-zinc-800 p-4 rounded-lg shadow">
            <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ $stats['total_confirmed'] }}</div>
            <div class="text-sm text-gray-600 dark:text-gray-300">Participantes Confirmados</div>
        </div>
        @if($stats['capacity'])
            <div class="bg-white dark:bg-zinc-800 p-4 rounded-lg shadow">
                <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{ $stats['capacity'] }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-300">Capacidade Total</div>
            </div>
            <div class="bg-white dark:bg-zinc-800 p-4 rounded-lg shadow">
                <div class="text-2xl font-bold {{ $stats['spots_remaining'] > 0 ? 'text-orange-600 dark:text-orange-400' : 'text-red-600 dark:text-red-400' }}">
                    {{ $stats['spots_remaining'] }}
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-300">Vagas Restantes</div>
            </div>
        @else
            <div class="bg-white dark:bg-zinc-800 p-4 rounded-lg shadow">
                <div class="text-2xl font-bold text-green-600 dark:text-green-400">∞</div>
                <div class="text-sm text-gray-600 dark:text-gray-300">Vagas Ilimitadas</div>
            </div>
        @endif
    </div>

    <!-- Busca -->
    <div class="bg-white dark:bg-zinc-800 shadow-md rounded-lg p-6">
        <div class="max-w-md">
            <flux:input 
                wire:model.live.debounce.300ms="search" 
                placeholder="Buscar participante..."
                class="w-full"
            >
                <flux:icon name="magnifying-glass" slot="leading" class="h-5 w-5" />
            </flux:input>
        </div>
    </div>

    <!-- Lista de Participantes -->
    <div class="bg-white dark:bg-zinc-800 shadow-md rounded-lg overflow-hidden">
        @if($participants->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 p-6">
                @foreach($participants as $participant)
                    <div class="bg-gray-50 dark:bg-zinc-700 rounded-lg p-4 hover:bg-gray-100 dark:hover:bg-zinc-600 transition-colors">
                        <div class="flex items-center space-x-3">
                            <div class="flex-shrink-0">
                                <img class="h-12 w-12 rounded-full object-cover" 
                                     src="{{ $participant->user->avatar_url }}" 
                                     alt="{{ $participant->user->name }}">
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="text-sm font-medium text-gray-900 dark:text-white truncate">
                                    {{ $participant->user->name }}
                                </div>
                                <div class="text-sm text-gray-500 dark:text-gray-400 truncate">
                                    {{ '@' . $participant->user->username }}
                                </div>
                                <div class="text-xs text-gray-400 dark:text-gray-500 mt-1">
                                    Inscrito em {{ $participant->created_at->format('d/m/Y') }}
                                </div>
                            </div>
                        </div>
                        
                        <!-- Badge de participante confirmado -->
                        <div class="mt-3">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                <flux:icon name="check-circle" class="h-3 w-3 mr-1" />
                                Confirmado
                            </span>
                        </div>

                        <!-- Link para perfil (se público) -->
                        <div class="mt-3">
                            <a href="/{{ $participant->user->username }}" 
                               class="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium">
                                Ver perfil →
                            </a>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Paginação -->
            @if($participants->hasPages())
                <div class="px-6 py-4 border-t border-gray-200 dark:border-zinc-700">
                    {{ $participants->links() }}
                </div>
            @endif
        @else
            <div class="px-6 py-12 text-center">
                <flux:icon name="users" class="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    @if($search)
                        Nenhum participante encontrado
                    @else
                        Ainda não há participantes confirmados
                    @endif
                </h3>
                <p class="text-gray-500 dark:text-gray-400">
                    @if($search)
                        Tente buscar com outros termos ou limpe a busca para ver todos os participantes.
                    @else
                        Seja o primeiro a se inscrever neste evento!
                    @endif
                </p>
                @if($search)
                    <div class="mt-4">
                        <flux:button wire:click="$set('search', '')" variant="outline">
                            Limpar busca
                        </flux:button>
                    </div>
                @endif
            </div>
        @endif
    </div>

    <!-- Informações adicionais -->
    <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <flux:icon name="information-circle" class="h-5 w-5 text-blue-400" />
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">
                    Sobre esta lista
                </h3>
                <div class="mt-2 text-sm text-blue-700 dark:text-blue-300">
                    <p>Esta lista mostra apenas os participantes que confirmaram sua presença através do pagamento do ingresso. A lista é atualizada em tempo real conforme novas inscrições são confirmadas.</p>
                </div>
            </div>
        </div>
    </div>
</div>
