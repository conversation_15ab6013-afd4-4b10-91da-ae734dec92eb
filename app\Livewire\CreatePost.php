<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithFileUploads;
use App\Models\Post;
use App\Models\UserPoint;
use App\Models\UserPointLog;
use App\Services\ContentProcessor;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Http; // Added for API calls

class CreatePost extends Component
{
    use WithFileUploads;

    public $content = '';
    public $image = null;
    public $video = null;
    public $uploading = false;

    // Properties for hashtag autocomplete
    public $hashtagQuery = '';
    public $hashtagSuggestions = [];
    public $showHashtagSuggestions = false;
    protected $currentHashtagStartPosition = null;

    // Properties for mention autocomplete
    public $mentionQuery = '';
    public $mentionSuggestions = [];
    public $showMentionSuggestions = false;
    protected $currentMentionStartPosition = null;

    // Properties for mention modal
    public $showMentionsModal = false;
    public $mentionSearchQuery = '';
    public $mentionsList = [];

    // Properties for hashtag modal
    public $showHashtagsModal = false;
    public $hashtagSearchQuery = '';
    public $hashtagsList = [];

    protected function rules()
    {
        return [
            'content' => $this->image || $this->video ? 'nullable' : 'required|min:3',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:204800', // 200MB
            'video' => 'nullable|file|mimetypes:video/mp4,video/quicktime,video/avi,video/webm|max:204800' // 200MB
        ];
    }

    protected $messages = [
        'image.max' => 'A imagem não pode ser maior que 200MB.',
        'image.mimes' => 'A imagem deve estar nos formatos: JPEG, PNG, JPG, GIF ou WebP.',
        'video.max' => 'O vídeo não pode ser maior que 200MB.',
        'video.mimetypes' => 'O vídeo deve estar nos formatos: MP4, MOV, AVI ou WebM.',
        'content.required' => 'É necessário adicionar um texto, imagem ou vídeo.',
        'content.min' => 'O texto deve ter pelo menos 3 caracteres.',
    ];

    public function store()
    {
        $this->uploading = true;
        try {
            // Validação com mensagens de erro específicas
            $this->validate();

            // Ensure at least one of content, image, or video is present
            if (empty($this->content) && !$this->image && !$this->video) {
                $this->addError('content', 'É necessário fornecer pelo menos um texto, imagem ou vídeo.');
                $this->uploading = false;
                return;
            }

            // Verificar tamanho dos arquivos antes do upload
            if ($this->image && $this->image->getSize() > 209715200) { // 200MB em bytes
                $this->addError('image', 'A imagem é muito grande. Tamanho máximo: 200MB.');
                $this->uploading = false;
                return;
            }

            if ($this->video && $this->video->getSize() > 209715200) { // 200MB em bytes
                $this->addError('video', 'O vídeo é muito grande. Tamanho máximo: 200MB.');
                $this->uploading = false;
                return;
            }

            $imagePath = null;
            if ($this->image) {
                try {
                    // Verificar se o arquivo é válido
                    if (!$this->image->isValid()) {
                        throw new \Exception('Arquivo de imagem inválido ou corrompido.');
                    }

                    // Gerar nome único para o arquivo
                    $filename = time() . '_' . uniqid() . '.' . $this->image->getClientOriginalExtension();
                    $imagePath = $this->image->storeAs('posts/images', $filename, 'public');

                    if (!$imagePath) {
                        throw new \Exception('Falha ao salvar a imagem no servidor.');
                    }

                    Log::info('Image uploaded successfully', [
                        'filename' => $filename,
                        'path' => $imagePath,
                        'size' => $this->image->getSize()
                    ]);
                } catch (\Exception $e) {
                    Log::error('Error uploading image', [
                        'error' => $e->getMessage(),
                        'file_size' => $this->image ? $this->image->getSize() : 'unknown',
                        'file_name' => $this->image ? $this->image->getClientOriginalName() : 'unknown'
                    ]);
                    $this->addError('image', 'Erro ao fazer upload da imagem: ' . $e->getMessage());
                    $this->uploading = false;
                    return;
                }
            }

            $videoPath = null;
            if ($this->video) {
                try {
                    // Verificar se o arquivo é válido
                    if (!$this->video->isValid()) {
                        throw new \Exception('Arquivo de vídeo inválido ou corrompido.');
                    }

                    // Gerar nome único para o arquivo
                    $videoFilename = time() . '_' . uniqid() . '.' . $this->video->getClientOriginalExtension();
                    $videoPath = $this->video->storeAs('posts/videos', $videoFilename, 'public');

                    if (!$videoPath) {
                        throw new \Exception('Falha ao salvar o vídeo no servidor.');
                    }

                    Log::info('Video uploaded successfully', [
                        'filename' => $videoFilename,
                        'path' => $videoPath,
                        'size' => $this->video->getSize()
                    ]);
                } catch (\Exception $e) {
                    Log::error('Error uploading video', [
                        'error' => $e->getMessage(),
                        'file_size' => $this->video ? $this->video->getSize() : 'unknown',
                        'file_name' => $this->video ? $this->video->getClientOriginalName() : 'unknown'
                    ]);

                    // Limpar imagem se o vídeo falhou
                    if ($imagePath) {
                        Storage::disk('public')->delete($imagePath);
                        Log::info('Cleaned up image after video upload failure', ['image_path' => $imagePath]);
                    }

                    $this->addError('video', 'Erro ao fazer upload do vídeo: ' . $e->getMessage());
                    $this->uploading = false;
                    return;
                }
            }

            $post = Post::create([
                'content' => $this->content,
                'image' => $imagePath,
                'video' => $videoPath,
                'user_id' => auth()->id(),
                'group_id' => null, // Definindo explicitamente como null para posts fora de grupos
            ]);

            if ($post) {
                Log::info('Post created successfully', ['post' => $post->toArray()]);

                // Processar menções e hashtags
                ContentProcessor::processMentions($this->content, $post, auth()->id());
                ContentProcessor::processHashtags($this->content, $post);

                $user = auth()->user();
                $pointsToAdd = 10; // Base points

                // Bônus por conteúdo multimídia
                if ($imagePath) $pointsToAdd += 5;
                if ($videoPath) $pointsToAdd += 10;

                // Bônus por tamanho do conteúdo
                if (strlen($this->content) > 100) $pointsToAdd += 5;

                // Adiciona pontos ao usuário
                \App\Models\UserPoint::addPoints(
                    $user->id,
                    'post',
                    $pointsToAdd,
                    "Criou uma nova postagem" .
                        ($imagePath ? " com imagem" : "") .
                        ($videoPath ? " com vídeo" : ""),
                    $post->id,
                    \App\Models\Post::class
                );

                // Dispara evento para animação de recompensa
                $this->dispatch('reward-earned', points: $pointsToAdd);

                // Lógica para conceder a conquista "Primeiro Post"
                $user = auth()->user();
                if ($user && $user->posts()->count() === 1) { // Verifica se este é o primeiro post do usuário
                    $firstPostAchievement = \App\Models\Achievement::where('name', 'Primeiro Post')->first();
                    if ($firstPostAchievement && !$user->achievements->contains($firstPostAchievement->id)) {
                        // Concede a conquista e adiciona os pontos da conquista ao usuário
                        $user->achievements()->attach($firstPostAchievement->id);

                        // Adicionar os pontos da conquista aos pontos totais do usuário
                        \App\Models\UserPoint::addPoints(
                            $user->id,
                            'achievement',
                            $firstPostAchievement->points,
                            "Ganhou a conquista \"" . $firstPostAchievement->name . "\"",
                            $firstPostAchievement->id,
                            \App\Models\Achievement::class
                        );

                        session()->flash('achievement', $firstPostAchievement->name); // Opcional: para mostrar uma notificação de conquista
                    }
                }

                $this->reset(['content', 'image', 'video']);
                session()->flash('message', 'Post criado com sucesso!');
                return redirect()->route('dashboard');
            }
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error('Validation error creating post', [
                'errors' => $e->errors(),
                'user_id' => auth()->id()
            ]);
            $this->uploading = false;
            // Livewire automaticamente mostra os erros de validação

        } catch (\Exception $e) {
            Log::error('Error creating post', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => auth()->id(),
                'has_image' => !is_null($this->image),
                'has_video' => !is_null($this->video)
            ]);

            // Limpar arquivos em caso de erro
            if (isset($imagePath) && $imagePath) {
                Storage::disk('public')->delete($imagePath);
            }
            if (isset($videoPath) && $videoPath) {
                Storage::disk('public')->delete($videoPath);
            }

            $this->addError('general', 'Erro inesperado ao criar o post. Tente novamente.');
            $this->uploading = false;
        } finally {
            $this->uploading = false;
        }
    }

    public function render()
    {
        return view('livewire.create-post');
    }

    public function handleContentInput()
    {
        $content = $this->content;
        error_log('HandleContentInput called with: ' . $content);

        // Reset suggestions visibility initially
        $this->showHashtagSuggestions = false;
        $this->showMentionSuggestions = false;

        // Check for Hashtag: #tag at the end of a word or content
        if (preg_match('/(?<=\s|^)\#([a-zA-Z0-9_]*)$/', $content, $matches, PREG_OFFSET_CAPTURE)) {
            $this->currentHashtagStartPosition = $matches[0][1];
            $this->hashtagQuery = $matches[1][0]; // Triggers updatedHashtagQuery
            // Hide mention suggestions if we're clearly typing a hashtag
            $this->mentionQuery = '';
            $this->mentionSuggestions = [];
            return;
        } else {
            // If no hashtag pattern is at the end, reset hashtag query
            if (!empty($this->hashtagQuery)) {
                $this->hashtagQuery = '';
                $this->hashtagSuggestions = [];
            }
        }

        // Check for Mention: @user at the end of a word or content
        // Allows for usernames with dots (e.g. @john.doe)
        if (preg_match('/(?<=\s|^)\@([a-zA-Z0-9_.]*)$/', $content, $matches, PREG_OFFSET_CAPTURE)) {
            $this->currentMentionStartPosition = $matches[0][1];
            $this->mentionQuery = $matches[1][0]; // Triggers updatedMentionQuery
            // Hide hashtag suggestions if we're clearly typing a mention
            $this->hashtagQuery = '';
            $this->hashtagSuggestions = [];
            return;
        } else {
            // If no mention pattern is at the end, reset mention query
            if (!empty($this->mentionQuery)) {
                $this->mentionQuery = '';
                $this->mentionSuggestions = [];
            }
        }
    }

    public function updatedHashtagQuery($query)
    {
        if (strlen($query) >= 1) { // Minimum 1 char after #
            try {
                // Assuming the API is on the same domain, session cookies should handle auth for Sanctum
                $response = Http::get(url('/api/hashtags/search'), [
                    'query' => $query,
                    'limit' => 7
                ]);

                if ($response->successful()) {
                    $this->hashtagSuggestions = $response->json();
                    $this->showHashtagSuggestions = !empty($this->hashtagSuggestions);
                } else {
                    Log::error('Hashtag API search failed: ' . $response->status(), $response->json());
                    $this->hashtagSuggestions = [];
                    $this->showHashtagSuggestions = false;
                }
            } catch (\Exception $e) {
                Log::error('Error calling hashtag API: ' . $e->getMessage());
                $this->hashtagSuggestions = [];
                $this->showHashtagSuggestions = false;
            }
        } else {
            $this->hashtagSuggestions = [];
            $this->showHashtagSuggestions = false;
        }
    }

    public function selectHashtag($selectedHashtagName)
    {
        if ($this->currentHashtagStartPosition !== null) {
            $textBeforeHashtag = substr($this->content, 0, $this->currentHashtagStartPosition);

            // Determine the end of the typed hashtag (e.g., "#que" or "#query")
            $lengthOfTypedHashtag = strlen($this->hashtagQuery); // This is just the query part, e.g., "que"

            // The part of the content string to be replaced starts at currentHashtagStartPosition
            // and its length is 1 (for '#') + lengthOfTypedHashtag
            $originalHashtagLength = 1 + $lengthOfTypedHashtag;

            $textAfterOriginalHashtag = substr($this->content, $this->currentHashtagStartPosition + $originalHashtagLength);

            $this->content = $textBeforeHashtag . '#' . $selectedHashtagName . ' ' . $textAfterOriginalHashtag;
        }

        // Reset hashtag state
        $this->hashtagQuery = '';
        $this->hashtagSuggestions = [];
        $this->showHashtagSuggestions = false;
        $this->currentHashtagStartPosition = null;
        $this->dispatch('contentUpdated', $this->content);
    }

    public function closeHashtagSuggestions()
    {
        $this->showHashtagSuggestions = false;
    }

    // Mention Autocomplete Logic
    public function updatedMentionQuery($query)
    {
        if (strlen($query) >= 1) {
            try {
                $response = Http::get(url('/api/users/search'), [
                    'query' => $query,
                    'limit' => 7
                ]);

                if ($response->successful()) {
                    $this->mentionSuggestions = $response->json();
                    $this->showMentionSuggestions = !empty($this->mentionSuggestions);
                } else {
                    Log::error('User API search failed: ' . $response->status(), $response->json());
                    $this->mentionSuggestions = [];
                    $this->showMentionSuggestions = false;
                }
            } catch (\Exception $e) {
                Log::error('Error calling User API: ' . $e->getMessage());
                $this->mentionSuggestions = [];
                $this->showMentionSuggestions = false;
            }
        } else {
            $this->mentionSuggestions = [];
            $this->showMentionSuggestions = false;
        }
    }

    public function selectMention($selectedUsername)
    {
        if ($this->currentMentionStartPosition !== null) {
            $textBeforeMention = substr($this->content, 0, $this->currentMentionStartPosition);
            $lengthOfTypedMention = strlen($this->mentionQuery);
            $originalMentionLength = 1 + $lengthOfTypedMention; // for '@' + query
            $textAfterOriginalMention = substr($this->content, $this->currentMentionStartPosition + $originalMentionLength);

            $this->content = $textBeforeMention . '@' . $selectedUsername . ' ' . $textAfterOriginalMention;
        }

        $this->mentionQuery = '';
        $this->mentionSuggestions = [];
        $this->showMentionSuggestions = false;
        $this->currentMentionStartPosition = null;
        $this->dispatch('contentUpdated', $this->content);
    }

    public function closeMentionSuggestions()
    {
        $this->showMentionSuggestions = false;
    }

    // Modal methods for mentions
    public function showMentionsList()
    {
        $this->showMentionsModal = true;
        $this->mentionSearchQuery = '';
        $this->loadMentionsList();
    }

    public function closeMentionsModal()
    {
        $this->showMentionsModal = false;
        $this->mentionSearchQuery = '';
        $this->mentionsList = [];
    }

    public function updatedMentionSearchQuery()
    {
        $this->loadMentionsList();
    }

    public function loadMentionsList()
    {
        try {
            $response = Http::get(url('/api/users/search'), [
                'query' => $this->mentionSearchQuery ?: '',
                'limit' => 20
            ]);

            if ($response->successful()) {
                $this->mentionsList = $response->json();
            } else {
                $this->mentionsList = [];
            }
        } catch (\Exception $e) {
            Log::error('Error loading mentions list: ' . $e->getMessage());
            $this->mentionsList = [];
        }
    }

    public function addMentionToContent($username)
    {
        $this->content = trim($this->content) . ' @' . $username . ' ';
        $this->closeMentionsModal();
    }

    public function addMentionFromList($index)
    {
        if (isset($this->mentionsList[$index]) && isset($this->mentionsList[$index]['username'])) {
            $username = $this->mentionsList[$index]['username'];
            $this->content = trim($this->content) . ' @' . $username . ' ';
            $this->closeMentionsModal();
        }
    }

    // Modal methods for hashtags
    public function showHashtagsList()
    {
        $this->showHashtagsModal = true;
        $this->hashtagSearchQuery = '';
        $this->loadHashtagsList();
    }

    public function closeHashtagsModal()
    {
        $this->showHashtagsModal = false;
        $this->hashtagSearchQuery = '';
        $this->hashtagsList = [];
    }

    public function updatedHashtagSearchQuery()
    {
        $this->loadHashtagsList();
    }

    public function loadHashtagsList()
    {
        try {
            $response = Http::get(url('/api/hashtags/search'), [
                'query' => $this->hashtagSearchQuery ?: '',
                'limit' => 20
            ]);

            if ($response->successful()) {
                $this->hashtagsList = $response->json();
            } else {
                $this->hashtagsList = [];
            }
        } catch (\Exception $e) {
            Log::error('Error loading hashtags list: ' . $e->getMessage());
            $this->hashtagsList = [];
        }
    }

    public function addHashtagToContent($hashtagName)
    {
        $this->content = trim($this->content) . ' #' . $hashtagName . ' ';
        $this->closeHashtagsModal();
    }

    public function addHashtagFromList($index)
    {
        if (isset($this->hashtagsList[$index]) && isset($this->hashtagsList[$index]['name'])) {
            $hashtagName = $this->hashtagsList[$index]['name'];
            $this->content = trim($this->content) . ' #' . $hashtagName . ' ';
            $this->closeHashtagsModal();
        }
    }

    public function testMentions()
    {
        // Simular uma menção para teste
        $this->content = $this->content . ' @test';
        $this->mentionQuery = 'test';
        $this->showMentionSuggestions = true;
        $this->mentionSuggestions = [
            [
                'id' => 1,
                'name' => 'Usuário Teste',
                'username' => 'teste',
                'avatar' => '/images/users/avatar.svg'
            ]
        ];
    }
}
