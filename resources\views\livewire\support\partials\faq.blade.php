<!-- FAQ Section -->
<div>
    <!-- Category Filter -->
    @if($this->faqCategories->count() > 0)
        <div class="mb-6">
            <div class="flex flex-wrap gap-2">
                <flux:button 
                    wire:click="$set('selectedCategory', '')"
                    variant="{{ $selectedCategory === '' ? 'primary' : 'ghost' }}"
                    size="sm">
                    Todas as Categorias
                </flux:button>
                @foreach($this->faqCategories as $category)
                    <flux:button 
                        wire:click="$set('selectedCategory', '{{ $category }}')"
                        variant="{{ $selectedCategory === $category ? 'primary' : 'ghost' }}"
                        size="sm">
                        {{ ucfirst($category) }}
                    </flux:button>
                @endforeach
            </div>
        </div>
    @endif

    <!-- Featured FAQs -->
    @if(!$searchQuery && !$selectedCategory && $this->featuredFaqs->count() > 0)
        <div class="mb-8">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Perguntas Mais Frequentes</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                @foreach($this->featuredFaqs as $faq)
                    <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md hover:shadow-lg transition-shadow border border-gray-200 dark:border-gray-700">
                        <div class="p-4">
                            <div class="flex items-center justify-between mb-2">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                    {{ ucfirst($faq->category) }}
                                </span>
                                @if($faq->helpful_count > 0 || $faq->not_helpful_count > 0)
                                    <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"></path>
                                        </svg>
                                        {{ $faq->helpful_percentage }}%
                                    </div>
                                @endif
                            </div>
                            <h3 class="font-semibold text-gray-900 dark:text-white mb-2">{{ $faq->question }}</h3>
                            <p class="text-gray-600 dark:text-gray-300 text-sm">
                                {{ Str::limit(strip_tags($faq->answer), 100) }}
                            </p>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    @endif

    <!-- All FAQs -->
    <div>
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
                @if($searchQuery)
                    Resultados da Busca
                @elseif($selectedCategory)
                    FAQ - {{ ucfirst($selectedCategory) }}
                @else
                    Todas as Perguntas
                @endif
            </h2>
            @if($searchQuery || $selectedCategory)
                <button wire:click="clearSearch" class="inline-flex items-center px-3 py-1 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600 rounded-md transition-colors">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Limpar Filtros
                </button>
            @endif
        </div>

        @if($this->faqItems->count() > 0)
            <div class="space-y-4">
                @foreach($this->faqItems as $faq)
                    <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
                        <div x-data="{ open: false }" class="border-b border-gray-200 dark:border-gray-700 last:border-b-0">
                            <button 
                                @click="open = !open"
                                class="w-full px-6 py-4 text-left focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset">
                                <div class="flex items-center justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center gap-3 mb-1">
                                            <h3 class="font-semibold text-gray-900 dark:text-white">{{ $faq->question }}</h3>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                                                {{ ucfirst($faq->category) }}
                                            </span>
                                        </div>
                                        <div class="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                                            <span class="flex items-center">
                                                <x-flux::icon icon="eye" class="w-4 h-4 mr-1" />
                                                {{ $faq->view_count }} visualizações
                                            </span>
                                            @if($faq->helpful_count > 0 || $faq->not_helpful_count > 0)
                                                <span class="flex items-center">
                                                    <x-flux::icon icon="hand-thumb-up" class="w-4 h-4 mr-1" />
                                                    {{ $faq->helpful_percentage }}% útil
                                                </span>
                                            @endif
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <x-flux::icon 
                                            icon="chevron-down" 
                                            class="w-5 h-5 text-gray-400 transition-transform duration-200"
                                            x-bind:class="{ 'rotate-180': open }" />
                                    </div>
                                </div>
                            </button>
                            
                            <div x-show="open" x-collapse class="px-6 pb-4">
                                <div class="prose prose-sm max-w-none dark:prose-invert text-gray-100">
                                    {!! nl2br(e($faq->answer)) !!}
                                </div>
                                
                                <!-- Feedback -->
                                <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                                    <p class="text-sm text-gray-600 dark:text-gray-300 mb-2">Esta resposta foi útil?</p>
                                    <div class="flex items-center gap-2">
                                        <button
                                            wire:click="markFaqAsHelpful({{ $faq->id }})"
                                            class="inline-flex items-center px-3 py-1 text-sm font-medium text-green-600 hover:text-green-700 hover:bg-green-50 dark:text-green-400 dark:hover:text-green-300 dark:hover:bg-green-900/20 rounded-md transition-colors">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"></path>
                                            </svg>
                                            Sim ({{ $faq->helpful_count }})
                                        </button>
                                        <button
                                            wire:click="markFaqAsNotHelpful({{ $faq->id }})"
                                            class="inline-flex items-center px-3 py-1 text-sm font-medium text-red-600 hover:text-red-700 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-900/20 rounded-md transition-colors">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14H5.236a2 2 0 01-1.789-2.894l3.5-7A2 2 0 018.736 3h4.018c.163 0 .326.02.485.06L17 4m-7 10v2a2 2 0 002 2h.095c.5 0 .905-.405.905-.905 0-.714.211-1.412.608-2.006L17 13V4m-7 10h2m5-10h2a2 2 0 012 2v6a2 2 0 01-2 2h-2.5"></path>
                                            </svg>
                                            Não ({{ $faq->not_helpful_count }})
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="mt-6">
                {{ $this->faqItems->links() }}
            </div>
        @else
            <div class="text-center py-12">
                <x-flux::icon icon="question-mark-circle" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Nenhuma pergunta encontrada</h3>
                <p class="text-gray-600 dark:text-gray-300">
                    @if($searchQuery)
                        Tente buscar com outros termos ou navegue pelas categorias.
                    @else
                        Não há perguntas frequentes disponíveis no momento.
                    @endif
                </p>
                <div class="mt-4">
                    <button wire:click="$dispatch('openCreateTicketModal')" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                        Fazer uma Pergunta
                    </button>
                </div>
            </div>
        @endif
    </div>
</div>
