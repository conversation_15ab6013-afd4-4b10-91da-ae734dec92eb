<x-layouts.app>
    <div class="container mx-auto max-w-4xl px-4 sm:px-6 lg:px-8 py-6">
        @if(isset($conto))
            <div class="bg-zinc-800 shadow-lg rounded-lg p-4 sm:p-6 lg:p-8">
                <!-- Header com título e informações do autor -->
                <div class="mb-6">
                    <h1 class="text-2xl sm:text-3xl lg:text-4xl font-bold mb-4 text-gray-300 leading-tight">
                        {{ $conto->title }}
                    </h1>

                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4">
                        <div class="flex items-center space-x-3">
                            @php
                                $authorAvatar = $conto->user->currentPhoto ?? $conto->user->userPhotos->first();
                                $authorAvatarUrl = $authorAvatar ? Storage::url($authorAvatar->photo_path) : asset('images/default-avatar.jpg');
                            @endphp
                            <img src="{{ $authorAvatarUrl }}"
                                 class="w-10 h-10 sm:w-12 sm:h-12 rounded-full object-cover">
                            <div>
                                <p class="text-sm sm:text-base text-gray-400">
                                    Por <a href="{{ route('user.profile', $conto->user->username) }}"
                                           class="text-gray-300 hover:text-sky-400 font-semibold">
                                        {{ $conto->user->name ?? 'Autor desconhecido' }}
                                    </a>
                                </p>
                                <div class="flex items-center gap-3 text-xs sm:text-sm text-gray-500">
                                    <span>{{ $conto->created_at->format('d/m/Y') }} • {{ $conto->created_at->diffForHumans() }}</span>
                                    <span class="flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                        </svg>
                                        {{ number_format($conto->number_views) }} visualizações
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs sm:text-sm font-medium bg-blue-100 text-blue-800">
                                {{ $conto->category->title ?? 'Sem categoria' }}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Conteúdo do conto -->
                <div class="prose prose-lg dark:prose-invert max-w-none text-gray-300 mb-8">
                    <div class="text-base sm:text-lg leading-relaxed whitespace-pre-wrap">
                        {{ $conto->content }}
                    </div>
                </div>

                <!-- Botões de ação (apenas para o autor) -->
                @if(auth()->check() && auth()->id() === $conto->user_id)
                    <div class="border-t border-zinc-700 pt-6">
                        <div class="flex flex-col sm:flex-row gap-3">
                            <a href="{{ route('contos.edit', $conto->id) }}"
                               class="flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-yellow-600 rounded-lg hover:bg-yellow-700 transition">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-4.036a2.5 2.5 0 113.536 3.536L7.5 21H3v-4.5L16.732 3.732z" />
                                </svg>
                                Editar Conto
                            </a>

                            <form action="{{ route('contos.destroy', $conto->id) }}" method="POST" class="inline-block">
                                @csrf
                                @method('DELETE')
                                <button type="submit"
                                        onclick="return confirm('Tem certeza que deseja excluir este conto? Esta ação não pode ser desfeita.')"
                                        class="w-full sm:w-auto flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700 transition">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                    Excluir Conto
                                </button>
                            </form>
                        </div>
                    </div>
                @endif
            </div>
        @else
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg">
                <div class="flex items-center">
                    <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Conto não encontrado.
                </div>
            </div>
        @endif

        <!-- Botão voltar -->
        <div class="mt-6 sm:mt-8">
            <a href="{{ route('contos') }}"
               class="inline-flex items-center text-gray-300 hover:text-sky-400 transition">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
                Voltar à lista de contos
            </a>
        </div>
    </div>
</x-layouts.app>