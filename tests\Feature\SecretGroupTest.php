<?php

namespace Tests\Feature;

use App\Models\Group;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class SecretGroupTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    public function test_authenticated_user_can_create_secret_group()
    {
        $user = User::factory()->create();
        
        $this->actingAs($user);
        
        $groupData = [
            'name' => 'Grupo Secreto Teste',
            'description' => 'Descrição do grupo secreto',
            'privacy' => 'secret',
            'posts_require_approval' => false,
        ];
        
        $response = $this->post(route('grupos.store'), $groupData);
        
        $this->assertDatabaseHas('groups', [
            'name' => 'Grupo Secreto Teste',
            'privacy' => 'secret',
            'creator_id' => $user->id,
        ]);
        
        $group = Group::where('name', 'Grupo Secreto Teste')->first();
        $this->assertNotNull($group);
        
        // Verificar se o criador foi adicionado como admin
        $this->assertTrue($group->isAdmin($user));
        $this->assertTrue($group->isMember($user));
    }
    
    public function test_secret_group_not_visible_to_non_members()
    {
        $creator = User::factory()->create();
        $nonMember = User::factory()->create();
        
        $group = Group::factory()->secret()->create([
            'creator_id' => $creator->id,
        ]);
        
        // Adicionar o criador como membro admin
        $group->members()->attach($creator->id, [
            'role' => 'admin',
            'is_approved' => true,
            'joined_at' => now(),
        ]);
        
        // Usuário não membro não deve conseguir acessar o grupo
        $this->actingAs($nonMember);
        $response = $this->get(route('grupos.show', $group->slug));
        $response->assertStatus(403);
    }
    
    public function test_secret_group_visible_to_members()
    {
        $creator = User::factory()->create();
        $member = User::factory()->create();
        
        $group = Group::factory()->secret()->create([
            'creator_id' => $creator->id,
        ]);
        
        // Adicionar o criador como admin
        $group->members()->attach($creator->id, [
            'role' => 'admin',
            'is_approved' => true,
            'joined_at' => now(),
        ]);
        
        // Adicionar outro usuário como membro
        $group->members()->attach($member->id, [
            'role' => 'member',
            'is_approved' => true,
            'joined_at' => now(),
        ]);
        
        // Membro deve conseguir acessar o grupo
        $this->actingAs($member);
        $response = $this->get(route('grupos.show', $group->slug));
        $response->assertStatus(200);
    }
    
    public function test_secret_group_not_listed_for_non_members()
    {
        $creator = User::factory()->create();
        $nonMember = User::factory()->create();
        
        $publicGroup = Group::factory()->public()->create();
        $secretGroup = Group::factory()->secret()->create([
            'creator_id' => $creator->id,
        ]);
        
        // Adicionar o criador como membro admin do grupo secreto
        $secretGroup->members()->attach($creator->id, [
            'role' => 'admin',
            'is_approved' => true,
            'joined_at' => now(),
        ]);
        
        // Usuário não membro não deve ver o grupo secreto na listagem
        $this->actingAs($nonMember);
        $response = $this->get(route('grupos.index'));
        
        $response->assertStatus(200);
        $response->assertSee($publicGroup->name);
        $response->assertDontSee($secretGroup->name);
    }
    
    public function test_guest_cannot_access_secret_group()
    {
        $creator = User::factory()->create();
        
        $group = Group::factory()->secret()->create([
            'creator_id' => $creator->id,
        ]);
        
        // Usuário não autenticado não deve conseguir acessar o grupo
        $response = $this->get(route('grupos.show', $group->slug));
        $response->assertStatus(404);
    }
}
