<?php

namespace App\Observers;

use App\Models\Order;
use App\Mail\OrderStatusChanged;
use App\Mail\OrderStatusChangedAdminCopy;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class OrderObserver
{
    /**
     * Handle the Order "created" event.
     */
    public function created(Order $order): void
    {
        // Log da criação do pedido
        Log::info('Novo pedido criado', [
            'order_id' => $order->id,
            'user_id' => $order->user_id,
            'status' => $order->status,
            'total' => $order->total
        ]);
    }

    /**
     * Handle the Order "updated" event.
     */
    public function updated(Order $order): void
    {
        // Verificar se o status foi alterado
        if ($order->isDirty('status')) {
            $oldStatus = $order->getOriginal('status');
            $newStatus = $order->status;

            Log::info('Status do pedido alterado', [
                'order_id' => $order->id,
                'user_id' => $order->user_id,
                'old_status' => $oldStatus,
                'new_status' => $newStatus
            ]);

            // Enviar emails de notificação
            $this->sendStatusChangeEmails($order, $oldStatus, $newStatus);
        }
    }

    /**
     * Enviar emails de notificação de mudança de status
     */
    protected function sendStatusChangeEmails(Order $order, string $oldStatus, string $newStatus): void
    {
        try {
            $user = $order->user;

            if (!$user) {
                Log::warning('Usuário não encontrado para o pedido', ['order_id' => $order->id]);
                return;
            }

            // Email para o usuário
            Mail::to($user->email)->send(new OrderStatusChanged($order, $user, $oldStatus, $newStatus));

            // Email para administradores
            $adminEmails = $this->getAdminEmails();
            foreach ($adminEmails as $adminEmail) {
                Mail::to($adminEmail)->send(new OrderStatusChangedAdminCopy($order, $user, $oldStatus, $newStatus));
            }

            Log::info('Emails de mudança de status enviados', [
                'order_id' => $order->id,
                'user_email' => $user->email,
                'admin_emails' => $adminEmails,
                'old_status' => $oldStatus,
                'new_status' => $newStatus
            ]);
        } catch (\Exception $e) {
            Log::error('Erro ao enviar emails de mudança de status', [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Obter emails dos administradores
     */
    protected function getAdminEmails(): array
    {
        return [
            '<EMAIL>'
        ];
    }

    /**
     * Handle the Order "deleted" event.
     */
    public function deleted(Order $order): void
    {
        Log::info('Pedido deletado', [
            'order_id' => $order->id,
            'user_id' => $order->user_id
        ]);
    }

    /**
     * Handle the Order "restored" event.
     */
    public function restored(Order $order): void
    {
        Log::info('Pedido restaurado', [
            'order_id' => $order->id,
            'user_id' => $order->user_id
        ]);
    }

    /**
     * Handle the Order "force deleted" event.
     */
    public function forceDeleted(Order $order): void
    {
        Log::info('Pedido deletado permanentemente', [
            'order_id' => $order->id,
            'user_id' => $order->user_id
        ]);
    }
}
