<?php
/**
 * Script para gerar lista de arquivos no servidor FTP
 * Execute acessando: https://www.swingcuritiba.com.br/generate-ftp-file-list.php
 * 
 * IMPORTANTE: Delete este arquivo após usar!
 */

// Verificar se está no ambiente correto
if (!file_exists('../artisan')) {
    die('❌ Erro: Execute no diretório public do Laravel');
}

// Mudar para o diretório raiz do Laravel
chdir('..');

// Verificar se é o ambiente de produção
if (!str_contains($_SERVER['HTTP_HOST'] ?? '', 'swingcuritiba.com.br')) {
    die('❌ Este script só pode ser executado na produção');
}

echo "<h1>🔍 Gerando Lista de Arquivos do FTP</h1>\n";
echo "<p>Aguarde, isso pode levar alguns minutos...</p>\n";

// Flush output para mostrar progresso
if (ob_get_level()) {
    ob_end_flush();
}
flush();

$files = [];
$basePath = getcwd();

// Diretórios a serem excluídos
$excludeDirs = ['.git', 'vendor', 'node_modules', 'storage/logs', 'storage/framework'];

echo "<p>📂 Escaneando diretórios...</p>\n";
flush();

scanDirectory($basePath, $files, $excludeDirs);

// Ordenar por caminho
ksort($files);

// Gerar conteúdo do arquivo
$content = "# Lista de Arquivos FTP - " . date('Y-m-d H:i:s') . "\n";
$content .= "# Servidor: " . ($_SERVER['HTTP_HOST'] ?? 'unknown') . "\n";
$content .= "# Total de arquivos: " . count($files) . "\n\n";

foreach ($files as $relativePath => $info) {
    $content .= sprintf(
        "%s | %s | %s\n",
        $info['modified'],
        $info['size'],
        $relativePath
    );
}

// Salvar arquivo
$outputFile = 'ftp-file-list.txt';
file_put_contents($outputFile, $content);

echo "<h2>✅ Lista Gerada!</h2>\n";
echo "<p>📊 Total de arquivos: " . count($files) . "</p>\n";
echo "<p>📄 Arquivo salvo: <a href='/{$outputFile}' target='_blank'>{$outputFile}</a></p>\n";
echo "<p>⚠️ <strong>IMPORTANTE:</strong> Delete este script após usar!</p>\n";

function scanDirectory($directory, &$files, $excludeDirs, $basePath = null)
{
    if ($basePath === null) {
        $basePath = $directory;
    }
    
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS),
        RecursiveIteratorIterator::LEAVES_ONLY
    );
    
    foreach ($iterator as $file) {
        $fullPath = $file->getRealPath();
        $relativePath = str_replace($basePath . DIRECTORY_SEPARATOR, '', $fullPath);
        $relativePath = str_replace('\\', '/', $relativePath);
        
        // Verificar se está em um diretório excluído
        $skip = false;
        foreach ($excludeDirs as $excludeDir) {
            if (str_starts_with($relativePath, $excludeDir . '/')) {
                $skip = true;
                break;
            }
        }
        
        if ($skip) {
            continue;
        }
        
        $files[$relativePath] = [
            'modified' => date('Y-m-d H:i:s', $file->getMTime()),
            'size' => formatBytes($file->getSize()),
            'path' => $fullPath
        ];
    }
}

function formatBytes($size)
{
    $units = ['B', 'KB', 'MB', 'GB'];
    $unitIndex = 0;
    
    while ($size >= 1024 && $unitIndex < count($units) - 1) {
        $size /= 1024;
        $unitIndex++;
    }
    
    return round($size, 2) . ' ' . $units[$unitIndex];
}
?>
