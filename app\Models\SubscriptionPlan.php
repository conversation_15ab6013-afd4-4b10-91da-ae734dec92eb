<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SubscriptionPlan extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'stripe_product_id',
        'stripe_price_id',
        'price',
        'currency',
        'interval',
        'interval_count',
        'trial_period_days',
        'features',
        'is_active',
        'sort_order',
        'recommended',
        'popular',
        'max_users',
        'benefits'
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'trial_period_days' => 'integer',
        'interval_count' => 'integer',
        'features' => 'array',
        'benefits' => 'array',
        'is_active' => 'boolean',
        'recommended' => 'boolean',
        'popular' => 'boolean',
        'max_users' => 'integer',
        'sort_order' => 'integer',
    ];

    /**
     * Intervalos disponíveis para assinaturas
     */
    const INTERVAL_MONTH = 'month';
    const INTERVAL_YEAR = 'year';
    const INTERVAL_WEEK = 'week';
    const INTERVAL_DAY = 'day';

    /**
     * Tipos de planos baseados no role do usuário
     */
    const TYPE_VISITOR = 'visitante';
    const TYPE_VIP = 'vip';
    const TYPE_ADMIN = 'administrador';

    /**
     * Scope para planos ativos
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope para planos ordenados
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('price');
    }

    /**
     * Scope para planos recomendados
     */
    public function scopeRecommended($query)
    {
        return $query->where('recommended', true);
    }

    /**
     * Scope para planos populares
     */
    public function scopePopular($query)
    {
        return $query->where('popular', true);
    }

    /**
     * Retorna o preço formatado
     */
    public function getFormattedPriceAttribute()
    {
        return 'R$ ' . number_format($this->price, 2, ',', '.');
    }

    /**
     * Retorna a descrição do intervalo
     */
    public function getIntervalDescriptionAttribute()
    {
        $intervals = [
            'day' => 'diário',
            'week' => 'semanal',
            'month' => 'mensal',
            'year' => 'anual',
        ];

        $description = $intervals[$this->interval] ?? $this->interval;
        
        if ($this->interval_count > 1) {
            return $this->interval_count . ' ' . $description;
        }

        return $description;
    }

    /**
     * Retorna o preço por mês para comparação
     */
    public function getMonthlyPriceAttribute()
    {
        switch ($this->interval) {
            case 'year':
                return $this->price / (12 * $this->interval_count);
            case 'month':
                return $this->price / $this->interval_count;
            case 'week':
                return ($this->price * 4.33) / $this->interval_count; // 4.33 semanas por mês
            case 'day':
                return ($this->price * 30) / $this->interval_count;
            default:
                return $this->price;
        }
    }

    /**
     * Retorna o desconto em relação ao plano mensal
     */
    public function getDiscountPercentageAttribute()
    {
        if ($this->interval === 'month' && $this->interval_count === 1) {
            return 0;
        }

        // Busca o plano mensal base para comparação
        $monthlyPlan = static::where('interval', 'month')
            ->where('interval_count', 1)
            ->where('is_active', true)
            ->first();

        if (!$monthlyPlan) {
            return 0;
        }

        $monthlyPrice = $monthlyPlan->price;
        $thisMonthlyPrice = $this->monthly_price;

        if ($monthlyPrice <= 0) {
            return 0;
        }

        return round((($monthlyPrice - $thisMonthlyPrice) / $monthlyPrice) * 100);
    }

    /**
     * Verifica se o plano tem trial
     */
    public function hasTrialAttribute()
    {
        return $this->trial_period_days > 0;
    }

    /**
     * Retorna as assinaturas ativas deste plano
     */
    public function activeSubscriptions()
    {
        return $this->hasMany(VipSubscription::class, 'plan_id')
            ->where('status', 'active');
    }

    /**
     * Retorna todas as assinaturas deste plano
     */
    public function subscriptions()
    {
        return $this->hasMany(VipSubscription::class, 'plan_id');
    }

    /**
     * Verifica se o plano está disponível para novos usuários
     */
    public function getIsAvailableAttribute()
    {
        if (!$this->is_active) {
            return false;
        }

        // Verificar limite máximo de usuários se definido
        if ($this->max_users && $this->activeSubscriptions()->count() >= $this->max_users) {
            return false;
        }

        return true;
    }

    /**
     * Retorna o role do usuário que este plano concede
     */
    public function getUserRoleAttribute()
    {
        // Por padrão, planos pagos concedem role VIP
        if ($this->price > 0) {
            return self::TYPE_VIP;
        }

        return self::TYPE_VISITOR;
    }
}
