<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class CheckGalleryDependencies extends Command
{
    protected $signature = 'gallery:check-dependencies';
    protected $description = 'Verifica se as dependências da galeria estão disponíveis';

    public function handle()
    {
        $this->info('🔍 Verificando dependências da Galeria de Mídia...');
        $this->newLine();

        // Verificar GD
        if (extension_loaded('gd')) {
            $this->info('✅ Extensão GD: Disponível');
            $gdInfo = gd_info();
            $this->line('   - Versão: ' . $gdInfo['GD Version']);
            $this->line('   - JPEG: ' . ($gdInfo['JPEG Support'] ? 'Sim' : 'Não'));
            $this->line('   - PNG: ' . ($gdInfo['PNG Support'] ? 'Sim' : 'Não'));
            $this->line('   - GIF: ' . ($gdInfo['GIF Read Support'] ? 'Sim' : 'Não'));
        } else {
            $this->error('❌ Extensão GD: Não disponível');
            $this->warn('   Thumbnails automáticas não funcionarão. Imagens originais serão usadas.');
        }

        $this->newLine();

        // Verificar diretórios
        $storageDir = storage_path('app/public');
        if (is_writable($storageDir)) {
            $this->info('✅ Diretório de storage: Gravável');
        } else {
            $this->error('❌ Diretório de storage: Não gravável');
            $this->warn('   Configure permissões: chmod 755 ' . $storageDir);
        }

        // Verificar se o link simbólico existe
        $publicStorage = public_path('storage');
        if (is_link($publicStorage) && is_dir($publicStorage)) {
            $this->info('✅ Link simbólico storage: Configurado');
        } else {
            $this->error('❌ Link simbólico storage: Não configurado');
            $this->warn('   Execute: php artisan storage:link');
        }

        $this->newLine();

        // Verificar limites de upload
        $maxFilesize = ini_get('upload_max_filesize');
        $maxPostSize = ini_get('post_max_size');
        $memoryLimit = ini_get('memory_limit');

        $this->info('📊 Limites de Upload:');
        $this->line('   - upload_max_filesize: ' . $maxFilesize);
        $this->line('   - post_max_size: ' . $maxPostSize);
        $this->line('   - memory_limit: ' . $memoryLimit);

        // Converter para bytes para comparação
        $maxFilesizeBytes = $this->convertToBytes($maxFilesize);
        $recommendedSize = 20 * 1024 * 1024; // 20MB

        if ($maxFilesizeBytes >= $recommendedSize) {
            $this->info('✅ Limite de upload adequado para vídeos');
        } else {
            $this->warn('⚠️  Limite de upload pode ser pequeno para vídeos grandes');
            $this->line('   Recomendado: 20M ou mais');
        }

        $this->newLine();

        // Verificar se as tabelas existem
        try {
            if (\Schema::hasTable('albums')) {
                $this->info('✅ Tabela albums: Existe');
            } else {
                $this->error('❌ Tabela albums: Não existe');
                $this->warn('   Execute as migrations ou o SQL de produção');
            }

            if (\Schema::hasTable('album_media')) {
                $this->info('✅ Tabela album_media: Existe');
            } else {
                $this->error('❌ Tabela album_media: Não existe');
                $this->warn('   Execute as migrations ou o SQL de produção');
            }
        } catch (\Exception $e) {
            $this->error('❌ Erro ao verificar tabelas: ' . $e->getMessage());
        }

        $this->newLine();
        $this->info('🎯 Verificação concluída!');

        return 0;
    }

    private function convertToBytes($value)
    {
        $value = trim($value);
        $last = strtolower($value[strlen($value) - 1]);
        $value = (int) $value;

        switch ($last) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }

        return $value;
    }
}
