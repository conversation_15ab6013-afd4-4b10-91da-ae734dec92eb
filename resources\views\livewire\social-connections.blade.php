<div class="space-y-6 overflow-hidden">
    @if(Auth::check() && Auth::id() !== $user->id)


        {{-- Con<PERSON><PERSON><PERSON> --}}
        @if($mutualFollowers->count() > 0 || $mutualFollowing->count() > 0)
        <section class="bg-white dark:bg-zinc-800 rounded-lg shadow-md p-6 overflow-hidden">
            <flux:heading size="lg" class="flex items-center">
                <flux:icon.heart class="w-5 h-5 mr-2 text-red-500" />
                Conexões em Comum
            </flux:heading>
            <flux:text class="mt-2 text-gray-600 dark:text-gray-400">
                Pessoas e grupos que vocês têm em comum
            </flux:text>
            
            {{-- Seguidores Mútuos --}}
            @if($mutualFollowers->count() > 0)
            <div class="mb-6 mt-6">
                <flux:heading size="lg" class="mb-2">
                    Seguidores em comum ({{ $networkStats['mutual_followers_count'] }})
                </flux:heading>
                @if($networkStats['mutual_followers_count'] > 6)
                    <div class="mb-3">
                        <flux:button
                            wire:click="toggleMutualFollowers"
                            variant="ghost"
                            size="sm"
                            class="text-purple-600 hover:text-purple-700"
                        >
                            {{ $showMutualFollowers ? 'Ver menos' : 'Ver todos' }}
                        </flux:button>
                    </div>
                @endif
                
                <div class="flex flex-wrap gap-3">
                    @foreach($mutualFollowers->take($showMutualFollowers ? 20 : 6) as $follower)
                        <div class="flex flex-col items-center group w-16">
                            <a href="/{{ $follower->username }}" wire:navigate class="relative">
                                <img
                                    src="{{ $this->getAvatar($follower) }}"
                                    alt="{{ $follower->name }}"
                                    class="w-12 h-12 rounded-full border-2 border-gray-200 dark:border-gray-600 group-hover:border-purple-500 transition-colors"
                                >
                                <livewire:user-status-indicator :userId="$follower->id" :key="'status-'.$follower->id" />
                            </a>
                            <span class="text-xs text-gray-600 dark:text-gray-400 mt-1 text-center w-full truncate" title="{{ $follower->name }}">
                                {{ $follower->name }}
                            </span>
                        </div>
                    @endforeach
                </div>
            </div>
            @endif

            {{-- Seguindo Mútuos --}}
            @if($mutualFollowing->count() > 0)
            <div class="mb-4">
                <flux:heading size="lg" class="mb-2">
                    Seguindo em comum ({{ $networkStats['mutual_following_count'] }})
                </flux:heading>
                @if($networkStats['mutual_following_count'] > 6)
                    <div class="mb-3">
                        <flux:button
                            wire:click="toggleMutualFollowing"
                            variant="ghost"
                            size="sm"
                            class="text-blue-600 hover:text-blue-700"
                        >
                            {{ $showMutualFollowing ? 'Ver menos' : 'Ver todos' }}
                        </flux:button>
                    </div>
                @endif
                
                <div class="flex flex-wrap gap-3">
                    @foreach($mutualFollowing->take($showMutualFollowing ? 20 : 6) as $following)
                        <div class="flex flex-col items-center group w-16">
                            <a href="/{{ $following->username }}" wire:navigate class="relative">
                                <img
                                    src="{{ $this->getAvatar($following) }}"
                                    alt="{{ $following->name }}"
                                    class="w-12 h-12 rounded-full border-2 border-gray-200 dark:border-gray-600 group-hover:border-blue-500 transition-colors"
                                >
                                <livewire:user-status-indicator :userId="$following->id" :key="'status-'.$following->id" />
                            </a>
                            <span class="text-xs text-gray-600 dark:text-gray-400 mt-1 text-center w-full truncate" title="{{ $following->name }}">
                                {{ $following->name }}
                            </span>
                        </div>
                    @endforeach
                </div>
            </div>
            @endif
        </section>
        @endif

        {{-- Grupos em Comum --}}
        @if($commonGroups->count() > 0)
        <section class="bg-white dark:bg-zinc-800 rounded-lg shadow-md p-6 overflow-hidden">
            <flux:heading size="lg" class="flex items-center">
                <flux:icon.user-group class="w-5 h-5 mr-2 text-green-500" />
                Grupos em Comum
            </flux:heading>
            <flux:text class="mt-2 text-gray-600 dark:text-gray-400">
                {{ $networkStats['common_groups_count'] }} grupos que vocês têm em comum
            </flux:text>
            
            <div class="space-y-4 mt-6">
                @foreach($commonGroups->take($showCommonGroups ? 20 : 6) as $group)
                    <div class="p-4 bg-gray-50 dark:bg-zinc-700 rounded-lg hover:bg-gray-100 dark:hover:bg-zinc-600 transition-colors">
                        <div class="flex items-start gap-3">
                            <div class="w-10 h-10 bg-gradient-to-br from-green-400 to-green-600 rounded-lg flex items-center justify-center flex-shrink-0">
                                <flux:icon.user-group class="w-5 h-5 text-white" />
                            </div>
                            <div class="flex-1 min-w-0">
                                <flux:heading size="sm" class="mb-1">
                                    <a href="{{ route('grupos.show', $group->slug) }}" wire:navigate class="hover:text-green-600 dark:hover:text-green-400 transition-colors" title="{{ $group->name }}">
                                        {{ $group->name }}
                                    </a>
                                </flux:heading>
                                <flux:text size="sm" class="text-gray-600 dark:text-gray-400">
                                    {{ $group->members_count }} membros
                                </flux:text>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
            
            @if($networkStats['common_groups_count'] > 6)
                <div class="mt-4 text-center">
                    <flux:button 
                        wire:click="toggleCommonGroups" 
                        variant="ghost" 
                        size="sm"
                        class="text-green-600 hover:text-green-700"
                    >
                        {{ $showCommonGroups ? 'Ver menos' : 'Ver todos os grupos' }}
                    </flux:button>
                </div>
            @endif
        </section>
        @endif

        {{-- Sugestões de Conexão --}}
        @if($suggestedConnections->count() > 0)
        <section class="bg-white dark:bg-zinc-800 rounded-lg shadow-md p-6 overflow-hidden">
            <flux:heading size="lg" class="flex items-center">
                <flux:icon.sparkles class="w-5 h-5 mr-2 text-yellow-500" />
                Pessoas que você pode conhecer
            </flux:heading>
            <flux:text class="mt-2 text-gray-600 dark:text-gray-400">
                Sugestões baseadas em conexões mútuas e interesses em comum
            </flux:text>
            
            <div class="space-y-4 mt-6">
                @foreach($suggestedConnections->take($showSuggestedConnections ? 20 : 6) as $suggestion)
                    <div class="p-4 bg-gray-50 dark:bg-zinc-700 rounded-lg">
                        <div class="flex items-start gap-3">
                            <a href="/{{ $suggestion->username }}" wire:navigate class="relative flex-shrink-0">
                                <img
                                    src="{{ $this->getAvatar($suggestion) }}"
                                    alt="{{ $suggestion->name }}"
                                    class="w-12 h-12 rounded-full border-2 border-gray-200 dark:border-gray-600"
                                >
                                <livewire:user-status-indicator :userId="$suggestion->id" :key="'status-'.$suggestion->id" />
                            </a>
                            <div class="flex-1 min-w-0">
                                <flux:heading size="sm" class="mb-1">
                                    <a href="/{{ $suggestion->username }}" wire:navigate class="hover:text-purple-600 dark:hover:text-purple-400 transition-colors" title="{{ $suggestion->name }}">
                                        {{ $suggestion->name }}
                                    </a>
                                </flux:heading>
                                <flux:text size="sm" class="text-gray-600 dark:text-gray-400 mb-3">
                                    {{ '@' . $suggestion->username }}
                                </flux:text>
                                <flux:button
                                    wire:click="followUser({{ $suggestion->id }})"
                                    variant="primary"
                                    size="sm"
                                    class="bg-purple-600 hover:bg-purple-700 text-white border-none"
                                >
                                    <flux:icon.plus class="w-4 h-4 mr-1" />
                                    Seguir
                                </flux:button>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
            
            @if($suggestedConnections->count() > 6)
                <div class="mt-4 text-center">
                    <flux:button 
                        wire:click="toggleSuggestedConnections" 
                        variant="ghost" 
                        size="sm"
                        class="text-yellow-600 hover:text-yellow-700"
                    >
                        {{ $showSuggestedConnections ? 'Ver menos' : 'Ver mais sugestões' }}
                    </flux:button>
                </div>
            @endif
        </section>
        @endif

        {{-- Mensagem quando não há conexões --}}
        @if($mutualFollowers->count() === 0 && $mutualFollowing->count() === 0 && $commonGroups->count() === 0 && $suggestedConnections->count() === 0)
        <section class="bg-white dark:bg-zinc-800 rounded-lg shadow-md p-6 text-center overflow-hidden">
            <flux:icon.users class="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <flux:heading size="lg" class="mb-2">
                Nenhuma conexão em comum
            </flux:heading>
            <flux:text class="text-gray-600 dark:text-gray-400">
                Vocês ainda não têm seguidores, grupos ou conexões em comum.
            </flux:text>
        </section>
        @endif
    @endif
</div>
