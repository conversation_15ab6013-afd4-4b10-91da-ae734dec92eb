# Correção de Imagens não Aparecem na Produção

## Problema
As imagens dos posts não aparecem na produção (www.swingcuritiba.com.br), mas funcionam corretamente no desenvolvimento local.

## Causa Provável
1. **Link simbólico do storage não existe na produção**
2. **Configuração incorreta do APP_URL**
3. **Permissões de arquivo incorretas**
4. **Servidor web não configurado para servir arquivos do storage**

## Solução Passo a Passo

### 1. Verificar e Corrigir via Linha de Comando

Execute os seguintes comandos no servidor de produção:

```bash
# 1. Verificar problemas de storage
php artisan storage:diagnose

# 2. Corrigir problemas automaticamente
php artisan storage:fix-production

# 3. Criar link simbólico (se necessário)
php artisan storage:link

# 4. Verificar permissões
chmod 755 storage/app/public
chmod 755 public/storage
```

### 2. Verificar Configuração do .env

Certifique-se que o arquivo `.env` na produção contém:

```env
APP_URL=https://www.swingcuritiba.com.br
ASSET_URL=https://www.swingcuritiba.com.br
FILESYSTEM_DISK=public
```

### 3. Verificar Estrutura de Diretórios

A estrutura deve estar assim:

```
public/
├── storage/ (link simbólico para ../storage/app/public)
└── ...

storage/
├── app/
│   └── public/
│       ├── posts/
│       │   ├── images/
│       │   └── videos/
│       ├── avatars/
│       ├── covers/
│       └── ...
└── ...
```

### 4. Configuração do Servidor Web (.htaccess)

O arquivo `public/storage/.htaccess` foi criado automaticamente com as configurações necessárias para servir as imagens corretamente.

### 5. Teste Manual

Para testar se as URLs estão funcionando:

1. Acesse: `https://www.swingcuritiba.com.br/storage/posts/images/[nome-de-uma-imagem].jpg`
2. Se a imagem não carregar, verifique:
   - Se o link simbólico existe: `ls -la public/storage`
   - Se o arquivo existe: `ls -la storage/app/public/posts/images/`

## Comandos de Diagnóstico

### Verificar Status do Storage
```bash
php artisan storage:diagnose --save-report
```

### Corrigir Problemas Automaticamente
```bash
php artisan storage:fix-production
```

### Verificar Logs
```bash
tail -f storage/logs/laravel.log
```

## Melhorias Implementadas

### 1. Helper de Storage (`App\Helpers\StorageHelper`)
- Gera URLs seguras com fallback
- Log de debug em produção
- Verificação de saúde do storage

### 2. Comandos Artisan
- `storage:fix-production`: Corrige problemas automaticamente
- `storage:diagnose`: Gera relatório detalhado

### 3. Componente PostFeed Atualizado
- Usa `StorageHelper` em vez de `Storage::url()` direto
- Exibe mensagem de fallback se imagem não estiver disponível
- Melhor tratamento de erros

### 4. Configuração Apache
- `.htaccess` otimizado para servir imagens
- Headers de cache configurados
- Tipos MIME corretos

## Verificação Final

Após aplicar as correções:

1. **Teste as URLs diretamente:**
   ```
   https://www.swingcuritiba.com.br/storage/posts/images/[arquivo-existente]
   ```

2. **Verifique os logs:**
   ```bash
   tail -f storage/logs/laravel.log | grep -i storage
   ```

3. **Execute diagnóstico:**
   ```bash
   php artisan storage:diagnose
   ```

## Troubleshooting

### Se as imagens ainda não aparecem:

1. **Verificar se o link simbólico existe:**
   ```bash
   ls -la public/ | grep storage
   ```
   Deve mostrar: `storage -> ../storage/app/public`

2. **Verificar permissões:**
   ```bash
   ls -la storage/app/public/
   ls -la public/storage/
   ```

3. **Testar URL direta no navegador:**
   Acesse uma URL de imagem diretamente para ver se retorna erro 404, 403 ou 500.

4. **Verificar logs do servidor web:**
   Consulte os logs do Apache/Nginx para erros relacionados ao diretório storage.

## Contato para Suporte

Se o problema persistir, execute:
```bash
php artisan storage:diagnose --save-report
```

E envie o arquivo de relatório gerado em `storage/logs/` para análise.
