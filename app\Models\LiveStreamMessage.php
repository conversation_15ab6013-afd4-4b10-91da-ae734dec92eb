<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LiveStreamMessage extends Model
{
    use HasFactory;

    protected $fillable = [
        'live_stream_id',
        'user_id',
        'message',
        'type',
        'data',
    ];

    protected $casts = [
        'data' => 'array',
    ];

    /**
     * Live stream que a mensagem pertence
     */
    public function liveStream()
    {
        return $this->belongsTo(LiveStream::class);
    }

    /**
     * Usuário que enviou a mensagem
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Verifica se é uma mensagem normal
     */
    public function isMessage()
    {
        return $this->type === 'message';
    }

    /**
     * Verifica se é uma mensagem de doação
     */
    public function isDonation()
    {
        return $this->type === 'donation';
    }

    /**
     * Verifica se é uma mensagem do sistema
     */
    public function isSystem()
    {
        return $this->type === 'system';
    }

    /**
     * Scope para mensagens normais
     */
    public function scopeMessages($query)
    {
        return $query->where('type', 'message');
    }

    /**
     * Scope para mensagens de doação
     */
    public function scopeDonations($query)
    {
        return $query->where('type', 'donation');
    }

    /**
     * Scope para mensagens do sistema
     */
    public function scopeSystem($query)
    {
        return $query->where('type', 'system');
    }
}
