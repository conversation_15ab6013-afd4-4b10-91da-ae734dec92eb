<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Group;

class CheckSecretGroupAccess
{
    /**
     * Handle an incoming request.
     * Verifica se o usuário tem acesso a grupos secretos
     */
    public function handle(Request $request, Closure $next)
    {
        // Obter o grupo da rota
        $group = $request->route('group');
        
        // Se não há grupo na rota, continuar
        if (!$group) {
            return $next($request);
        }
        
        // Se o grupo não é do tipo Group model, tentar buscar pelo slug
        if (is_string($group)) {
            $group = Group::where('slug', $group)->first();
        }
        
        // Se não encontrou o grupo, retornar 404
        if (!$group) {
            abort(404);
        }
        
        // Se o grupo não é secreto, permitir acesso
        if ($group->privacy !== 'secret') {
            return $next($request);
        }
        
        // Se o grupo é secreto e o usuário não está logado, retornar 404
        if (!Auth::check()) {
            abort(404);
        }
        
        // Se o grupo é secreto e o usuário não é membro, retornar 403
        if (!Auth::user()->isMemberOf($group)) {
            abort(403, 'Você não tem permissão para acessar este grupo secreto.');
        }
        
        return $next($request);
    }
}
