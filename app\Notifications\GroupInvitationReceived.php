<?php

namespace App\Notifications;

use App\Models\GroupInvitation;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class GroupInvitationReceived extends Notification
{
    use Queueable;

    protected $invitation;

    /**
     * Create a new notification instance.
     */
    public function __construct(GroupInvitation $invitation)
    {
        $this->invitation = $invitation;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via($notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable): MailMessage
    {
        $group = $this->invitation->group;
        $inviter = $this->invitation->inviter;

        return (new MailMessage)
            ->subject('Convite para Grupo - ' . $group->name)
            ->view('emails.group-invitation-received', [
                'invitation' => $this->invitation,
                'group' => $group,
                'inviter' => $inviter,
                'user' => $notifiable,
            ]);
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray($notifiable): array
    {
        return [
            'invitation_id' => $this->invitation->id,
            'group_id' => $this->invitation->group_id,
            'group_name' => $this->invitation->group->name,
            'inviter_id' => $this->invitation->invited_by,
            'inviter_name' => $this->invitation->inviter->name,
            'message' => 'Você foi convidado para o grupo ' . $this->invitation->group->name,
        ];
    }
}
