/* Mobile Responsive Improvements */

/* Base mobile viewport settings */
@media (max-width: 640px) {
    
    /* Ensure proper viewport handling */
    html {
        -webkit-text-size-adjust: 100%;
        -ms-text-size-adjust: 100%;
    }
    
    body {
        overflow-x: hidden;
        min-height: 100vh;
        min-height: 100dvh; /* Dynamic viewport height for mobile */
    }
    
    /* Fix sidebar layout on mobile */
    [data-flux-sidebar] {
        width: 280px !important;
        max-width: 85vw !important;
    }
    
    /* Mobile header improvements */
    [data-flux-header] {
        padding: 0.75rem 1rem;
        min-height: 3.5rem;
        flex-wrap: nowrap;
    }
    
    /* Mobile navigation improvements */
    [data-flux-navlist] {
        padding: 0.5rem;
    }
    
    [data-flux-navlist-item] {
        padding: 0.75rem 1rem;
        min-height: 2.75rem;
        font-size: 0.875rem;
    }
    
    /* Mobile dropdown menu improvements */
    [data-flux-menu] {
        max-width: 90vw !important;
        margin: 0.5rem;
    }
    
    /* Mobile profile component */
    [data-flux-profile] {
        padding: 0.5rem;
    }
    
    /* Mobile button improvements */
    [data-flux-button] {
        min-height: 2.5rem;
        padding: 0.625rem 1rem;
        font-size: 0.875rem;
    }
    
    /* Mobile input improvements */
    [data-flux-input],
    [data-flux-textarea] {
        min-height: 2.75rem;
        font-size: 16px; /* Prevent zoom on iOS */
        padding: 0.75rem;
    }
    
    /* Mobile main content area */
    [data-flux-main] {
        padding: 1rem;
        min-height: calc(100vh - 4rem);
        min-height: calc(100dvh - 4rem);
    }
    
    /* Mobile card improvements */
    [data-flux-card] {
        margin: 0.5rem 0;
        border-radius: 0.75rem;
    }
    
    /* Mobile modal improvements */
    [data-flux-modal] {
        margin: 1rem;
        max-width: calc(100vw - 2rem);
        max-height: calc(100vh - 2rem);
        max-height: calc(100dvh - 2rem);
    }
    
    /* Mobile toast notifications */
    [data-flux-toast] {
        margin: 0.5rem;
        max-width: calc(100vw - 1rem);
    }
    
    /* Mobile table improvements */
    [data-flux-table] {
        font-size: 0.875rem;
    }
    
    [data-flux-table] th,
    [data-flux-table] td {
        padding: 0.5rem 0.75rem;
    }
    
    /* Mobile badge improvements */
    [data-flux-badge] {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
    
    /* Mobile icon improvements */
    [data-flux-icon] {
        width: 1.25rem;
        height: 1.25rem;
    }
    
    /* Mobile spacer adjustments */
    [data-flux-spacer] {
        min-width: 0.5rem;
    }
}

/* Tablet responsive improvements */
@media (min-width: 641px) and (max-width: 768px) {
    
    [data-flux-sidebar] {
        width: 320px !important;
    }
    
    [data-flux-main] {
        padding: 1.5rem;
    }
    
    [data-flux-navlist-item] {
        padding: 0.875rem 1.25rem;
        min-height: 3rem;
    }
    
    [data-flux-button] {
        min-height: 2.75rem;
        padding: 0.75rem 1.25rem;
    }
    
    [data-flux-input],
    [data-flux-textarea] {
        min-height: 3rem;
        padding: 0.875rem 1rem;
    }
}

/* Touch device improvements */
@media (hover: none) and (pointer: coarse) {
    
    /* Larger touch targets */
    [data-flux-button],
    [data-flux-navlist-item],
    button,
    a {
        min-height: 2.75rem;
        min-width: 2.75rem;
    }
    
    /* Better touch feedback */
    [data-flux-button]:active,
    [data-flux-navlist-item]:active,
    button:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
    }
    
    /* Remove hover effects on touch devices */
    [data-flux-button]:hover,
    [data-flux-navlist-item]:hover {
        transform: none;
    }
}

/* Landscape mobile improvements */
@media (max-width: 896px) and (orientation: landscape) {
    
    [data-flux-main] {
        padding: 0.75rem;
        min-height: calc(100vh - 3rem);
        min-height: calc(100dvh - 3rem);
    }
    
    [data-flux-header] {
        padding: 0.5rem 1rem;
        min-height: 3rem;
    }
    
    [data-flux-navlist-item] {
        padding: 0.5rem 1rem;
        min-height: 2.5rem;
        font-size: 0.8125rem;
    }
}

/* High DPI display improvements */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    
    [data-flux-icon] {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
    
    /* Sharper borders on high DPI */
    [data-flux-card],
    [data-flux-button],
    [data-flux-input] {
        border-width: 0.5px;
    }
}

/* Accessibility improvements for mobile */
@media (max-width: 640px) {
    
    /* Focus improvements */
    [data-flux-button]:focus,
    [data-flux-navlist-item]:focus,
    [data-flux-input]:focus {
        outline: 2px solid #3B82F6;
        outline-offset: 2px;
    }
    
    /* Text size improvements for readability */
    [data-flux-text] {
        line-height: 1.6;
    }
    
    /* Better contrast for mobile */
    [data-flux-text-muted] {
        color: rgb(107 114 128) !important; /* gray-500 */
    }
    
    .dark [data-flux-text-muted] {
        color: rgb(156 163 175) !important; /* gray-400 */
    }
}

/* Safe area insets for devices with notches */
@supports (padding: max(0px)) {
    
    @media (max-width: 640px) {
        
        [data-flux-header] {
            padding-left: max(1rem, env(safe-area-inset-left));
            padding-right: max(1rem, env(safe-area-inset-right));
            padding-top: max(0.75rem, env(safe-area-inset-top));
        }
        
        [data-flux-main] {
            padding-left: max(1rem, env(safe-area-inset-left));
            padding-right: max(1rem, env(safe-area-inset-right));
            padding-bottom: max(1rem, env(safe-area-inset-bottom));
        }
        
        [data-flux-sidebar] {
            padding-left: max(0.5rem, env(safe-area-inset-left));
            padding-bottom: max(0.5rem, env(safe-area-inset-bottom));
        }
    }
}

/* Prevent horizontal scroll issues */
* {
    box-sizing: border-box;
}

img,
video,
iframe {
    max-width: 100%;
    height: auto;
}

/* Mobile-specific utility classes */
@media (max-width: 640px) {
    
    .mobile-hidden {
        display: none !important;
    }
    
    .mobile-full-width {
        width: 100% !important;
    }
    
    .mobile-text-sm {
        font-size: 0.875rem !important;
    }
    
    .mobile-p-2 {
        padding: 0.5rem !important;
    }
    
    .mobile-m-2 {
        margin: 0.5rem !important;
    }
    
    .mobile-gap-2 {
        gap: 0.5rem !important;
    }
}
