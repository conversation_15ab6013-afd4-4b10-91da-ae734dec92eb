<?php
/**
 * Script para remover APENAS assets antigos do FTP
 * Execute acessando: https://www.swingcuritiba.com.br/remove-assets-only.php
 * 
 * IMPORTANTE: Delete este arquivo após usar!
 */

// Verificar se está no ambiente correto
if (!file_exists('../artisan')) {
    die('❌ Erro: Execute no diretório public do Laravel');
}

// Mudar para o diretório raiz do Laravel
chdir('..');

// Verificar se é o ambiente de produção
if (!str_contains($_SERVER['HTTP_HOST'] ?? '', 'swingcuritiba.com.br')) {
    die('❌ Este script só pode ser executado na produção');
}

$action = $_GET['action'] ?? 'preview';

echo "<h1>🗑️ Remoção de Assets Antigos</h1>\n";
echo "<p><strong>Este script remove APENAS arquivos da pasta public/build/</strong></p>\n";

if ($action === 'preview') {
    echo "<h2>📋 Escaneando pasta public/build/...</h2>\n";
    
    $buildDir = 'public/build';
    $filesToRemove = [];
    
    if (is_dir($buildDir)) {
        $files = glob($buildDir . '/*');
        
        foreach ($files as $file) {
            if (is_file($file)) {
                $filesToRemove[] = $file;
            }
        }
    }
    
    echo "<p><strong>Arquivos encontrados: " . count($filesToRemove) . "</strong></p>\n";
    
    if (count($filesToRemove) > 0) {
        echo "<h3>📄 Lista de arquivos:</h3>\n";
        echo "<ul style='max-height: 400px; overflow-y: auto; border: 1px solid #ccc; padding: 10px;'>\n";
        
        foreach ($filesToRemove as $file) {
            $size = filesize($file);
            $sizeFormatted = formatBytes($size);
            $modified = date('Y-m-d H:i:s', filemtime($file));
            
            echo "<li><strong>" . basename($file) . "</strong> - {$sizeFormatted} - {$modified}</li>\n";
        }
        
        echo "</ul>\n";
        
        echo "<p style='margin: 20px 0;'>\n";
        echo "<a href='?action=remove' style='background: #dc3545; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;'>🗑️ REMOVER TODOS OS ASSETS</a>\n";
        echo "</p>\n";
        
        echo "<p><em>⚠️ Isso vai remover todos os arquivos CSS e JS compilados da pasta public/build/</em></p>\n";
        
    } else {
        echo "<p style='color: green;'>✅ Nenhum asset encontrado para remover!</p>\n";
    }
    
} elseif ($action === 'remove') {
    echo "<h2>🗑️ Removendo assets...</h2>\n";
    
    $buildDir = 'public/build';
    $removed = 0;
    $errors = 0;
    
    if (is_dir($buildDir)) {
        $files = glob($buildDir . '/*');
        
        echo "<p>Processando " . count($files) . " arquivos...</p>\n";
        
        foreach ($files as $file) {
            if (is_file($file)) {
                $fileName = basename($file);
                
                if (unlink($file)) {
                    echo "<p style='color: green'>✅ Removido: {$fileName}</p>\n";
                    $removed++;
                } else {
                    echo "<p style='color: red'>❌ Erro ao remover: {$fileName}</p>\n";
                    $errors++;
                }
                
                // Flush output para mostrar progresso
                if (ob_get_level()) {
                    ob_flush();
                }
                flush();
            }
        }
        
        // Tentar remover o manifest.json se existir
        $manifestFile = $buildDir . '/manifest.json';
        if (file_exists($manifestFile)) {
            if (unlink($manifestFile)) {
                echo "<p style='color: green'>✅ Removido: manifest.json</p>\n";
                $removed++;
            } else {
                echo "<p style='color: red'>❌ Erro ao remover: manifest.json</p>\n";
                $errors++;
            }
        }
    }
    
    echo "<h2>📊 Resultado:</h2>\n";
    echo "<p style='font-size: 18px;'><strong>✅ Removidos: {$removed}</strong></p>\n";
    echo "<p style='font-size: 18px;'><strong>❌ Erros: {$errors}</strong></p>\n";
    
    if ($errors === 0) {
        echo "<p style='color: green; font-size: 20px; font-weight: bold;'>🎉 Todos os assets foram removidos com sucesso!</p>\n";
        echo "<p><strong>Próximos passos:</strong></p>\n";
        echo "<ol>\n";
        echo "<li>Faça upload dos novos assets do seu projeto local</li>\n";
        echo "<li>Ou execute 'npm run build' no servidor</li>\n";
        echo "<li>Delete este script!</li>\n";
        echo "</ol>\n";
    } else {
        echo "<p style='color: orange;'>⚠️ Alguns arquivos não puderam ser removidos. Verifique as permissões.</p>\n";
    }
}

echo "<hr>\n";
echo "<p><small>Gerado em: " . date('Y-m-d H:i:s') . "</small></p>\n";

function formatBytes($size) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $unitIndex = 0;
    
    while ($size >= 1024 && $unitIndex < count($units) - 1) {
        $size /= 1024;
        $unitIndex++;
    }
    
    return round($size, 2) . ' ' . $units[$unitIndex];
}
?>
