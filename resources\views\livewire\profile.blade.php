<?php
use Illuminate\Support\Facades\Auth;
?>

<div wire:poll.300s="refreshStatus" x-data="{ showCoverModal: false, showAvatarModal: false }"> {{-- Atualiza status a cada 5 minutos (300s) --}}
    <livewire:user-images />
    <livewire:user-videos />
    <livewire:user-following />
    <livewire:user-followers />
    <livewire:user-posts />

    {{-- Profile header  --}}
    <div
        x-data="{ show: false }"
        x-init="setTimeout(() => show = true, 200)"
        x-transition:enter="transition ease-out duration-700"
        x-transition:enter-start="opacity-0 scale-90"
        x-transition:enter-end="opacity-100 scale-100"
        x-show="show"
        class="relative w-full bg-white dark:bg-zinc-800 shadow-lg rounded-lg overflow-hidden">

        <div id="profile_header" class="relative w-full group border border-zinc-300">
            {{-- Foto de capa Background cover --}}
            <div class="w-full h-64 sm:h-80 bg-cover bg-center relative cursor-pointer hover:brightness-110 transition-all duration-300"
                 style="background-image: url('{{ $this->cover() ?? asset('images/users/capa.svg') }}');"
                 @click="showCoverModal = true"
                 title="Clique para ampliar a capa">
                @if($this->isOwner())
                    <!-- Botão para editar capa -->
                    <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <flux:button icon="photo"
                            href="{{ route('settings.profile-with-cover') }}"
                            wire:navigate
                            variant="primary"
                            size="sm"
                            class="text-white bg-black bg-opacity-50 hover:bg-opacity-70 border-none shadow-lg backdrop-blur-sm"
                            @click.stop
                        >
                            <span class="hidden sm:inline">Editar Capa</span>
                            <span class="sm:hidden">Capa</span>
                        </flux:button>
                    </div>
                @endif
            </div>

            <!-- Foto de perfil centralizada -->
            <div class="absolute left-1/2 -translate-x-1/2 -bottom-16 sm:-bottom-20">
                <div class="relative w-32 h-32 sm:w-40 sm:h-40 rounded-full border-4 border-white overflow-hidden shadow-xl group bg-white cursor-pointer hover:scale-105 transition-all duration-300"
                     @click="showAvatarModal = true"
                     title="Clique para ampliar a foto de perfil">
                    @php
                        $rankingBorderClass = \App\Helpers\RankingHelper::getRankingBorderClass($user);
                    @endphp
                    <div class="ranking-avatar {{ $rankingBorderClass }} w-full h-full">
                        <img src="{{ $this->avatar() ?? asset('images/default-avatar.jpg') }}" class="w-full h-full object-cover rounded-full" />
                    </div>
                    <livewire:user-status-indicator :userId="$user->id" />

                    @if($this->isOwner())
                        <!-- Overlay para editar foto -->
                        <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center rounded-full"
                             @click.stop>
                            <flux:button
                                href="{{ route('settings.profile-with-avatar') }}"
                                icon="camera"
                                wire:navigate
                                variant="primary"
                                size="sm"
                                class="text-white border-none bg-transparent hover:bg-white hover:bg-opacity-20"
                            >
                                <span class="hidden sm:inline">Foto</span>
                                <flux:icon name="camera" class="sm:hidden w-5 h-5" />
                            </flux:button>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Informações do usuário centralizadas -->
        <div class="pt-20 sm:pt-24 pb-6 text-center">
            <h2 class="text-2xl sm:text-3xl font-bold text-zinc-900 dark:text-white mb-2">{{ $user->name }}</h2>
            <a href="/{{ $user->username }}" class="text-lg text-zinc-600 dark:text-zinc-400 hover:text-zinc-900 dark:hover:text-white transition-colors">
                {{ '@'. $user->username }}
            </a>

            @if($user->level)
                <div class="text-sm font-semibold text-zinc-700 dark:text-zinc-300 mt-2">
                    Nível: {{ $user->level->level }}
                </div>
            @endif

            {{-- Componente de status do usuário --}}
            <div class="mt-3">
                <livewire:user-status-manager :user="$user" />
            </div>

            @if($this->isOwner())
                <!-- Botão para editar perfil centralizado -->
                <div class="mt-4">
                    <flux:button
                        href="{{ route('settings.profile') }}"
                        icon="pencil"
                        wire:navigate
                        variant="primary"
                        size="sm"
                        class="bg-zinc-800 hover:bg-zinc-700 text-white border-none shadow-lg"
                    >
                        Editar Perfil
                    </flux:button>
                </div>
            @endif
        </div>

        <div id="profile_navigation" class="border-t border-gray-200 dark:border-gray-700 px-3 sm:px-6 py-3 mt-4 text-sm text-body">
            <!-- Mobile: Grid simples e direto -->
            <div class="block sm:hidden">
                <!-- Grid 2x2 para todos os casos -->
                <div class="grid grid-cols-2 gap-3 mb-4">
                    <!-- Botão Fotos -->
                    <button wire:click="showUserImages" class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg p-4 flex flex-col items-center hover:bg-zinc-50 dark:hover:bg-zinc-700 transition-all">
                        <span class="text-2xl font-bold text-zinc-900 dark:text-white mb-1">{{ $this->imagesCount() }}</span>
                        <span class="text-xs text-zinc-600 dark:text-zinc-400 font-medium">Fotos</span>
                    </button>

                    <!-- Botão Vídeos -->
                    <button wire:click="showUserVideos" class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg p-4 flex flex-col items-center hover:bg-zinc-50 dark:hover:bg-zinc-700 transition-all">
                        <span class="text-2xl font-bold text-zinc-900 dark:text-white mb-1">{{ $this->videosCount() }}</span>
                        <span class="text-xs text-zinc-600 dark:text-zinc-400 font-medium">Vídeos</span>
                    </button>

                    <!-- Botão Seguindo -->
                    <button wire:click="showUserFollowing" class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg p-4 flex flex-col items-center hover:bg-zinc-50 dark:hover:bg-zinc-700 transition-all">
                        <span class="text-2xl font-bold text-zinc-900 dark:text-white mb-1">{{ $this->followingCount() }}</span>
                        <span class="text-xs text-zinc-600 dark:text-zinc-400 font-medium">Seguindo</span>
                    </button>

                    <!-- Botão Seguidores -->
                    <button wire:click="showUserFollowers" class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg p-4 flex flex-col items-center hover:bg-zinc-50 dark:hover:bg-zinc-700 transition-all">
                        <span class="text-2xl font-bold text-zinc-900 dark:text-white mb-1">{{ $this->followersCount() }}</span>
                        <span class="text-xs text-zinc-600 dark:text-zinc-400 font-medium">Seguidores</span>
                    </button>

                    <!-- Botão Posts -->
                    <button wire:click="showUserPosts" class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg p-4 flex flex-col items-center hover:bg-zinc-50 dark:hover:bg-zinc-700 transition-all">
                        <span class="text-2xl font-bold text-zinc-900 dark:text-white mb-1">{{ $this->postsCount() }}</span>
                        <span class="text-xs text-zinc-600 dark:text-zinc-400 font-medium">Posts</span>
                    </button>

                    <!-- Botão Charme -->
                    <button wire:click="showSendCharm" class="bg-gradient-to-br from-pink-500 to-purple-600 text-white rounded-lg p-4 flex flex-col items-center hover:from-pink-600 hover:to-purple-700 transition-all shadow-lg">
                        <svg class="w-6 h-6 mb-1" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                        <span class="text-xs font-medium">Charme</span>
                    </button>
                </div>

                @if($user->id !== Auth::id())
                    <!-- Botões de ação para outros usuários -->
                    <div class="grid grid-cols-2 gap-3">
                        <button wire:click="toggleFollow({{ $user->id }})" class="{{ $followStatus[$user->id] ? 'bg-red-500 hover:bg-red-600' : 'bg-purple-600 hover:bg-purple-700' }} text-white rounded-lg p-3 flex items-center justify-center transition-all shadow-lg">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                <path d="{{ $followStatus[$user->id] ? 'M16 12v6H8v-6H6v8h12v-8h-2z' : 'M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z' }}"/>
                            </svg>
                            <span class="text-sm font-medium">{{ $followStatus[$user->id] ? 'Parar' : 'Seguir' }}</span>
                        </button>

                        <a href="{{ route('caixa_de_mensagens', ['user' => $user->username]) }}" wire:navigate class="bg-zinc-800 hover:bg-zinc-700 text-white rounded-lg p-3 flex items-center justify-center transition-all shadow-lg">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z"/>
                            </svg>
                            <span class="text-sm font-medium">Mensagem</span>
                        </a>
                    </div>
                @endif
            </div>

            <!-- Desktop: Flex layout -->
            <div class="hidden sm:flex flex-wrap items-center justify-between">
                <div class="flex flex-wrap gap-2">
                    <flux:button.group>
                        <flux:button variant="ghost" icon="photo" wire:click="showUserImages">
                            Imagens ({{ $this->imagesCount() }})
                        </flux:button>

                        <flux:button variant="ghost" icon="video-camera" wire:click="showUserVideos">
                            Vídeos ({{ $this->videosCount() }})
                        </flux:button>

                        <flux:button variant="ghost" icon="users" wire:click="showUserFollowing">
                            Seguindo: {{ $this->followingCount() }}
                        </flux:button>

                        <flux:button variant="ghost" icon="users" wire:click="showUserFollowers">
                            Seguidores: {{ $this->followersCount() }}
                        </flux:button>

                        <flux:button variant="ghost" icon="rss" wire:click="showUserPosts">
                            Postagens: {{ $this->postsCount() }}
                        </flux:button>

                        <flux:button variant="ghost" icon="gift" wire:click="showSendCharm">
                            Enviar Charme
                        </flux:button>
                    </flux:button.group>
                </div>

                @if($user->id !== Auth::id())
                    <div class="flex gap-2 mt-2 sm:mt-0">
                        <flux:button
                            wire:click="toggleFollow({{ $user->id }})"
                            variant="ghost"
                            icon="user-plus"
                            size="sm"
                            class="bg-purple-600 hover:bg-purple-700 text-white border-none"
                        >
                            {{ $followStatus[$user->id] ? 'Deixar de Seguir' : 'Seguir' }}
                        </flux:button>

                        <flux:button
                            href="{{ route('caixa_de_mensagens', ['user' => $user->username]) }}"
                            wire:navigate
                            variant="ghost"
                            size="sm"
                            icon="chat-bubble-left"
                            class="text-white bg-zinc-800 hover:bg-zinc-700 border-none shadow-lg"
                        >
                            Enviar Mensagem
                        </flux:button>
                    </div>
                @endif
            </div>
        </div>
    </div>
    <!-- Layout responsivo: coluna única no mobile, duas colunas no desktop -->
    <div class="flex flex-col lg:flex-row gap-6 mt-6">
        <!-- Sidebar: Informações do usuário -->
        <div class="w-full lg:w-1/3 order-2 lg:order-1">
           <livewire:profile-progress-bar :username="$user->username" />

           <!-- Sistema de Abas para Mobile -->
           <div class="mt-6" x-data="{ activeTab: 'sobre' }">
               <!-- Navegação das Abas -->
               <div class="flex flex-wrap gap-1 p-1 bg-zinc-100 dark:bg-zinc-800 rounded-lg mb-4">
                   <button @click="activeTab = 'sobre'"
                           :class="activeTab === 'sobre' ? 'bg-white dark:bg-zinc-700 text-zinc-900 dark:text-white shadow-sm' : 'text-zinc-600 dark:text-zinc-400 hover:text-zinc-900 dark:hover:text-white'"
                           class="flex-1 px-3 py-2 text-sm font-medium rounded-md transition-all duration-200">
                       <span class="hidden sm:inline">Sobre</span>
                       <span class="sm:hidden">Info</span>
                   </button>
                   <button @click="activeTab = 'atividade'"
                           :class="activeTab === 'atividade' ? 'bg-white dark:bg-zinc-700 text-zinc-900 dark:text-white shadow-sm' : 'text-zinc-600 dark:text-zinc-400 hover:text-zinc-900 dark:hover:text-white'"
                           class="flex-1 px-3 py-2 text-sm font-medium rounded-md transition-all duration-200">
                       <span class="hidden sm:inline">Atividade</span>
                       <span class="sm:hidden">Stats</span>
                   </button>
                   <button @click="activeTab = 'midia'"
                           :class="activeTab === 'midia' ? 'bg-white dark:bg-zinc-700 text-zinc-900 dark:text-white shadow-sm' : 'text-zinc-600 dark:text-zinc-400 hover:text-zinc-900 dark:hover:text-white'"
                           class="flex-1 px-3 py-2 text-sm font-medium rounded-md transition-all duration-200">
                       Mídia
                   </button>
                   <button @click="activeTab = 'social'"
                           :class="activeTab === 'social' ? 'bg-white dark:bg-zinc-700 text-zinc-900 dark:text-white shadow-sm' : 'text-zinc-600 dark:text-zinc-400 hover:text-zinc-900 dark:hover:text-white'"
                           class="flex-1 px-3 py-2 text-sm font-medium rounded-md transition-all duration-200">
                       Social
                   </button>
               </div>

               <!-- Conteúdo das Abas -->
               <div class="min-h-[400px]">
                   <!-- Aba Sobre -->
                   <div x-show="activeTab === 'sobre'" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0 transform translate-y-2" x-transition:enter-end="opacity-100 transform translate-y-0">
                       <section id="additional-info">
                           <h3 class="text-lg font-semibold text-title mb-4">Informações Pessoais</h3>
                           <div class="flex flex-col gap-4">
                               <div class="space-y-2">
                                   <flux:text>
                                       <strong>Sexo:</strong> {{ $user->sexo ?? 'Não especificado' }}
                                   </flux:text>
                                   <flux:text>
                                       <strong>Aniversário:</strong> {{ $user->aniversario ? $user->aniversario->format('d/m/Y') : 'Não especificado' }}
                                   </flux:text>
                                   <flux:text>
                                       <strong>Localização:</strong> {{ $user->city->name ?? 'Não especificado' }}
                                   </flux:text>
                                   <flux:text>
                                       <strong>Sobre mim:</strong> {{ $user->bio ?? 'Não especificado' }}
                                   </flux:text>
                               </div>

                               <div>
                                   <h4 class="text-md font-semibold mb-2 text-gray-300">Interesses:</h4>
                                   <div class="flex flex-wrap gap-2">
                                       @foreach($user->hobbies as $hobby)
                                           <flux:badge>{{ $hobby->nome }}</flux:badge>
                                       @endforeach
                                   </div>
                               </div>

                               <div>
                                   <h4 class="text-md font-semibold mb-2 text-gray-300">Procuro por:</h4>
                                   <div class="flex flex-wrap gap-2">
                                       @foreach($user->procuras as $procura)
                                           <flux:badge>{{ $procura->nome }}</flux:badge>
                                       @endforeach
                                   </div>
                               </div>
                           </div>
                       </section>

                       {{-- Seção de Conquistas --}}
                       @if ($user->achievements->count() > 0)
                       <section id="achievements" class="mt-6">
                           <h3 class="text-lg font-semibold text-title mb-4">Conquistas</h3>
                           <div class="flex flex-wrap gap-2">
                               @foreach ($user->achievements as $achievement)
                                   <x-flux::badge icon="{{ $achievement->icon ?? 'star' }}" color="primary" outline>
                                       {{ $achievement->name }}
                                   </x-flux::badge>
                               @endforeach
                           </div>
                       </section>
                       @endif
                   </div>

                   <!-- Aba Atividade -->
                   <div x-show="activeTab === 'atividade'" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0 transform translate-y-2" x-transition:enter-end="opacity-100 transform translate-y-0">
                       <!-- Ranking Section -->
                       <section id="ranking">
                           <h3 class="text-lg font-semibold text-title mb-4">Ranking</h3>
                           <livewire:leaderboard />
                       </section>

                       <section id="online-stats" class="mt-6">
                           <h3 class="text-lg font-semibold text-title mb-4">Estatísticas Online</h3>
                           <livewire:user-online-stats :user="$user" />
                       </section>
                   </div>

                   <!-- Aba Mídia -->
                   <div x-show="activeTab === 'midia'" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0 transform translate-y-2" x-transition:enter-end="opacity-100 transform translate-y-0">
                       {{-- GALERIA DE MÍDIA EM DESTAQUE --}}
                       <section id="media-gallery-featured">
                           <h3 class="text-lg font-semibold text-title mb-4">Mídia em Destaque</h3>
                           <livewire:media-gallery-featured :user="$user" />
                       </section>

                       {{-- Seção de Galeria de Mídia Completa --}}
                       <section id="media-gallery" class="mt-6">
                           <h3 class="text-lg font-semibold text-title mb-4">Galeria Completa</h3>
                           <livewire:media-gallery :user="$user" />
                       </section>
                   </div>

                   <!-- Aba Social -->
                   <div x-show="activeTab === 'social'" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0 transform translate-y-2" x-transition:enter-end="opacity-100 transform translate-y-0">
                       {{-- Seção de Conexões Sociais --}}
                       <section id="social-connections">
                           <h3 class="text-lg font-semibold text-title mb-4">Conexões Sociais</h3>
                           <livewire:social-connections :user="$user" />
                       </section>

                       {{-- Seção de Visualização da Rede --}}
                       <section id="network-visualization" class="mt-6">
                           <h3 class="text-lg font-semibold text-title mb-4">Visualização da Rede</h3>
                           <livewire:network-visualization :user="$user" />
                       </section>
                   </div>
               </div>
           </div>
        </div>

        <!-- Conteúdo principal: Posts -->
        <div class="w-full lg:w-2/3 order-1 lg:order-2">
            <section id="create-post">
                <livewire:create-post />
                <livewire:postfeed />
            </section>
        </div>
    </div>

    {{-- Botão Flutuante para Galeria (apenas para o próprio usuário) --}}
    @if(Auth::check() && Auth::id() === $user->id)
        <div class="fixed bottom-6 right-6 z-40">
            <div class="relative group">
                {{-- Botão Principal --}}
                <button onclick="document.getElementById('media-gallery-featured').scrollIntoView({ behavior: 'smooth' })"
                        class="w-14 h-14 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center group-hover:scale-110">
                    <flux:icon.photo class="w-6 h-6" />
                </button>

                {{-- Tooltip --}}
                <div class="absolute bottom-full right-0 mb-2 px-3 py-2 bg-black text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                    Ir para Galeria
                    <div class="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-black"></div>
                </div>
            </div>
        </div>
    @endif

    <!-- Modal para Capa Ampliada -->
    <div x-show="showCoverModal"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-90 p-4"
         @click="showCoverModal = false"
         @keydown.escape.window="showCoverModal = false"
         style="display: none;">

        <div class="relative max-w-6xl max-h-full w-full h-full flex items-center justify-center">
            <!-- Botão Fechar -->
            <button @click="showCoverModal = false"
                    class="absolute top-4 right-4 z-10 w-10 h-10 bg-black bg-opacity-50 hover:bg-opacity-70 text-white rounded-full flex items-center justify-center transition-all duration-200 backdrop-blur-sm">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>

            <!-- Imagem da Capa -->
            <img src="{{ $this->cover() ?? asset('images/users/capa.svg') }}"
                 class="max-w-full max-h-full object-contain rounded-lg shadow-2xl"
                 @click.stop
                 alt="Capa de {{ $user->name }}">

            <!-- Informações da Imagem -->
            <div class="absolute bottom-4 left-4 bg-black bg-opacity-50 text-white p-3 rounded-lg backdrop-blur-sm">
                <h3 class="font-semibold">Capa de {{ $user->name }}</h3>
                @if($this->isOwner())
                    <a href="{{ route('settings.profile-with-cover') }}"
                       wire:navigate
                       class="text-sm text-blue-300 hover:text-blue-200 transition-colors">
                        Editar capa
                    </a>
                @endif
            </div>
        </div>
    </div>

    <!-- Modal para Avatar Ampliado -->
    <div x-show="showAvatarModal"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 scale-90"
         x-transition:enter-end="opacity-100 scale-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100 scale-100"
         x-transition:leave-end="opacity-0 scale-90"
         class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-90 p-4"
         @click="showAvatarModal = false"
         @keydown.escape.window="showAvatarModal = false"
         style="display: none;">

        <div class="relative max-w-2xl max-h-full w-full h-full flex items-center justify-center">
            <!-- Botão Fechar -->
            <button @click="showAvatarModal = false"
                    class="absolute top-4 right-4 z-10 w-10 h-10 bg-black bg-opacity-50 hover:bg-opacity-70 text-white rounded-full flex items-center justify-center transition-all duration-200 backdrop-blur-sm">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>

            <!-- Container da Foto de Perfil -->
            <div class="relative">
                @php
                    $rankingBorderClass = \App\Helpers\RankingHelper::getRankingBorderClass($user);
                @endphp
                <div class="ranking-avatar {{ $rankingBorderClass }} w-80 h-80 sm:w-96 sm:h-96">
                    <img src="{{ $this->avatar() ?? asset('images/default-avatar.jpg') }}"
                         class="w-full h-full object-cover rounded-full shadow-2xl"
                         @click.stop
                         alt="Foto de perfil de {{ $user->name }}">
                </div>

                <!-- Status Indicator Ampliado -->
                <div class="absolute bottom-4 right-4">
                    <livewire:user-status-indicator :userId="$user->id" />
                </div>
            </div>

            <!-- Informações da Imagem -->
            <div class="absolute bottom-4 left-1/2 -translate-x-1/2 bg-black bg-opacity-50 text-white p-3 rounded-lg backdrop-blur-sm text-center">
                <h3 class="font-semibold">{{ $user->name }}</h3>
                <p class="text-sm text-gray-300">{{ '@' . $user->username }}</p>
                @if($this->isOwner())
                    <a href="{{ route('settings.profile-with-avatar') }}"
                       wire:navigate
                       class="text-sm text-blue-300 hover:text-blue-200 transition-colors block mt-1">
                        Editar foto
                    </a>
                @endif
            </div>
        </div>
    </div>
</div>

