<div class="p-6 bg-white dark:bg-zinc-800 rounded-lg shadow-lg max-w-md mx-auto">
    <h2 class="text-2xl font-bold mb-4 text-gray-900 dark:text-gray-100">Teste Livewire + Alpine.js</h2>

    <!-- <PERSON>e Livewire -->
    <div class="mb-6">
        <h3 class="text-lg font-semibold mb-2 text-gray-800 dark:text-gray-200">Teste Livewire:</h3>
        <p class="mb-2 text-gray-700 dark:text-gray-300">{{ $message }}</p>
        <p class="mb-4 text-gray-700 dark:text-gray-300">Contador: <span class="font-bold text-blue-600">{{ $count }}</span></p>

        <div class="space-x-2">
            <button wire:click="increment" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                + Incrementar
            </button>
            <button wire:click="decrement" class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600">
                - Decrementar
            </button>
        </div>
    </div>

    <!-- Teste Alpine.js -->
    <div x-data="{ alpineCount: 0, alpineMessage: 'Alpine.js funcionando!' }">
        <h3 class="text-lg font-semibold mb-2 text-gray-800 dark:text-gray-200">Teste Alpine.js:</h3>
        <p class="mb-2 text-gray-700 dark:text-gray-300" x-text="alpineMessage"></p>
        <p class="mb-4 text-gray-700 dark:text-gray-300">Contador Alpine: <span class="font-bold text-green-600" x-text="alpineCount"></span></p>

        <div class="space-x-2">
            <button @click="alpineCount++" class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                + Alpine
            </button>
            <button @click="alpineCount--" class="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600">
                - Alpine
            </button>
        </div>
    </div>

    <!-- Teste $wire (Alpine controlando Livewire) -->
    <div class="mt-6" x-data="{}">
        <h3 class="text-lg font-semibold mb-2 text-gray-800 dark:text-gray-200">Teste $wire (Alpine → Livewire):</h3>
        <div class="space-x-2">
            <button @click="$wire.increment()" class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600">
                Alpine incrementa Livewire
            </button>
            <button @click="$wire.count = 0" class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
                Alpine reseta Livewire
            </button>
        </div>
    </div>
</div>
