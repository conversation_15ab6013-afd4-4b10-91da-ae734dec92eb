<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class DebugStorageUrls
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Apenas em produção e para rotas específicas
        if (app()->environment('production') && $this->shouldDebug($request)) {
            $this->logStorageDebugInfo($request);
        }

        return $response;
    }

    private function shouldDebug(Request $request): bool
    {
        // Debug apenas para páginas que exibem posts
        $debugRoutes = [
            '/',
            '/feed',
            '/profile/*',
            '/groups/*'
        ];

        $currentPath = $request->path();
        
        foreach ($debugRoutes as $route) {
            if ($route === $currentPath || 
                (str_ends_with($route, '*') && str_starts_with($currentPath, rtrim($route, '*')))) {
                return true;
            }
        }

        return false;
    }

    private function logStorageDebugInfo(Request $request): void
    {
        try {
            $debugInfo = [
                'url' => $request->fullUrl(),
                'user_agent' => $request->userAgent(),
                'timestamp' => now()->toISOString(),
                'storage_config' => [
                    'app_url' => config('app.url'),
                    'storage_url' => config('filesystems.disks.public.url'),
                    'storage_root' => config('filesystems.disks.public.root'),
                ],
                'server_info' => [
                    'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'N/A',
                    'script_name' => $_SERVER['SCRIPT_NAME'] ?? 'N/A',
                    'http_host' => $_SERVER['HTTP_HOST'] ?? 'N/A',
                    'https' => isset($_SERVER['HTTPS']) ? $_SERVER['HTTPS'] : 'N/A',
                ],
                'storage_checks' => [
                    'public_storage_exists' => is_dir(public_path('storage')),
                    'public_storage_is_link' => is_link(public_path('storage')),
                    'storage_app_public_exists' => is_dir(storage_path('app/public')),
                    'storage_app_public_writable' => is_writable(storage_path('app/public')),
                ]
            ];

            Log::channel('daily')->info('Storage Debug Info', $debugInfo);
        } catch (\Exception $e) {
            Log::error('Erro ao gerar debug info de storage: ' . $e->getMessage());
        }
    }
}
