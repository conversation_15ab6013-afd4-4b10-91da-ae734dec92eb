<div class="max-w-7xl mx-auto p-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-300 mb-2">Meus Álbuns Premium</h1>
            <p class="text-gray-400">Gerencie seus álbuns exclusivos</p>
        </div>
        <flux:button 
            href="{{ route('premium-albums.create') }}"
            color="purple"
        >
            <flux:icon name="plus" class="w-4 h-4 mr-2" />
            Novo Álbum
        </flux:button>
    </div>

    <!-- Filters -->
    <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg class="mb-6"">
        <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.body">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- Search -->
                <div>
                    <flux:label for="search">Buscar</flux:label>
                    <flux:input 
                        wire:model.live="search" 
                        id="search" 
                        placeholder="Nome, descrição ou tags..."
                        class="mt-1"
                    />
                </div>

                <!-- Status Filter -->
                <div>
                    <flux:label for="status">Status</flux:label>
                    <flux:select wire:model.live="status" id="status" class="mt-1">
                        <option value="">Todos</option>
                        <option value="draft">Rascunho</option>
                        <option value="published">Publicado</option>
                        <option value="suspended">Suspenso</option>
                    </flux:select>
                </div>

                <!-- Sort -->
                <div>
                    <flux:label for="sort">Ordenar por</flux:label>
                    <flux:select wire:model.live="sortBy" id="sort" class="mt-1">
                        <option value="created_at">Data de criação</option>
                        <option value="name">Nome</option>
                        <option value="price">Preço</option>
                        <option value="sales_count">Vendas</option>
                        <option value="total_earnings">Ganhos</option>
                    </flux:select>
                </div>
            </div>
        </div>
    </div>

    <!-- Albums Grid -->
    @if($albums->count() > 0)
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
        @foreach($albums as $album)
        <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg overflow-hidden">
            <!-- Album Cover -->
            <div class="relative h-48 bg-zinc-800">
                @if($album->medias->count() > 0)
                    @php $firstMedia = $album->medias->first(); @endphp
                    @if($firstMedia->type === 'photo')
                        <img 
                            src="{{ route('premium-albums.media', [$album, $firstMedia]) }}" 
                            alt="{{ $album->name }}"
                            class="w-full h-full object-cover"
                        />
                    @else
                        <div class="w-full h-full flex items-center justify-center">
                            <flux:icon name="film" class="w-12 h-12 text-gray-400" />
                        </div>
                    @endif
                @else
                    <div class="w-full h-full flex items-center justify-center">
                        <flux:icon name="photo" class="w-12 h-12 text-gray-400" />
                    </div>
                @endif

                <!-- Status Badge -->
                <div class="absolute top-2 right-2">
                    <span class="px-2 py-1 text-xs font-medium rounded-full {{ $this->getStatusBadgeClass($album->status) }}">
                        {{ $this->getStatusText($album->status) }}
                    </span>
                </div>

                <!-- Media Count -->
                <div class="absolute bottom-2 left-2">
                    <span class="bg-black bg-opacity-75 text-white px-2 py-1 text-xs rounded">
                        {{ $album->medias_count }} {{ $album->medias_count === 1 ? 'arquivo' : 'arquivos' }}
                    </span>
                </div>
            </div>

            <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.body class="p-4"">
                <!-- Album Info -->
                <h3 class="font-semibold text-gray-300 mb-2 truncate">{{ $album->name }}</h3>
                
                <div class="space-y-2 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-400">Preço:</span>
                        <span class="text-purple-400 font-medium">R$ {{ number_format($album->price, 2, ',', '.') }}</span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-400">Vendas:</span>
                        <span class="text-gray-300">{{ $album->purchases_count }}</span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-400">Avaliações:</span>
                        <span class="text-gray-300">{{ $album->reviews_count }}</span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-400">Ganhos:</span>
                        <span class="text-green-400 font-medium">R$ {{ number_format($album->total_earnings, 2, ',', '.') }}</span>
                    </div>
                </div>

                <!-- Actions -->
                <div class="flex space-x-2 mt-4">
                    <flux:button 
                        href="{{ route('premium-albums.show', $album) }}"
                        size="sm"
                        variant="outline"
                        class="flex-1"
                    >
                        Ver
                    </flux:button>
                    
                    <flux:button 
                        href="{{ route('premium-albums.edit', $album) }}"
                        size="sm"
                        color="purple"
                        class="flex-1"
                    >
                        Editar
                    </flux:button>
                    
                    @if($album->sales_count == 0)
                    <flux:button 
                        wire:click="confirmDelete({{ $album->id }})"
                        size="sm"
                        color="red"
                        variant="outline"
                    >
                        <flux:icon name="trash" class="w-4 h-4" />
                    </flux:button>
                    @endif
                </div>
            </div>
        </div>
        @endforeach
    </div>

    <!-- Pagination -->
    {{ $albums->links() }}
    @else
    <!-- Empty State -->
    <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg class="text-center py-12"">
        <flux:icon name="photo" class="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-300 mb-2">Nenhum álbum encontrado</h3>
        <p class="text-gray-400 mb-6">
            @if($search || $status)
                Tente ajustar os filtros ou criar um novo álbum.
            @else
                Comece criando seu primeiro álbum premium.
            @endif
        </p>
        <flux:button 
            href="{{ route('premium-albums.create') }}"
            color="purple"
        >
            <flux:icon name="plus" class="w-4 h-4 mr-2" />
            Criar Primeiro Álbum
        </flux:button>
    </div>
    @endif

    <!-- Delete Confirmation Modal -->
    @if($showDeleteModal && $albumToDelete)
    <flux:modal wire:model="showDeleteModal">
        <flux:modal.header>
            <h2 class="text-lg font-semibold text-gray-300">Confirmar Exclusão</h2>
        </flux:modal.header>
        
        <flux:modal.body>
            <p class="text-gray-400 mb-4">
                Tem certeza que deseja excluir o álbum <strong class="text-gray-300">"{{ $albumToDelete->name }}"</strong>?
            </p>
            <p class="text-red-400 text-sm">
                Esta ação não pode ser desfeita. Todos os arquivos serão removidos permanentemente.
            </p>
        </flux:modal.body>
        
        <flux:modal.footer>
            <flux:button wire:click="cancelDelete" variant="outline">
                Cancelar
            </flux:button>
            <flux:button wire:click="deleteAlbum" color="red">
                Excluir Álbum
            </flux:button>
        </flux:modal.footer>
    </flux:modal>
    @endif
</div>
