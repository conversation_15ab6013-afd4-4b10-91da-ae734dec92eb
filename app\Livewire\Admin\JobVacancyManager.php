<?php

namespace App\Livewire\Admin;

use App\Models\JobVacancy;
use App\Models\JobCategory;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\WithPagination;

class JobVacancyManager extends Component
{
    use WithPagination, WithFileUploads;

    // Filtros e paginação
    public $search = '';
    public $sortBy = 'created_at';
    public $sortDirection = 'desc';
    public $perPage = 10;
    public $categoryFilter = '';
    public $statusFilter = 'active';

    // Campos do formulário
    public $jobId;
    public $title;
    public $description;
    public $requirements;
    public $benefits;
    public $categoryId;
    public $contractType = 'CLT';
    public $workMode = 'Presencial';
    public $location;
    public $salaryMin;
    public $salaryMax;
    public $salaryPeriod = 'Mensal';
    public $salaryNegotiable = false;
    public $vacancies = 1;
    public $applicationDeadline;
    public $experienceLevel = 'Pleno';
    public $requiresResume = true;
    public $requiresCoverLetter = false;
    public $isFeatured = false;
    public $isActive = true;

    // Controle de modal
    public $showModal = false;
    public $confirmingDelete = false;
    public $deleteId;
    public $isEditing = false;

    protected $rules = [
        'title' => 'required|string|max:255',
        'description' => 'required|string|min:50',
        'requirements' => 'nullable|string',
        'benefits' => 'nullable|string',
        'categoryId' => 'required|exists:job_categories,id',
        'contractType' => 'required|in:CLT,PJ,Freelance,Estágio,Temporário',
        'workMode' => 'required|in:Presencial,Remoto,Híbrido',
        'location' => 'nullable|string|max:255',
        'salaryMin' => 'nullable|numeric|min:0',
        'salaryMax' => 'nullable|numeric|min:0|gte:salaryMin',
        'salaryPeriod' => 'required|string',
        'salaryNegotiable' => 'boolean',
        'vacancies' => 'required|integer|min:1',
        'applicationDeadline' => 'nullable|date|after_or_equal:today',
        'experienceLevel' => 'required|in:Júnior,Pleno,Sênior,Especialista',
        'requiresResume' => 'boolean',
        'requiresCoverLetter' => 'boolean',
        'isFeatured' => 'boolean',
        'isActive' => 'boolean',
    ];

    protected $messages = [
        'title.required' => 'O título da vaga é obrigatório.',
        'description.required' => 'A descrição da vaga é obrigatória.',
        'description.min' => 'A descrição deve ter pelo menos 50 caracteres.',
        'categoryId.required' => 'A categoria é obrigatória.',
        'categoryId.exists' => 'A categoria selecionada não existe.',
        'salaryMax.gte' => 'O salário máximo deve ser maior ou igual ao salário mínimo.',
        'applicationDeadline.after_or_equal' => 'A data limite deve ser hoje ou uma data futura.',
    ];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingCategoryFilter()
    {
        $this->resetPage();
    }

    public function updatingStatusFilter()
    {
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortBy === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $field;
            $this->sortDirection = 'asc';
        }
    }

    public function createJob()
    {
        $this->resetForm();
        $this->isEditing = false;
        $this->showModal = true;
    }

    public function edit($id)
    {
        $this->resetForm();
        $this->isEditing = true;
        $this->jobId = $id;

        $job = JobVacancy::findOrFail($id);

        $this->title = $job->title;
        $this->description = $job->description;
        $this->requirements = $job->requirements;
        $this->benefits = $job->benefits;
        $this->categoryId = $job->category_id;
        $this->contractType = $job->contract_type;
        $this->workMode = $job->work_mode;
        $this->location = $job->location;
        $this->salaryMin = $job->salary_min;
        $this->salaryMax = $job->salary_max;
        $this->salaryPeriod = $job->salary_period;
        $this->salaryNegotiable = $job->salary_negotiable;
        $this->vacancies = $job->vacancies;
        $this->applicationDeadline = $job->application_deadline?->format('Y-m-d');
        $this->experienceLevel = $job->experience_level;
        $this->requiresResume = $job->requires_resume;
        $this->requiresCoverLetter = $job->requires_cover_letter;
        $this->isFeatured = $job->is_featured;
        $this->isActive = $job->is_active;

        $this->showModal = true;
    }

    public function save()
    {
        $this->validate();

        $data = [
            'title' => $this->title,
            'description' => $this->description,
            'requirements' => $this->requirements,
            'benefits' => $this->benefits,
            'category_id' => $this->categoryId,
            'contract_type' => $this->contractType,
            'work_mode' => $this->workMode,
            'location' => $this->location,
            'salary_min' => $this->salaryMin,
            'salary_max' => $this->salaryMax,
            'salary_period' => $this->salaryPeriod,
            'salary_negotiable' => $this->salaryNegotiable,
            'vacancies' => $this->vacancies,
            'application_deadline' => $this->applicationDeadline,
            'experience_level' => $this->experienceLevel,
            'requires_resume' => $this->requiresResume,
            'requires_cover_letter' => $this->requiresCoverLetter,
            'is_featured' => $this->isFeatured,
            'is_active' => $this->isActive,
        ];

        if ($this->isEditing) {
            $job = JobVacancy::findOrFail($this->jobId);
            $job->update($data);
            session()->flash('message', 'Vaga atualizada com sucesso!');
        } else {
            JobVacancy::create($data);
            session()->flash('message', 'Vaga criada com sucesso!');
        }

        $this->closeModal();
    }

    public function delete($id)
    {
        Log::info('Método delete chamado', ['job_id' => $id]);
        $this->deleteId = $id;
        $this->confirmingDelete = true;
    }

    public function deleteDirectly($id)
    {
        try {
            $job = JobVacancy::findOrFail($id);

            Log::info('Excluindo vaga diretamente', [
                'job_id' => $job->id,
                'job_title' => $job->title,
                'applications_count' => $job->applications_count
            ]);

            $job->delete();

            session()->flash('message', 'Vaga excluída com sucesso!');

            // Reset da paginação se necessário
            $this->resetPage();

            Log::info('Vaga excluída com sucesso (direto)', ['job_id' => $job->id]);
        } catch (\Exception $e) {
            Log::error('Erro ao excluir vaga (direto)', [
                'job_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            session()->flash('error', 'Erro ao excluir vaga: ' . $e->getMessage());
        }
    }

    public function confirmDelete()
    {
        try {
            $job = JobVacancy::findOrFail($this->deleteId);

            // Log para debug
            Log::info('Tentando excluir vaga', [
                'job_id' => $job->id,
                'job_title' => $job->title,
                'applications_count' => $job->applications_count
            ]);

            $job->delete();

            session()->flash('message', 'Vaga excluída com sucesso!');
            $this->confirmingDelete = false;
            $this->deleteId = null;

            // Reset da paginação se necessário
            $this->resetPage();

            Log::info('Vaga excluída com sucesso', ['job_id' => $job->id]);
        } catch (\Exception $e) {
            Log::error('Erro ao excluir vaga', [
                'job_id' => $this->deleteId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            session()->flash('error', 'Erro ao excluir vaga: ' . $e->getMessage());
            $this->confirmingDelete = false;
            $this->deleteId = null;
        }
    }

    public function toggleFeatured($id)
    {
        $job = JobVacancy::findOrFail($id);
        $job->update(['is_featured' => !$job->is_featured]);

        session()->flash('message', 'Status de destaque atualizado!');
    }

    public function toggleActive($id)
    {
        $job = JobVacancy::findOrFail($id);
        $job->update(['is_active' => !$job->is_active]);

        session()->flash('message', 'Status da vaga atualizado!');
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->resetForm();
    }

    private function resetForm()
    {
        $this->jobId = null;
        $this->title = '';
        $this->description = '';
        $this->requirements = '';
        $this->benefits = '';
        $this->categoryId = '';
        $this->contractType = 'CLT';
        $this->workMode = 'Presencial';
        $this->location = '';
        $this->salaryMin = '';
        $this->salaryMax = '';
        $this->salaryPeriod = 'Mensal';
        $this->salaryNegotiable = false;
        $this->vacancies = 1;
        $this->applicationDeadline = '';
        $this->experienceLevel = 'Pleno';
        $this->requiresResume = true;
        $this->requiresCoverLetter = false;
        $this->isFeatured = false;
        $this->isActive = true;
    }

    public function render()
    {
        $query = JobVacancy::with('category');

        if ($this->search) {
            $query->where(function ($q) {
                $q->where('title', 'like', '%' . $this->search . '%')
                    ->orWhere('description', 'like', '%' . $this->search . '%')
                    ->orWhere('location', 'like', '%' . $this->search . '%')
                    ->orWhereHas('category', function ($categoryQuery) {
                        $categoryQuery->where('name', 'like', '%' . $this->search . '%');
                    });
            });
        }

        if ($this->categoryFilter) {
            $query->where('category_id', $this->categoryFilter);
        }

        if ($this->statusFilter === 'active') {
            $query->where('is_active', true);
        } elseif ($this->statusFilter === 'inactive') {
            $query->where('is_active', false);
        } elseif ($this->statusFilter === 'featured') {
            $query->where('is_featured', true);
        }

        $query->orderBy($this->sortBy, $this->sortDirection);

        $jobs = $query->paginate($this->perPage);
        $categories = JobCategory::active()->ordered()->get();

        return view('livewire.admin.job-manager', [
            'jobs' => $jobs,
            'categories' => $categories,
        ]);
    }
}
