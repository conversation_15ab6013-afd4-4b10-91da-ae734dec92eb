<?php

namespace App\Services;

use App\Models\Album;
use App\Models\AlbumMedia;
use App\Models\User;
use App\Models\PremiumAlbumSetting;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Exception;

class PremiumAlbumService
{
    /**
     * Cria um novo álbum premium.
     */
    public function createPremiumAlbum(User $user, array $data, array $files = [])
    {
        // Validar permissões
        $this->validateUserPermissions($user);
        
        // Validar dados
        $this->validateAlbumData($data, $files);

        DB::beginTransaction();
        
        try {
            // Criar álbum
            $album = Album::create([
                'user_id' => $user->id,
                'name' => $data['name'],
                'description' => $data['description'] ?? null,
                'privacy' => 'public', // Álbuns premium são sempre públicos
                'is_premium' => true,
                'price' => $data['price'],
                'preview_limit' => $data['preview_limit'] ?? 3,
                'status' => 'draft',
                'admin_approved' => PremiumAlbumSetting::shouldAutoApprove(),
                'commission_rate' => PremiumAlbumSetting::getCommissionRate(),
                'tags' => $data['tags'] ?? null,
            ]);

            // Upload de arquivos
            if (!empty($files)) {
                $this->uploadAlbumFiles($album, $files);
            }

            // Se auto-aprovação estiver ativa, publicar
            if (PremiumAlbumSetting::shouldAutoApprove()) {
                $album->update(['status' => 'published']);
            }

            DB::commit();

            return [
                'success' => true,
                'album' => $album,
                'message' => PremiumAlbumSetting::shouldAutoApprove() 
                    ? 'Álbum premium criado e publicado com sucesso!'
                    : 'Álbum premium criado! Aguarde aprovação da administração.'
            ];

        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Atualiza um álbum premium.
     */
    public function updatePremiumAlbum(Album $album, User $user, array $data, array $files = [])
    {
        // Verificar propriedade
        if ($album->user_id !== $user->id && $user->role !== 'administrador') {
            throw new Exception('Você não tem permissão para editar este álbum.');
        }

        // Validar dados
        $this->validateAlbumData($data, $files, $album);

        DB::beginTransaction();
        
        try {
            // Atualizar dados básicos
            $updateData = [
                'name' => $data['name'],
                'description' => $data['description'] ?? $album->description,
                'tags' => $data['tags'] ?? $album->tags,
            ];

            // Se for admin, pode alterar preço e status
            if ($user->role === 'administrador') {
                $updateData['price'] = $data['price'] ?? $album->price;
                $updateData['status'] = $data['status'] ?? $album->status;
                $updateData['admin_approved'] = $data['admin_approved'] ?? $album->admin_approved;
            } else {
                // Usuário comum pode alterar preço apenas se não tiver vendas
                if ($album->sales_count == 0) {
                    $updateData['price'] = $data['price'] ?? $album->price;
                }
            }

            $album->update($updateData);

            // Upload de novos arquivos
            if (!empty($files)) {
                $this->uploadAlbumFiles($album, $files);
            }

            DB::commit();

            return [
                'success' => true,
                'album' => $album->fresh(),
                'message' => 'Álbum atualizado com sucesso!'
            ];

        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Remove um álbum premium.
     */
    public function deletePremiumAlbum(Album $album, User $user)
    {
        // Verificar propriedade
        if ($album->user_id !== $user->id && $user->role !== 'administrador') {
            throw new Exception('Você não tem permissão para excluir este álbum.');
        }

        // Verificar se tem vendas
        if ($album->sales_count > 0) {
            throw new Exception('Não é possível excluir um álbum que já possui vendas.');
        }

        DB::beginTransaction();
        
        try {
            // Remover arquivos do storage
            foreach ($album->medias as $media) {
                if (Storage::exists($media->file_path)) {
                    Storage::delete($media->file_path);
                }
                if ($media->thumbnail_path && Storage::exists($media->thumbnail_path)) {
                    Storage::delete($media->thumbnail_path);
                }
            }

            // Remover álbum (cascade irá remover mídias e outros relacionamentos)
            $album->delete();

            DB::commit();

            return [
                'success' => true,
                'message' => 'Álbum excluído com sucesso!'
            ];

        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Aprova um álbum premium.
     */
    public function approveAlbum(Album $album, User $admin)
    {
        if ($admin->role !== 'administrador') {
            throw new Exception('Apenas administradores podem aprovar álbuns.');
        }

        $album->update([
            'admin_approved' => true,
            'status' => 'published'
        ]);

        return [
            'success' => true,
            'message' => 'Álbum aprovado e publicado com sucesso!'
        ];
    }

    /**
     * Rejeita um álbum premium.
     */
    public function rejectAlbum(Album $album, User $admin, string $reason = null)
    {
        if ($admin->role !== 'administrador') {
            throw new Exception('Apenas administradores podem rejeitar álbuns.');
        }

        $album->update([
            'admin_approved' => false,
            'status' => 'suspended'
        ]);

        // TODO: Enviar notificação para o criador com o motivo

        return [
            'success' => true,
            'message' => 'Álbum rejeitado com sucesso!'
        ];
    }

    /**
     * Valida permissões do usuário.
     */
    private function validateUserPermissions(User $user)
    {
        if ($user->role === 'visitante') {
            throw new Exception('Apenas usuários VIP podem criar álbuns premium.');
        }
    }

    /**
     * Valida dados do álbum.
     */
    private function validateAlbumData(array $data, array $files, Album $album = null)
    {
        // Validar nome
        if (empty($data['name'])) {
            throw new Exception('Nome do álbum é obrigatório.');
        }

        // Validar preço
        if (isset($data['price'])) {
            $price = (float) $data['price'];
            $minPrice = PremiumAlbumSetting::getMinPrice();
            $maxPrice = PremiumAlbumSetting::getMaxPrice();

            if ($price < $minPrice || $price > $maxPrice) {
                throw new Exception("Preço deve estar entre R$ {$minPrice} e R$ {$maxPrice}.");
            }
        }

        // Validar arquivos
        if (!empty($files)) {
            $maxFiles = PremiumAlbumSetting::getMaxFilesPerAlbum();
            $currentFiles = $album ? $album->medias()->count() : 0;
            
            if (($currentFiles + count($files)) > $maxFiles) {
                throw new Exception("Máximo de {$maxFiles} arquivos por álbum.");
            }

            $maxSize = PremiumAlbumSetting::getMaxFileSize() * 1024 * 1024; // MB para bytes
            $allowedTypes = PremiumAlbumSetting::getAllowedFileTypes();

            foreach ($files as $file) {
                if ($file->getSize() > $maxSize) {
                    throw new Exception("Arquivo muito grande. Máximo: " . PremiumAlbumSetting::getMaxFileSize() . "MB");
                }

                $extension = strtolower($file->getClientOriginalExtension());
                if (!in_array($extension, $allowedTypes)) {
                    throw new Exception("Tipo de arquivo não permitido: {$extension}");
                }
            }
        }
    }

    /**
     * Faz upload dos arquivos do álbum.
     */
    private function uploadAlbumFiles(Album $album, array $files)
    {
        foreach ($files as $file) {
            $this->uploadSingleFile($album, $file);
        }
    }

    /**
     * Faz upload de um único arquivo.
     */
    private function uploadSingleFile(Album $album, UploadedFile $file)
    {
        $extension = strtolower($file->getClientOriginalExtension());
        $type = in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp']) ? 'photo' : 'video';
        
        // Gerar nome único
        $filename = Str::uuid() . '.' . $extension;
        $path = "albums/premium/{$album->id}/{$filename}";
        
        // Fazer upload
        $file->storeAs('albums/premium/' . $album->id, $filename, 'private');
        
        // Criar registro na base de dados
        AlbumMedia::create([
            'album_id' => $album->id,
            'type' => $type,
            'file_path' => $path,
            'file_name' => $file->getClientOriginalName(),
            'file_size' => $file->getSize(),
            'mime_type' => $file->getMimeType(),
            'sort_order' => $album->medias()->count() + 1,
        ]);
    }
}
