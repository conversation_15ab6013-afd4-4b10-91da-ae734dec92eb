/**
 * Mobile Menu Fix - ESSENCIAL APENAS
 */

/* <PERSON><PERSON><PERSON> para o site */
body {
    min-width: 320px !important;
}

/* Sidebar Mobile */
@media (max-width: 1023px) {
    [data-flux-sidebar] {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        height: 100vh !important;
        width: 280px !important;
        z-index: 50 !important;
        transform: translateX(-100%) !important;
        transition: transform 0.3s ease !important;
    }

    [data-flux-sidebar][data-open="true"] {
        transform: translateX(0) !important;
    }

    /* Botão toggle do menu - área de clique maior */
    [data-flux-sidebar-toggle] {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        min-width: 44px !important;
        min-height: 44px !important;
        padding: 12px !important;
        z-index: 51 !important;
        position: relative !important;
        cursor: pointer !important;
        background: transparent !important;
        border: none !important;
    }

    /* Ícone dentro do botão */
    [data-flux-sidebar-toggle] svg {
        width: 20px !important;
        height: 20px !important;
        pointer-events: none !important;
    }

    /* Header mobile - garantir altura adequada */
    [data-flux-header] {
        min-height: 60px !important;
        padding: 8px 16px !important;
    }

    /* Conteúdo principal */
    [data-flux-main] {
        margin-left: 0 !important;
        width: 100% !important;
        min-height: calc(100vh - 60px) !important;
    }
}

/* Telas muito pequenas */
@media (max-width: 640px) {
    [data-flux-sidebar] {
        width: 100% !important;
        max-width: 300px !important;
    }

    /* Botões ainda maiores em telas pequenas */
    [data-flux-sidebar-toggle] {
        min-width: 48px !important;
        min-height: 48px !important;
        padding: 14px !important;
    }

    [data-flux-sidebar-toggle] svg {
        width: 22px !important;
        height: 22px !important;
    }
}

/* Correções adicionais para evitar sobreposição */
@media (max-width: 1023px) {

    /* Garantir que elementos não se sobreponham ao botão */
    [data-flux-header]>* {
        flex-shrink: 0;
    }

    /* Espaçamento adequado entre elementos do header */
    [data-flux-header] {
        gap: 8px !important;
    }

    /* Prevenir que outros elementos "roubem" o clique */
    [data-flux-sidebar-toggle] {
        touch-action: manipulation !important;
        user-select: none !important;
        -webkit-user-select: none !important;
        -webkit-tap-highlight-color: transparent !important;
    }

    /* Garantir que o botão esteja sempre visível */
    [data-flux-sidebar-toggle]:before {
        content: '';
        position: absolute;
        top: -5px;
        left: -5px;
        right: -5px;
        bottom: -5px;
        z-index: -1;
    }
}