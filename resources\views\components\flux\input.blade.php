@props([
    'type' => 'text',
    'placeholder' => '',
    'disabled' => false,
    'readonly' => false,
    'iconTrailing' => null,
    'iconLeading' => null,
])

@php
    $classes = 'block w-full rounded-md border-0 py-1.5 px-3 text-gray-900 dark:text-gray-100 shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-gray-600 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-purple-600 dark:focus:ring-purple-400 sm:text-sm sm:leading-6 bg-white dark:bg-zinc-800';
    
    if ($disabled) {
        $classes .= ' opacity-50 cursor-not-allowed';
    }
@endphp

<div class="relative">
    @if($iconLeading)
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            {{ $iconLeading }}
        </div>
        @php $classes .= ' pl-10'; @endphp
    @endif
    
    <input 
        type="{{ $type }}"
        placeholder="{{ $placeholder }}"
        @if($disabled) disabled @endif
        @if($readonly) readonly @endif
        {{ $attributes->merge(['class' => $classes]) }}
    />
    
    @if($iconTrailing)
        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            {{ $iconTrailing }}
        </div>
    @endif
</div>
