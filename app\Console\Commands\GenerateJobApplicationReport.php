<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\JobApplication;
use App\Models\JobVacancy;
use App\Models\User;
use Carbon\Carbon;

class GenerateJobApplicationReport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'job-applications:report 
                            {--period=week : Período do relatório (day, week, month, year)}
                            {--format=table : Formato de saída (table, json, csv)}
                            {--email= : Email para enviar o relatório}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Gerar relatório de candidaturas de emprego';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $period = $this->option('period');
        $format = $this->option('format');
        $email = $this->option('email');

        $this->info("Gerando relatório de candidaturas para o período: {$period}");

        // Definir período
        $dateRange = $this->getDateRange($period);
        
        // Coletar dados
        $data = $this->collectData($dateRange);
        
        // Exibir relatório
        $this->displayReport($data, $format);
        
        // Enviar por email se solicitado
        if ($email) {
            $this->sendReportByEmail($data, $email);
        }
        
        $this->info('Relatório gerado com sucesso!');
    }

    /**
     * Obter intervalo de datas baseado no período
     */
    private function getDateRange($period): array
    {
        $end = Carbon::now();
        
        $start = match ($period) {
            'day' => $end->copy()->startOfDay(),
            'week' => $end->copy()->startOfWeek(),
            'month' => $end->copy()->startOfMonth(),
            'year' => $end->copy()->startOfYear(),
            default => $end->copy()->startOfWeek(),
        };
        
        return [$start, $end];
    }

    /**
     * Coletar dados do relatório
     */
    private function collectData($dateRange): array
    {
        [$start, $end] = $dateRange;
        
        $applications = JobApplication::with(['job', 'user'])
            ->whereBetween('created_at', [$start, $end])
            ->get();
            
        return [
            'period' => [
                'start' => $start->format('d/m/Y H:i'),
                'end' => $end->format('d/m/Y H:i'),
            ],
            'summary' => [
                'total_applications' => $applications->count(),
                'vip_applications' => $applications->where('is_vip_priority', true)->count(),
                'pending_applications' => $applications->where('status', 'Pendente')->count(),
                'approved_applications' => $applications->where('status', 'Aprovada')->count(),
                'rejected_applications' => $applications->where('status', 'Rejeitada')->count(),
            ],
            'by_status' => $applications->groupBy('status')->map->count(),
            'by_priority' => $applications->groupBy('priority_level')->map->count(),
            'by_job' => $applications->groupBy('job.title')->map->count(),
            'top_jobs' => $applications->groupBy('job.title')
                ->map->count()
                ->sortDesc()
                ->take(5),
            'applications' => $applications->map(function ($app) {
                return [
                    'id' => $app->id,
                    'job' => $app->job->title,
                    'candidate' => $app->candidate_name ?: $app->user->name,
                    'email' => $app->candidate_email ?: $app->user->email,
                    'status' => $app->status,
                    'priority' => $app->priority_level,
                    'is_vip' => $app->is_vip_priority,
                    'created_at' => $app->created_at->format('d/m/Y H:i'),
                ];
            }),
        ];
    }

    /**
     * Exibir relatório
     */
    private function displayReport($data, $format)
    {
        if ($format === 'json') {
            $this->line(json_encode($data, JSON_PRETTY_PRINT));
            return;
        }
        
        if ($format === 'csv') {
            $this->generateCsv($data);
            return;
        }
        
        // Formato tabela (padrão)
        $this->displayTableReport($data);
    }

    /**
     * Exibir relatório em formato tabela
     */
    private function displayTableReport($data)
    {
        $this->info("\n📊 RELATÓRIO DE CANDIDATURAS");
        $this->info("Período: {$data['period']['start']} até {$data['period']['end']}");
        
        $this->info("\n📈 RESUMO GERAL");
        $this->table(
            ['Métrica', 'Valor'],
            [
                ['Total de Candidaturas', $data['summary']['total_applications']],
                ['Candidaturas VIP', $data['summary']['vip_applications']],
                ['Pendentes', $data['summary']['pending_applications']],
                ['Aprovadas', $data['summary']['approved_applications']],
                ['Rejeitadas', $data['summary']['rejected_applications']],
            ]
        );
        
        $this->info("\n📊 POR STATUS");
        $statusData = [];
        foreach ($data['by_status'] as $status => $count) {
            $statusData[] = [$status, $count];
        }
        $this->table(['Status', 'Quantidade'], $statusData);
        
        $this->info("\n🏆 TOP 5 VAGAS");
        $topJobsData = [];
        foreach ($data['top_jobs'] as $job => $count) {
            $topJobsData[] = [$job, $count];
        }
        $this->table(['Vaga', 'Candidaturas'], $topJobsData);
    }

    /**
     * Gerar arquivo CSV
     */
    private function generateCsv($data)
    {
        $filename = 'job_applications_report_' . date('Y-m-d_H-i-s') . '.csv';
        $filepath = storage_path('app/reports/' . $filename);
        
        // Criar diretório se não existir
        if (!is_dir(dirname($filepath))) {
            mkdir(dirname($filepath), 0755, true);
        }
        
        $file = fopen($filepath, 'w');
        
        // Cabeçalho
        fputcsv($file, [
            'ID', 'Vaga', 'Candidato', 'Email', 'Status', 
            'Prioridade', 'VIP', 'Data de Candidatura'
        ]);
        
        // Dados
        foreach ($data['applications'] as $app) {
            fputcsv($file, [
                $app['id'],
                $app['job'],
                $app['candidate'],
                $app['email'],
                $app['status'],
                $app['priority'],
                $app['is_vip'] ? 'Sim' : 'Não',
                $app['created_at'],
            ]);
        }
        
        fclose($file);
        
        $this->info("Arquivo CSV gerado: {$filepath}");
    }

    /**
     * Enviar relatório por email
     */
    private function sendReportByEmail($data, $email)
    {
        // Implementar envio por email se necessário
        $this->info("Funcionalidade de envio por email será implementada em versão futura.");
    }
}
