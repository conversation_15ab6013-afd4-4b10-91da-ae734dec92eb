<div>
    <!-- Category Filter -->
    <div class="mb-6 sm:mb-8">
        <div class="bg-zinc-800 rounded-lg p-4 sm:p-6">
            <h3 class="text-lg font-semibold text-gray-300 mb-4">Filtrar por categoria</h3>

            <!-- Dropdown para mobile (visível apenas em telas pequenas) -->
            <div class="block md:hidden">
                <flux:select wire:model.live="selectedCategory" placeholder="Selecione uma categoria" class="w-full">
                    <option value="">Todas as categorias</option>
                    @foreach($categories as $category)
                        <option value="{{ $category->id }}">{{ $category->title }}</option>
                    @endforeach
                </flux:select>
            </div>

            <!-- Botões horizontais para desktop (visível apenas em telas médias e grandes) -->
            <div class="hidden md:block">
                <flux:radio.group wire:model.live="selectedCategory" variant="segmented" class="w-full">
                    <flux:radio value="">Todas as categorias</flux:radio>
                    @foreach($categories as $category)
                        <flux:radio value="{{ $category->id }}">{{ $category->title }}</flux:radio>
                    @endforeach
                </flux:radio.group>
            </div>
        </div>
    </div>



    <!-- Contos Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
        @forelse($contos as $conto)
            <div class="animate-in zoom-in duration-200">
                <div class="conto-card bg-zinc-800 ring-1 ring-zinc-700 rounded-lg flex flex-col p-4 sm:p-6 hover:ring-2 hover:ring-sky-400 relative h-full">
                    <!-- Header do card com autor -->
                    <div class="flex items-start space-x-3 mb-4">
                        <div class="flex-shrink-0">
                            <a href="{{ route('user.profile', $conto->user->username) }}">
                                @php
                                    $contoAuthorAvatar = $conto->user->currentPhoto ?? $conto->user->userPhotos->first();
                                    $contoAuthorAvatarUrl = $contoAuthorAvatar ? Storage::url($contoAuthorAvatar->photo_path) : asset('images/default-avatar.jpg');
                                @endphp
                                <img src="{{ $contoAuthorAvatarUrl }}"
                                     class="w-10 h-10 sm:w-12 sm:h-12 rounded-full object-cover">
                            </a>
                        </div>
                        <div class="flex-1 min-w-0">
                            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                                <div class="min-w-0">
                                    <a href="{{ route('user.profile', $conto->user->username) }}" class="font-semibold hover:underline text-gray-300 text-sm sm:text-base truncate block">
                                        {{ $conto->user->name }}
                                    </a>
                                    <div class="text-xs sm:text-sm text-gray-400 truncate">
                                        {{ '@' . $conto->user->username }}
                                    </div>
                                </div>
                                <div class="flex-shrink-0">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        {{ $conto->category->title ?? 'Sem categoria' }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Conteúdo do conto -->
                    <div class="flex-1 flex flex-col">
                        <h3 class="text-lg sm:text-xl font-semibold mb-3">
                            <a href="{{ route('contos.show', $conto->id) }}" class="hover:text-sky-400 hover:underline text-gray-300 line-clamp-2">
                                {{ $conto->title }}
                            </a>
                        </h3>

                        <div class="prose dark:prose-invert max-w-none text-gray-400 text-sm sm:text-base mb-4 flex-1">
                            <p class="line-clamp-3">
                                {!! Str::limit(strip_tags($conto->content), 150) !!}
                            </p>
                        </div>

                        <!-- Data de publicação e visualizações -->
                        <div class="flex items-center justify-between text-xs text-gray-500 mb-4">
                            <span>{{ $conto->created_at->format('d/m/Y') }} • {{ $conto->created_at->diffForHumans() }}</span>
                            <span class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                </svg>
                                {{ number_format($conto->number_views) }}
                            </span>
                        </div>

                        <!-- Botões de ação -->
                        <div class="flex flex-col sm:flex-row gap-2 sm:gap-3 mt-auto">
                            <a href="{{ route('contos.show', $conto->id) }}"
                               class="flex-1 text-center text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition px-3 py-2">
                                Ler mais
                            </a>

                            @if(auth()->check() && auth()->id() === $conto->user_id)
                                <div class="flex gap-2">
                                    <a href="{{ route('contos.edit', $conto->id) }}"
                                       class="flex items-center justify-center text-sm font-medium text-white bg-yellow-600 rounded-lg hover:bg-yellow-700 transition px-3 py-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-4.036a2.5 2.5 0 113.536 3.536L7.5 21H3v-4.5L16.732 3.732z" />
                                        </svg>
                                        <span class="ml-1 hidden sm:inline">Editar</span>
                                    </a>

                                    <form action="{{ route('contos.destroy', $conto->id) }}" method="POST" class="inline-block">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit"
                                                onclick="return confirm('Tem certeza que deseja excluir este conto?')"
                                                class="flex items-center justify-center text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700 transition px-3 py-2">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                            </svg>
                                            <span class="ml-1 hidden sm:inline">Excluir</span>
                                        </button>
                                    </form>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        @empty
            <div class="col-span-full text-center py-12">
                <div class="bg-zinc-800 rounded-lg p-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                    <h3 class="text-lg font-medium text-gray-300 mb-2">Nenhum conto encontrado</h3>
                    <p class="text-gray-400">Seja o primeiro a compartilhar uma história incrível!</p>
                </div>
            </div>
        @endforelse
    </div>

    <!-- Pagination -->
    <div class="mt-8 flex justify-center">
        <div class="bg-zinc-800 rounded-lg p-4">
            {{ $contos->links() }}
        </div>
    </div>
</div>
