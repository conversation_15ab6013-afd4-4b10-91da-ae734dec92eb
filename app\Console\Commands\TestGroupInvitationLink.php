<?php

namespace App\Console\Commands;

use App\Models\Group;
use App\Models\GroupInvitation;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class TestGroupInvitationLink extends Command
{
    protected $signature = 'test:group-invitation-link';
    protected $description = 'Testa o link de aceitar convite de grupo';

    public function handle()
    {
        $this->info('Testando link de convite de grupo...');

        // Criar usuários de teste
        $inviter = User::create([
            'name' => 'Usuário Convidador',
            'username' => 'convidador_' . now()->timestamp,
            'email' => 'convidador' . now()->timestamp . '@exemplo.com',
            'password' => bcrypt('password'),
            'role' => 'vip',
            'active' => true,
        ]);

        $invited = User::create([
            'name' => 'Usuário Convidado',
            'username' => 'convidado_' . now()->timestamp,
            'email' => 'convidado' . now()->timestamp . '@exemplo.com',
            'password' => bcrypt('password'),
            'role' => 'vip',
            'active' => true,
        ]);

        $this->info("Usuário convidador criado: {$inviter->name} (ID: {$inviter->id})");
        $this->info("Usuário convidado criado: {$invited->name} (ID: {$invited->id})");

        // Criar um grupo
        $group = Group::create([
            'name' => 'Grupo Teste Convite ' . now()->format('H:i:s'),
            'slug' => Str::slug('Grupo Teste Convite ' . now()->format('H:i:s')),
            'description' => 'Grupo para testar convites',
            'privacy' => 'private',
            'creator_id' => $inviter->id,
            'posts_require_approval' => false,
        ]);

        $this->info("Grupo criado: {$group->name} (ID: {$group->id})");

        // Criar um convite
        $invitation = GroupInvitation::create([
            'group_id' => $group->id,
            'user_id' => $invited->id,
            'invited_by' => $inviter->id,
            'status' => 'pending',
        ]);

        $this->info("Convite criado: ID {$invitation->id}");

        // Gerar URLs de teste
        $acceptUrl = route('grupos.invitations.accept', $invitation);
        $declineUrl = route('grupos.invitations.decline', $invitation);

        $this->info("\nURLs geradas:");
        $this->info("Aceitar: {$acceptUrl}");
        $this->info("Recusar: {$declineUrl}");

        // Verificar se as rotas existem
        $this->info("\nVerificando rotas...");
        
        try {
            $acceptRoute = app('router')->getRoutes()->match(
                app('request')->create($acceptUrl, 'GET')
            );
            $this->info("✓ Rota de aceitar existe: " . $acceptRoute->getName());
        } catch (\Exception $e) {
            $this->error("✗ Rota de aceitar não encontrada: " . $e->getMessage());
        }

        try {
            $declineRoute = app('router')->getRoutes()->match(
                app('request')->create($declineUrl, 'GET')
            );
            $this->info("✓ Rota de recusar existe: " . $declineRoute->getName());
        } catch (\Exception $e) {
            $this->error("✗ Rota de recusar não encontrada: " . $e->getMessage());
        }

        $this->info("\nTeste concluído!");
        return 0;
    }
}
