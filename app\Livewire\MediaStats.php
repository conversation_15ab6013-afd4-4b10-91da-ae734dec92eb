<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\User;
use App\Models\AlbumMedia;
use App\Models\Album;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class MediaStats extends Component
{
    public User $user;
    public $stats = [];
    public $chartData = [];
    public $recentActivity = [];

    public function mount(User $user)
    {
        $this->user = $user;
        $this->loadStats();
        $this->loadChartData();
        $this->loadRecentActivity();
    }

    protected function loadStats()
    {
        $query = AlbumMedia::where('user_id', $this->user->id);
        
        // Se não é o próprio usuário, contar apenas mídias públicas
        if (!Auth::check() || Auth::id() !== $this->user->id) {
            $query->whereHas('album', function($q) {
                $q->where('privacy', 'public');
            });
        }

        $totalMedias = $query->count();
        $totalPhotos = $query->where('type', 'photo')->count();
        $totalVideos = $query->where('type', 'video')->count();
        
        // Tamanho total dos arquivos
        $totalSize = $query->sum('file_size') ?? 0;
        
        // Álbuns
        $albumQuery = Album::where('user_id', $this->user->id);
        if (!Auth::check() || Auth::id() !== $this->user->id) {
            $albumQuery->where('privacy', 'public');
        }
        $totalAlbums = $albumQuery->count();

        // Mídia mais recente
        $latestMedia = $query->latest()->first();
        
        // Álbum com mais mídias
        $topAlbum = $albumQuery->withCount('medias')
            ->orderBy('medias_count', 'desc')
            ->first();

        $this->stats = [
            'total_medias' => $totalMedias,
            'total_photos' => $totalPhotos,
            'total_videos' => $totalVideos,
            'total_albums' => $totalAlbums,
            'total_size' => $this->formatBytes($totalSize),
            'latest_media' => $latestMedia,
            'top_album' => $topAlbum,
            'photos_percentage' => $totalMedias > 0 ? round(($totalPhotos / $totalMedias) * 100) : 0,
            'videos_percentage' => $totalMedias > 0 ? round(($totalVideos / $totalMedias) * 100) : 0,
        ];
    }

    protected function loadChartData()
    {
        $query = AlbumMedia::where('user_id', $this->user->id);
        
        if (!Auth::check() || Auth::id() !== $this->user->id) {
            $query->whereHas('album', function($q) {
                $q->where('privacy', 'public');
            });
        }

        // Uploads por mês (últimos 6 meses)
        $monthlyUploads = $query
            ->select(
                DB::raw('DATE_FORMAT(created_at, "%Y-%m") as month'),
                DB::raw('COUNT(*) as count'),
                DB::raw('SUM(CASE WHEN type = "photo" THEN 1 ELSE 0 END) as photos'),
                DB::raw('SUM(CASE WHEN type = "video" THEN 1 ELSE 0 END) as videos')
            )
            ->where('created_at', '>=', now()->subMonths(6))
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        $this->chartData = [
            'monthly_uploads' => $monthlyUploads,
            'labels' => $monthlyUploads->pluck('month')->map(function($month) {
                return \Carbon\Carbon::createFromFormat('Y-m', $month)->format('M/Y');
            }),
            'photos_data' => $monthlyUploads->pluck('photos'),
            'videos_data' => $monthlyUploads->pluck('videos'),
        ];
    }

    protected function loadRecentActivity()
    {
        $query = AlbumMedia::where('user_id', $this->user->id)
            ->with('album');
        
        if (!Auth::check() || Auth::id() !== $this->user->id) {
            $query->whereHas('album', function($q) {
                $q->where('privacy', 'public');
            });
        }

        $this->recentActivity = $query
            ->latest()
            ->take(5)
            ->get()
            ->map(function($media) {
                return [
                    'id' => $media->id,
                    'type' => $media->type,
                    'title' => $media->title ?: ($media->type === 'photo' ? 'Foto' : 'Vídeo'),
                    'album' => $media->album->name,
                    'date' => $media->created_at->diffForHumans(),
                    'thumbnail' => $media->thumbnail_url,
                ];
            });
    }

    protected function formatBytes($bytes)
    {
        if ($bytes == 0) return '0 B';
        
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $factor = floor(log($bytes, 1024));
        
        return round($bytes / pow(1024, $factor), 2) . ' ' . $units[$factor];
    }

    public function render()
    {
        return view('livewire.media-stats');
    }
}
