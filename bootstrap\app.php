<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Support\Facades\Route;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__ . '/../routes/web.php',
        commands: __DIR__ . '/../routes/console.php',
        health: '/up',
        then: function () {
            Route::middleware('web')
                ->group(base_path('routes/auth.php'));
        },
    )
    ->withMiddleware(function (Middleware $middleware) {
        // Register middleware aliases
        $middleware->alias([
            'auth' => \App\Http\Middleware\Authenticate::class,
            'auth.basic' => \Illuminate\Auth\Middleware\AuthenticateWithBasicAuth::class,
            'cache.headers' => \Illuminate\Http\Middleware\SetCacheHeaders::class,
            'can' => \Illuminate\Auth\Middleware\Authorize::class,
            'guest' => \App\Http\Middleware\RedirectIfAuthenticated::class,
            'password.confirm' => \Illuminate\Auth\Middleware\RequirePassword::class,
            'signed' => \Illuminate\Routing\Middleware\ValidateSignature::class,
            'throttle' => \Illuminate\Routing\Middleware\ThrottleRequests::class,
            'verified' => \Illuminate\Auth\Middleware\EnsureEmailIsVerified::class,
            'verified.redirect' => \App\Http\Middleware\EnsureEmailIsVerifiedOrRedirect::class,
            'verified.home' => \App\Http\Middleware\CheckEmailVerificationForHome::class,
            'role' => \App\Http\Middleware\RoleMiddleware::class,
            'role_or_above' => \App\Http\Middleware\RoleOrAboveMiddleware::class,
            'admin' => \App\Http\Middleware\AdminMiddleware::class,
            'visitor.time' => \App\Http\Middleware\VisitorTimeLimit::class,
            'check.event.registration.time' => \App\Http\Middleware\CheckEventRegistrationTime::class,
            'session.timeout' => \App\Http\Middleware\SessionTimeout::class,
            'secret.group.access' => \App\Http\Middleware\CheckSecretGroupAccess::class,
            'track.activity' => \App\Http\Middleware\UserActivityTracker::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
