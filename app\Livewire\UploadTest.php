<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class UploadTest extends Component
{
    use WithFileUploads;

    public $testImage;
    public $testVideo;
    public $uploadStatus = '';
    public $errorMessage = '';
    public $uploadProgress = 0;

    protected function rules()
    {
        return [
            'testImage' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:204800', // 200MB
            'testVideo' => 'nullable|file|mimetypes:video/mp4,video/quicktime,video/avi,video/webm|max:204800', // 200MB
        ];
    }

    protected $messages = [
        'testImage.max' => 'A imagem não pode ser maior que 200MB.',
        'testImage.mimes' => 'A imagem deve estar nos formatos: JPEG, PNG, JPG, GIF ou WebP.',
        'testVideo.max' => 'O vídeo não pode ser maior que 200MB.',
        'testVideo.mimetypes' => 'O vídeo deve estar nos formatos: MP4, MOV, AVI ou WebM.',
    ];

    public function testImageUpload()
    {
        try {
            $this->validate(['testImage' => 'required|image|mimes:jpeg,png,jpg,gif,webp|max:204800']);

            if (!$this->testImage->isValid()) {
                throw new \Exception('Arquivo de imagem inválido ou corrompido.');
            }

            $filename = 'test_' . time() . '_' . uniqid() . '.' . $this->testImage->getClientOriginalExtension();
            $path = $this->testImage->storeAs('test-uploads/images', $filename, 'public');

            if (!$path) {
                throw new \Exception('Falha ao salvar a imagem no servidor.');
            }

            $this->uploadStatus = "✅ Imagem enviada com sucesso!\n";
            $this->uploadStatus .= "📁 Arquivo: {$filename}\n";
            $this->uploadStatus .= "📏 Tamanho: " . $this->formatBytes($this->testImage->getSize()) . "\n";
            $this->uploadStatus .= "🔗 URL: " . Storage::url($path);

            Log::info('Test image upload successful', [
                'filename' => $filename,
                'path' => $path,
                'size' => $this->testImage->getSize()
            ]);

            $this->errorMessage = '';
            $this->reset('testImage');

        } catch (\Illuminate\Validation\ValidationException $e) {
            $this->errorMessage = 'Erro de validação: ' . implode(', ', $e->validator->errors()->all());
        } catch (\Exception $e) {
            $this->errorMessage = 'Erro no upload: ' . $e->getMessage();
            Log::error('Test image upload failed', [
                'error' => $e->getMessage(),
                'file_size' => $this->testImage ? $this->testImage->getSize() : 'unknown'
            ]);
        }
    }

    public function testVideoUpload()
    {
        try {
            $this->validate(['testVideo' => 'required|file|mimetypes:video/mp4,video/quicktime,video/avi,video/webm|max:204800']);

            if (!$this->testVideo->isValid()) {
                throw new \Exception('Arquivo de vídeo inválido ou corrompido.');
            }

            $filename = 'test_' . time() . '_' . uniqid() . '.' . $this->testVideo->getClientOriginalExtension();
            $path = $this->testVideo->storeAs('test-uploads/videos', $filename, 'public');

            if (!$path) {
                throw new \Exception('Falha ao salvar o vídeo no servidor.');
            }

            $this->uploadStatus = "✅ Vídeo enviado com sucesso!\n";
            $this->uploadStatus .= "📁 Arquivo: {$filename}\n";
            $this->uploadStatus .= "📏 Tamanho: " . $this->formatBytes($this->testVideo->getSize()) . "\n";
            $this->uploadStatus .= "🔗 URL: " . Storage::url($path);

            Log::info('Test video upload successful', [
                'filename' => $filename,
                'path' => $path,
                'size' => $this->testVideo->getSize()
            ]);

            $this->errorMessage = '';
            $this->reset('testVideo');

        } catch (\Illuminate\Validation\ValidationException $e) {
            $this->errorMessage = 'Erro de validação: ' . implode(', ', $e->validator->errors()->all());
        } catch (\Exception $e) {
            $this->errorMessage = 'Erro no upload: ' . $e->getMessage();
            Log::error('Test video upload failed', [
                'error' => $e->getMessage(),
                'file_size' => $this->testVideo ? $this->testVideo->getSize() : 'unknown'
            ]);
        }
    }

    public function clearResults()
    {
        $this->uploadStatus = '';
        $this->errorMessage = '';
        $this->reset(['testImage', 'testVideo']);
    }

    private function formatBytes($bytes)
    {
        if ($bytes >= 1073741824) {
            return number_format($bytes / 1073741824, 2) . ' GB';
        } elseif ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }

    public function render()
    {
        return view('livewire.upload-test');
    }
}
