<?php

namespace App\Livewire\Admin\PremiumAlbums;

use App\Models\Album;
use App\Models\AlbumPurchase;
use App\Models\AlbumEarning;
use App\Models\User;
use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AdminReports extends Component
{
    public $period = 'month'; // today, week, month, year, all
    public $startDate = '';
    public $endDate = '';
    
    public $activeTab = 'overview'; // overview, sales, creators, albums

    public function mount()
    {
        // Verificar se é admin
        if (Auth::user()->role !== 'administrador') {
            abort(403, 'Acesso negado.');
        }

        $this->endDate = now()->format('Y-m-d');
        $this->startDate = now()->subDays(30)->format('Y-m-d');
    }

    public function updatedPeriod()
    {
        switch ($this->period) {
            case 'today':
                $this->startDate = now()->format('Y-m-d');
                $this->endDate = now()->format('Y-m-d');
                break;
            case 'week':
                $this->startDate = now()->startOfWeek()->format('Y-m-d');
                $this->endDate = now()->endOfWeek()->format('Y-m-d');
                break;
            case 'month':
                $this->startDate = now()->startOfMonth()->format('Y-m-d');
                $this->endDate = now()->endOfMonth()->format('Y-m-d');
                break;
            case 'year':
                $this->startDate = now()->startOfYear()->format('Y-m-d');
                $this->endDate = now()->endOfYear()->format('Y-m-d');
                break;
        }
    }

    public function setActiveTab($tab)
    {
        $this->activeTab = $tab;
    }

    public function getOverviewStatsProperty()
    {
        $dateFilter = $this->getDateFilter();

        return [
            'total_albums' => Album::premium()->count(),
            'pending_albums' => Album::premium()->where('admin_approved', false)->where('status', '!=', 'suspended')->count(),
            'total_sales' => AlbumPurchase::completed()->when($dateFilter, function($q) use ($dateFilter) {
                return $q->whereBetween('purchased_at', $dateFilter);
            })->count(),
            'total_revenue' => AlbumPurchase::completed()->when($dateFilter, function($q) use ($dateFilter) {
                return $q->whereBetween('purchased_at', $dateFilter);
            })->sum('amount'),
            'platform_commission' => AlbumPurchase::completed()->when($dateFilter, function($q) use ($dateFilter) {
                return $q->whereBetween('purchased_at', $dateFilter);
            })->sum('commission'),
            'active_creators' => User::whereHas('premiumAlbums', function($q) {
                $q->published();
            })->count(),
            'top_creator' => $this->getTopCreator(),
            'avg_album_price' => Album::premium()->published()->avg('price') ?? 0,
        ];
    }

    public function getSalesDataProperty()
    {
        $dateFilter = $this->getDateFilter();
        
        $sales = AlbumPurchase::with(['album', 'buyer', 'seller'])
                             ->completed()
                             ->when($dateFilter, function($q) use ($dateFilter) {
                                 return $q->whereBetween('purchased_at', $dateFilter);
                             })
                             ->orderBy('purchased_at', 'desc')
                             ->limit(50)
                             ->get();

        $dailySales = AlbumPurchase::completed()
                                  ->when($dateFilter, function($q) use ($dateFilter) {
                                      return $q->whereBetween('purchased_at', $dateFilter);
                                  })
                                  ->selectRaw('DATE(purchased_at) as date, COUNT(*) as count, SUM(amount) as revenue')
                                  ->groupBy('date')
                                  ->orderBy('date')
                                  ->get();

        return [
            'recent_sales' => $sales,
            'daily_sales' => $dailySales,
            'payment_methods' => AlbumPurchase::completed()
                                            ->when($dateFilter, function($q) use ($dateFilter) {
                                                return $q->whereBetween('purchased_at', $dateFilter);
                                            })
                                            ->selectRaw('payment_method, COUNT(*) as count, SUM(amount) as revenue')
                                            ->groupBy('payment_method')
                                            ->get(),
        ];
    }

    public function getCreatorsDataProperty()
    {
        $dateFilter = $this->getDateFilter();

        return [
            'top_creators' => User::withCount(['premiumAlbums as published_albums' => function($q) {
                                $q->published();
                            }])
                            ->withSum(['albumSales as total_revenue' => function($q) use ($dateFilter) {
                                $q->completed()->when($dateFilter, function($query) use ($dateFilter) {
                                    return $query->whereBetween('purchased_at', $dateFilter);
                                });
                            }], 'amount')
                            ->withCount(['albumSales as total_sales' => function($q) use ($dateFilter) {
                                $q->completed()->when($dateFilter, function($query) use ($dateFilter) {
                                    return $query->whereBetween('purchased_at', $dateFilter);
                                });
                            }])
                            ->whereHas('premiumAlbums')
                            ->orderByDesc('total_revenue')
                            ->limit(20)
                            ->get(),
            'new_creators' => User::whereHas('premiumAlbums', function($q) use ($dateFilter) {
                                $q->when($dateFilter, function($query) use ($dateFilter) {
                                    return $query->whereBetween('created_at', $dateFilter);
                                });
                            })
                            ->withCount('premiumAlbums')
                            ->orderBy('created_at', 'desc')
                            ->limit(10)
                            ->get(),
        ];
    }

    public function getAlbumsDataProperty()
    {
        $dateFilter = $this->getDateFilter();

        return [
            'top_albums' => Album::premium()
                                ->with('user')
                                ->withCount(['purchases as sales_count' => function($q) use ($dateFilter) {
                                    $q->completed()->when($dateFilter, function($query) use ($dateFilter) {
                                        return $query->whereBetween('purchased_at', $dateFilter);
                                    });
                                }])
                                ->withSum(['purchases as total_revenue' => function($q) use ($dateFilter) {
                                    $q->completed()->when($dateFilter, function($query) use ($dateFilter) {
                                        return $query->whereBetween('purchased_at', $dateFilter);
                                    });
                                }], 'amount')
                                ->orderByDesc('sales_count')
                                ->limit(20)
                                ->get(),
            'recent_albums' => Album::premium()
                                   ->with('user')
                                   ->withCount('purchases')
                                   ->when($dateFilter, function($q) use ($dateFilter) {
                                       return $q->whereBetween('created_at', $dateFilter);
                                   })
                                   ->orderBy('created_at', 'desc')
                                   ->limit(10)
                                   ->get(),
            'price_distribution' => Album::premium()
                                        ->published()
                                        ->selectRaw('
                                            CASE 
                                                WHEN price < 10 THEN "< R$ 10"
                                                WHEN price < 25 THEN "R$ 10-25"
                                                WHEN price < 50 THEN "R$ 25-50"
                                                WHEN price < 100 THEN "R$ 50-100"
                                                ELSE "> R$ 100"
                                            END as price_range,
                                            COUNT(*) as count
                                        ')
                                        ->groupBy('price_range')
                                        ->get(),
        ];
    }

    private function getDateFilter()
    {
        if ($this->period === 'all') {
            return null;
        }

        return [
            Carbon::parse($this->startDate)->startOfDay(),
            Carbon::parse($this->endDate)->endOfDay()
        ];
    }

    private function getTopCreator()
    {
        $dateFilter = $this->getDateFilter();
        
        return User::withSum(['albumSales as total_revenue' => function($q) use ($dateFilter) {
                        $q->completed()->when($dateFilter, function($query) use ($dateFilter) {
                            return $query->whereBetween('purchased_at', $dateFilter);
                        });
                    }], 'amount')
                   ->whereHas('premiumAlbums')
                   ->orderByDesc('total_revenue')
                   ->first();
    }

    public function render()
    {
        $data = [];
        
        switch ($this->activeTab) {
            case 'overview':
                $data['overviewStats'] = $this->overviewStats;
                break;
            case 'sales':
                $data['salesData'] = $this->salesData;
                break;
            case 'creators':
                $data['creatorsData'] = $this->creatorsData;
                break;
            case 'albums':
                $data['albumsData'] = $this->albumsData;
                break;
        }

        return view('livewire.admin.premium-albums.admin-reports', $data)
               ->layout('layouts.app');
    }
}
