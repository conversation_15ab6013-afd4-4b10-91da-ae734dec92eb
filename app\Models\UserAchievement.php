<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\User; // Importar o modelo User
use App\Models\Achievement; // Importar o modelo Achievement

class UserAchievement extends Model
{
    protected $table = 'user_achievements'; // Nome da tabela pivô

    // Relacionamento com o usuário
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // Relacionamento com a conquista
    public function achievement(): BelongsTo
    {
        return $this->belongsTo(Achievement::class);
    }
}
