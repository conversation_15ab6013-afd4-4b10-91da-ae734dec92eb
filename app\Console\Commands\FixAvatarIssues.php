<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\UserPhoto;
use App\Models\User;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;

class FixAvatarIssues extends Command
{
    protected $signature = 'fix:avatars';
    protected $description = 'Fix avatar issues - remove orphaned files and fix database inconsistencies';

    public function handle()
    {
        $this->info('Starting avatar fixes...');

        // 1. Fix missing is_current column values
        $this->fixMissingCurrentFlags();

        // 2. Remove orphaned avatar files
        $this->removeOrphanedFiles();

        // 3. Fix users without current avatar
        $this->fixUsersWithoutCurrentAvatar();

        $this->info('Avatar fixes completed!');
    }

    private function fixMissingCurrentFlags()
    {
        $this->info('Fixing missing is_current flags...');

        // Get all users
        $users = User::all();

        foreach ($users as $user) {
            $photos = $user->userPhotos()->get();
            
            if ($photos->count() > 0) {
                // Check if any photo is marked as current
                $currentPhoto = $photos->where('is_current', true)->first();
                
                if (!$currentPhoto) {
                    // Mark the latest photo as current
                    $latestPhoto = $photos->sortByDesc('created_at')->first();
                    $latestPhoto->is_current = true;
                    $latestPhoto->save();
                    
                    $this->info("Set current avatar for user {$user->username}");
                }
            }
        }
    }

    private function removeOrphanedFiles()
    {
        $this->info('Checking for orphaned avatar files...');

        // Get all avatar files from storage
        $avatarFiles = Storage::disk('public')->files('avatars');
        
        // Get all photo paths from database
        $dbPhotoPaths = UserPhoto::pluck('photo_path')->toArray();
        
        $orphanedFiles = [];
        
        foreach ($avatarFiles as $file) {
            $found = false;
            foreach ($dbPhotoPaths as $dbPath) {
                if (str_contains($dbPath, basename($file))) {
                    $found = true;
                    break;
                }
            }
            
            if (!$found) {
                $orphanedFiles[] = $file;
            }
        }

        if (count($orphanedFiles) > 0) {
            $this->info('Found ' . count($orphanedFiles) . ' orphaned files');
            
            if ($this->confirm('Do you want to delete orphaned files?')) {
                foreach ($orphanedFiles as $file) {
                    Storage::disk('public')->delete($file);
                    $this->info("Deleted: {$file}");
                }
            }
        } else {
            $this->info('No orphaned files found');
        }
    }

    private function fixUsersWithoutCurrentAvatar()
    {
        $this->info('Fixing users without current avatar...');

        // Find users with photos but no current avatar
        $usersWithoutCurrent = User::whereHas('userPhotos')
            ->whereDoesntHave('userPhotos', function($query) {
                $query->where('is_current', true);
            })
            ->get();

        foreach ($usersWithoutCurrent as $user) {
            $latestPhoto = $user->userPhotos()->latest()->first();
            if ($latestPhoto) {
                $latestPhoto->is_current = true;
                $latestPhoto->save();
                $this->info("Fixed current avatar for user {$user->username}");
            }
        }
    }
}
