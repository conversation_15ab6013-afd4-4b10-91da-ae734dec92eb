<div class="max-w-7xl mx-auto p-6">
    <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-300 mb-2">Gerenciar Labels de Usuários</h1>
        <p class="text-gray-400">Ad<PERSON><PERSON>, edite ou remova labels dos usuários</p>
    </div>

    @if (session()->has('success'))
        <div class="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded">
            {{ session('success') }}
        </div>
    @endif

    @if (session()->has('error'))
        <div class="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
            {{ session('error') }}
        </div>
    @endif

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Lista de Usuários -->
        <div class="bg-zinc-800 rounded-lg shadow-md">
            <div class="p-4 border-b border-zinc-700">
                <h2 class="text-lg font-semibold text-gray-300 mb-4">Buscar Usuários</h2>

                <flux:input
                    wire:model.live="search"
                    placeholder="Buscar por nome, username ou email..."
                    class="w-full"
                />
            </div>

            <div class="p-4">
                <div class="space-y-3">
                    @forelse($users as $user)
                        <div
                            wire:click="selectUser({{ $user->id }})"
                            class="flex items-center justify-between p-3 bg-zinc-700 rounded-lg hover:bg-zinc-600 cursor-pointer transition {{ $selectedUser && $selectedUser->id === $user->id ? 'ring-2 ring-blue-500' : '' }}"
                        >
                            <div class="flex items-center space-x-3">
                                @php
                                    $userAvatar = $user->currentPhoto ?? $user->userPhotos->first();
                                    $userAvatarUrl = $userAvatar ? Storage::url($userAvatar->photo_path) : asset('images/default-avatar.jpg');
                                @endphp
                                <img src="{{ $userAvatarUrl }}"
                                     alt="{{ $user->name }}"
                                     class="w-10 h-10 rounded-full object-cover">

                                <div>
                                    <p class="font-medium text-gray-300">{{ $user->name }}</p>
                                    <p class="text-sm text-gray-400">{{ '@' . $user->username }}</p>
                                </div>
                            </div>

                            <div class="text-right">
                                <span class="text-xs text-gray-400">
                                    {{ $user->labels()->count() }} labels
                                </span>
                            </div>
                        </div>
                    @empty
                        <p class="text-gray-400 text-center py-4">Nenhum usuário encontrado</p>
                    @endforelse
                </div>

                <div class="mt-4">
                    {{ $users->links() }}
                </div>
            </div>
        </div>

        <!-- Gerenciar Labels do Usuário Selecionado -->
        <div class="bg-zinc-800 rounded-lg shadow-md">
            @if($selectedUser)
                <div class="p-4 border-b border-zinc-700">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            @php
                                $selectedUserAvatar = $selectedUser->currentPhoto ?? $selectedUser->userPhotos->first();
                                $selectedUserAvatarUrl = $selectedUserAvatar ? Storage::url($selectedUserAvatar->photo_path) : asset('images/default-avatar.jpg');
                            @endphp
                            <img src="{{ $selectedUserAvatarUrl }}"
                                 alt="{{ $selectedUser->name }}"
                                 class="w-12 h-12 rounded-full object-cover">

                            <div>
                                <h2 class="text-lg font-semibold text-gray-300">{{ $selectedUser->name }}</h2>
                                <p class="text-sm text-gray-400">{{ '@' . $selectedUser->username }}</p>
                            </div>
                        </div>

                        <flux:button wire:click="openModal" color="primary">
                            <x-flux::icon name="plus" class="w-4 h-4 mr-2" />
                            Aplicar Label
                        </flux:button>
                    </div>
                </div>

                <div class="p-4">
                    <h3 class="text-md font-medium text-gray-300 mb-4">Labels Atuais</h3>

                    @if($selectedUser->labels && $selectedUser->labels->count() > 0)
                        <div class="space-y-3">
                            @foreach($selectedUser->labels as $label)
                                <div class="flex items-center justify-between p-3 bg-zinc-700 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <span class="user-label {{ $label->color }}">
                                            @if($label->icon)
                                                <x-flux::icon :name="$label->icon" class="icon" />
                                            @endif
                                            {{ $label->name }}
                                        </span>

                                        <div class="text-sm text-gray-400">
                                            @if($label->description)
                                                <p>{{ $label->description }}</p>
                                            @endif
                                            @if($label->expires_at)
                                                <p>Expira: {{ $label->expires_at->format('d/m/Y H:i') }}</p>
                                            @endif
                                            @if(!$label->is_active)
                                                <span class="text-red-400">(Inativa)</span>
                                            @endif
                                        </div>
                                    </div>

                                    <div class="flex items-center space-x-2">
                                        <flux:button
                                            wire:click="editLabel({{ $label->id }})"
                                            size="sm"
                                            variant="ghost"
                                        >
                                            <x-flux::icon name="pencil" class="w-4 h-4" />
                                        </flux:button>

                                        <flux:button
                                            wire:click="toggleLabelStatus({{ $label->id }})"
                                            size="sm"
                                            variant="ghost"
                                            color="{{ $label->is_active ? 'yellow' : 'green' }}"
                                        >
                                            <x-flux::icon name="{{ $label->is_active ? 'eye-slash' : 'eye' }}" class="w-4 h-4" />
                                        </flux:button>

                                        <flux:button
                                            wire:click="removeLabel({{ $label->id }})"
                                            wire:confirm="Tem certeza que deseja remover esta label?"
                                            size="sm"
                                            variant="ghost"
                                            color="red"
                                        >
                                            <x-flux::icon name="trash" class="w-4 h-4" />
                                        </flux:button>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <p class="text-gray-400 text-center py-8">Este usuário não possui labels</p>
                    @endif
                </div>
            @else
                <div class="p-8 text-center">
                    <x-flux::icon name="user-group" class="w-16 h-16 text-gray-400 mx-auto mb-4" />
                    <h3 class="text-lg font-medium text-gray-300 mb-2">Selecione um usuário</h3>
                    <p class="text-gray-400">Escolha um usuário da lista para gerenciar suas labels</p>
                </div>
            @endif
        </div>
    </div>

    <!-- Modal para Adicionar/Editar Label -->
    @if($showModal)
        <flux:modal wire:model="showModal" class="max-w-md">
            <flux:modal.header>
                <flux:modal.title>
                    {{ $editingLabel ? 'Editar Label' : 'Aplicar Label' }}
                </flux:modal.title>
            </flux:modal.header>

            <flux:modal.body>
                <div class="space-y-6">
                    <div>
                        <flux:label class="text-base font-medium">Selecione uma Label</flux:label>
                        <flux:description>Escolha uma das labels pré-definidas para aplicar ao usuário</flux:description>

                        <div class="grid grid-cols-2 gap-3 mt-4">
                            @foreach($availableLabels as $label)
                                <div
                                    wire:click="$set('selectedLabelName', '{{ $label['name'] }}')"
                                    class="cursor-pointer p-3 border-2 rounded-lg transition-all hover:bg-zinc-50 dark:hover:bg-zinc-700 {{ $selectedLabelName === $label['name'] ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' : 'border-gray-200 dark:border-zinc-600' }}"
                                >
                                    <div class="text-center">
                                        <div class="label {{ $label['color'] }} mx-auto mb-2" style="transform: scale(0.6); margin-bottom: -10px;">
                                            <div class="circle">
                                                <i class="fa {{ $label['fa_icon'] }}"></i>
                                            </div>
                                            <div class="ribbon">
                                                <div class="font">{{ $label['name'] }}</div>
                                            </div>
                                        </div>
                                        <p class="text-xs text-gray-600 dark:text-gray-400 mt-2">{{ $label['description'] }}</p>
                                    </div>
                                </div>
                            @endforeach
                        </div>

                        <flux:error name="selectedLabelName" />
                    </div>

                    <div>
                        <flux:field>
                            <flux:label>Data de Expiração (opcional)</flux:label>
                            <flux:input type="datetime-local" wire:model="expiresAt" />
                            <flux:error name="expiresAt" />
                            <flux:description>Deixe em branco para label permanente</flux:description>
                        </flux:field>
                    </div>

                    <!-- Preview da Label Selecionada -->
                    @if($selectedLabelName)
                        @php
                            $selectedLabel = collect($availableLabels)->firstWhere('name', $selectedLabelName);
                        @endphp
                        @if($selectedLabel)
                            <div>
                                <flux:label>Preview</flux:label>
                                <div class="mt-2 text-center">
                                    <span class="user-label {{ $selectedLabel['color'] }}">
                                        <x-flux::icon :name="$selectedLabel['icon']" class="icon" />
                                        {{ $selectedLabel['name'] }}
                                    </span>
                                </div>
                            </div>
                        @endif
                    @endif
                </div>
            </flux:modal.body>

            <flux:modal.footer>
                <flux:button wire:click="closeModal" variant="ghost">Cancelar</flux:button>
                <flux:button
                    wire:click="{{ $editingLabel ? 'updateLabel' : 'addLabel' }}"
                    color="primary"
                    :disabled="!$selectedLabelName"
                >
                    {{ $editingLabel ? 'Atualizar' : 'Aplicar' }}
                </flux:button>
            </flux:modal.footer>
        </flux:modal>
    @endif
</div>
