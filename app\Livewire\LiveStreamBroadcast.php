<?php

namespace App\Livewire;

use App\Models\LiveStream;
use App\Models\LiveStreamMessage;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Livewire\Component;

class LiveStreamBroadcast extends Component
{
    public $liveStream;
    public $title = '';
    public $description = '';
    public $isLive = false;
    public $viewers = 0;
    public $totalDonations = 0;
    public $messages = [];
    public $newMessage = '';
    public $showCreateModal = false;
    public $showEndModal = false;

    protected $listeners = [
        'live-stream-message' => 'addMessage',
        'live-stream-donation' => 'addDonation',
        'viewer-joined' => 'incrementViewers',
        'viewer-left' => 'decrementViewers',
    ];

    public function mount()
    {
        // Verificar se o usuário já tem uma live ativa
        $this->liveStream = LiveStream::where('user_id', Auth::id())
            ->whereIn('status', ['waiting', 'live'])
            ->first();

        if ($this->liveStream) {
            $this->title = $this->liveStream->title;
            $this->description = $this->liveStream->description;
            $this->isLive = $this->liveStream->isLive();
            $this->viewers = $this->liveStream->viewers_count;
            $this->totalDonations = $this->liveStream->total_donations;
            $this->loadMessages();
        }
    }

    public function createLiveStream()
    {
        try {
            $this->validate([
                'title' => 'required|string|max:255',
                'description' => 'nullable|string|max:1000',
            ]);

            // Verificar se já existe uma live ativa
            $existingLive = LiveStream::where('user_id', Auth::id())
                ->whereIn('status', ['waiting', 'live'])
                ->first();

            if ($existingLive) {
                $this->addError('title', 'Você já tem uma live ativa. Termine a live atual antes de criar uma nova.');
                return;
            }

            $this->liveStream = LiveStream::create([
                'user_id' => Auth::id(),
                'title' => $this->title,
                'description' => $this->description,
                'status' => 'waiting',
            ]);

            // Verificar se a live foi criada com sucesso
            if (!$this->liveStream || !$this->liveStream->id) {
                throw new \Exception('Falha ao criar live stream');
            }

            $this->showCreateModal = false;
            $this->dispatch('live-stream-created', $this->liveStream->id);

            $this->dispatch(
                'toast',
                message: 'Live criada com sucesso! Configure sua câmera e clique em "Iniciar Live".',
                type: 'success'
            )->to('toast-notification');

            Log::info('Live criada com sucesso', [
                'stream_id' => $this->liveStream->id,
                'user_id' => Auth::id(),
                'title' => $this->title
            ]);
        } catch (\Exception $e) {
            Log::error('Erro ao criar live stream: ' . $e->getMessage(), [
                'user_id' => Auth::id(),
                'title' => $this->title,
                'trace' => $e->getTraceAsString()
            ]);

            $this->dispatch(
                'toast',
                message: 'Erro ao criar live. Tente novamente.',
                type: 'error'
            )->to('toast-notification');
        }
    }

    public function startLive()
    {
        try {
            if (!$this->liveStream || !$this->liveStream->id) {
                throw new \Exception('Live stream não encontrada');
            }

            $this->liveStream->start();
            $this->isLive = true;

            // Adicionar mensagem do sistema
            $this->addSystemMessage(Auth::user()->name . ' iniciou a live!');

            // Log para debug
            Log::info('Live iniciada', ['stream_id' => $this->liveStream->id, 'user_id' => Auth::id()]);

            $this->dispatch('live-stream-started', $this->liveStream->id);

            // Disparar evento JavaScript para iniciar streaming real
            $this->dispatch('start-real-streaming');

            // Notificar viewers que a câmera está sendo preparada
            $this->dispatch('broadcaster-camera-preparing', $this->liveStream->id);

            $this->dispatch(
                'toast',
                message: 'Live iniciada com sucesso! Streaming WebRTC ativo.',
                type: 'success'
            )->to('toast-notification');
        } catch (\Exception $e) {
            Log::error('Erro ao iniciar live: ' . $e->getMessage(), [
                'user_id' => Auth::id(),
                'liveStream_id' => $this->liveStream?->id
            ]);

            $this->dispatch(
                'toast',
                message: 'Erro ao iniciar live. Tente novamente.',
                type: 'error'
            )->to('toast-notification');
        }
    }

    public function notifyCameraActive()
    {
        if ($this->liveStream && $this->isLive) {
            // Notificar todos os viewers que a câmera está ativa
            $this->dispatch('broadcaster-camera-active', $this->liveStream->id);

            Log::info('Câmera ativada para live', [
                'stream_id' => $this->liveStream->id,
                'user_id' => Auth::id()
            ]);
        }
    }

    public function endLive()
    {
        if (!$this->liveStream) {
            return;
        }

        // Calcular duração da live
        $duration = $this->liveStream->started_at ?
            now()->diffInSeconds($this->liveStream->started_at) : 0;

        // Simular criação de replay (em produção, aqui seria o processamento real do vídeo)
        $this->createReplaySimulation($duration);

        $this->liveStream->end();
        $this->isLive = false;

        // Adicionar mensagem do sistema
        $this->addSystemMessage(Auth::user()->name . ' encerrou a live. Obrigado por assistir!');

        $this->dispatch('live-stream-ended', $this->liveStream->id);
        $this->showEndModal = false;

        $this->dispatch(
            'toast',
            message: 'Live encerrada! Replay será processado em alguns minutos.',
            type: 'success'
        )->to('toast-notification');
    }

    public function sendMessage()
    {
        if (!$this->liveStream || empty(trim($this->newMessage))) {
            return;
        }

        $message = LiveStreamMessage::create([
            'live_stream_id' => $this->liveStream->id,
            'user_id' => Auth::id(),
            'message' => $this->newMessage,
            'type' => 'message',
        ]);

        $this->newMessage = '';
        $this->loadMessages();

        // Broadcast para todos os viewers
        $this->dispatch('live-stream-message', [
            'stream_id' => $this->liveStream->id,
            'message' => $message->toArray(),
            'user' => Auth::user()->toArray(),
        ]);
    }

    public function addMessage($data)
    {
        if ($data['stream_id'] == $this->liveStream?->id) {
            $this->loadMessages();
        }
    }

    public function addDonation($data)
    {
        if ($data['stream_id'] == $this->liveStream?->id) {
            $this->totalDonations = $this->liveStream->fresh()->total_donations;
            $this->loadMessages();
        }
    }

    public function incrementViewers()
    {
        $this->viewers++;
    }

    public function decrementViewers()
    {
        $this->viewers = max(0, $this->viewers - 1);
    }

    private function loadMessages()
    {
        if (!$this->liveStream) {
            return;
        }

        $this->messages = LiveStreamMessage::where('live_stream_id', $this->liveStream->id)
            ->with('user.userPhotos')
            ->latest()
            ->take(50)
            ->get()
            ->reverse()
            ->values()
            ->toArray();
    }

    private function addSystemMessage($message)
    {
        try {
            if (!$this->liveStream || !$this->liveStream->id) {
                Log::warning('Tentativa de adicionar mensagem do sistema sem live stream válida', [
                    'message' => $message,
                    'user_id' => Auth::id()
                ]);
                return;
            }

            LiveStreamMessage::create([
                'live_stream_id' => $this->liveStream->id,
                'user_id' => Auth::id(),
                'message' => $message,
                'type' => 'system',
            ]);

            $this->loadMessages();
        } catch (\Exception $e) {
            Log::error('Erro ao adicionar mensagem do sistema: ' . $e->getMessage(), [
                'message' => $message,
                'user_id' => Auth::id(),
                'liveStream_id' => $this->liveStream?->id
            ]);
        }
    }

    public function openCreateModal()
    {
        $this->showCreateModal = true;
    }

    public function closeCreateModal()
    {
        $this->showCreateModal = false;
        $this->reset(['title', 'description']);
    }

    public function openEndModal()
    {
        $this->showEndModal = true;
    }

    public function closeEndModal()
    {
        $this->showEndModal = false;
    }

    /**
     * Simula a criação de um replay da live
     * Em produção, aqui seria implementado o processamento real do vídeo
     */
    private function createReplaySimulation($duration)
    {
        // Por enquanto, vamos usar um vídeo de exemplo
        // Em produção, aqui seria o processamento do stream gravado
        $exampleVideos = [
            'videos/example-live-1.mp4',
            'videos/example-live-2.mp4',
            'videos/example-live-3.mp4',
        ];

        $videoPath = $exampleVideos[array_rand($exampleVideos)];

        // Salvar o replay na live stream
        $this->liveStream->saveReplay($videoPath, $duration);

        // Log para debug
        Log::info('Replay criado para live', [
            'stream_id' => $this->liveStream->id,
            'duration' => $duration,
            'video_path' => $videoPath
        ]);
    }

    public function render()
    {
        return view('livewire.live-stream-broadcast');
    }
}
