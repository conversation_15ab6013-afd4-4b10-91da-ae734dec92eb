<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AlbumEarning extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'album_id',
        'purchase_id',
        'gross_amount',
        'commission',
        'net_amount',
        'status',
        'paid_at',
        'payment_reference',
    ];

    protected $casts = [
        'gross_amount' => 'decimal:2',
        'commission' => 'decimal:2',
        'net_amount' => 'decimal:2',
        'paid_at' => 'datetime',
    ];

    /**
     * Status possíveis para ganhos.
     */
    const STATUS_PENDING = 'pending';
    const STATUS_PAID = 'paid';
    const STATUS_HELD = 'held';

    /**
     * Usuário que recebeu o ganho.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Álbum que gerou o ganho.
     */
    public function album()
    {
        return $this->belongsTo(Album::class);
    }

    /**
     * Compra que gerou o ganho.
     */
    public function purchase()
    {
        return $this->belongsTo(AlbumPurchase::class, 'purchase_id');
    }

    /**
     * Scope para ganhos pendentes.
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * Scope para ganhos pagos.
     */
    public function scopePaid($query)
    {
        return $query->where('status', self::STATUS_PAID);
    }

    /**
     * Scope para ganhos retidos.
     */
    public function scopeHeld($query)
    {
        return $query->where('status', self::STATUS_HELD);
    }

    /**
     * Scope para ganhos por período.
     */
    public function scopeByPeriod($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Marca o ganho como pago.
     */
    public function markAsPaid($paymentReference = null)
    {
        $this->update([
            'status' => self::STATUS_PAID,
            'paid_at' => now(),
            'payment_reference' => $paymentReference,
        ]);
    }

    /**
     * Marca o ganho como retido.
     */
    public function markAsHeld()
    {
        $this->update([
            'status' => self::STATUS_HELD,
        ]);
    }

    /**
     * Verifica se o ganho está pendente.
     */
    public function isPending()
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Verifica se o ganho foi pago.
     */
    public function isPaid()
    {
        return $this->status === self::STATUS_PAID;
    }

    /**
     * Verifica se o ganho está retido.
     */
    public function isHeld()
    {
        return $this->status === self::STATUS_HELD;
    }
}
