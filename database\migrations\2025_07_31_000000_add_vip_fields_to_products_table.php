<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // Campos para produtos que concedem acesso VIP
            $table->boolean('grants_vip_access')->default(false)->after('is_digital');
            $table->integer('vip_duration_days')->nullable()->after('grants_vip_access');
            $table->string('vip_plan_type')->nullable()->after('vip_duration_days'); // 'monthly', 'yearly', 'custom'
            $table->text('vip_benefits')->nullable()->after('vip_plan_type'); // Benefícios específicos do VIP

            // Campos para produtos que adicionam crédito à carteira
            $table->boolean('adds_wallet_credit')->default(false)->after('vip_benefits');
            $table->decimal('wallet_credit_amount', 10, 2)->nullable()->after('adds_wallet_credit');

            // Índices para performance
            $table->index('grants_vip_access');
            $table->index('adds_wallet_credit');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropIndex(['grants_vip_access']);
            $table->dropIndex(['adds_wallet_credit']);
            $table->dropColumn([
                'grants_vip_access',
                'vip_duration_days',
                'vip_plan_type',
                'vip_benefits',
                'adds_wallet_credit',
                'wallet_credit_amount'
            ]);
        });
    }
};
