<div>
    <h1>Busca Avançada - Teste</h1>
    <p>Encontre usuários, posts e eventos na plataforma.</p>
    
    <div class="p-6 border rounded-lg">
        <form wire:submit.prevent="search">
            
            <!-- Tipo de busca -->
            <div class="mb-4">
                <label>Tipo de Busca:</label>
                <select wire:model.live="searchType" class="form-control">
                    <option value="users">👥 Usuários</option>
                    <option value="posts">📝 Posts</option>
                    <option value="events">🎉 Eventos</option>
                </select>
            </div>
            
            <!-- Campo de busca -->
            <div class="mb-4">
                <label>Buscar:</label>
                <input type="text" wire:model="searchQuery" placeholder="Digite sua busca..." class="form-control">
            </div>
            
            <!-- Filtros para usuários -->
            @if(($searchType ?? 'users') === 'users')
            <div class="mb-4">
                <h3>Filtros para Usuários</h3>
                
                <div class="mb-2">
                    <label>Perfil com Foto?</label>
                    <input type="radio" wire:model="filters.foto" value="1" checked> Sim
                    <input type="radio" wire:model="filters.foto" value="0"> Não
                </div>
                
                <div class="mb-2">
                    <label>Sexo:</label>
                    <input type="checkbox" wire:model="filters.sexo" value="casal"> Casal
                    <input type="checkbox" wire:model="filters.sexo" value="homem"> Homem
                    <input type="checkbox" wire:model="filters.sexo" value="mulher"> Mulher
                </div>
            </div>
            @endif
            
            <!-- Filtros para posts -->
            @if(($searchType ?? 'users') === 'posts')
            <div class="mb-4">
                <h3>Filtros para Posts</h3>
                <div class="mb-2">
                    <label>Ordenar por:</label>
                    <input type="radio" wire:model="filters.ordenar" value="id_decrescente" checked> Mais Recentes
                    <input type="radio" wire:model="filters.ordenar" value="id_crescente"> Mais Antigos
                </div>
            </div>
            @endif
            
            <!-- Filtros para eventos -->
            @if(($searchType ?? 'users') === 'events')
            <div class="mb-4">
                <h3>Filtros para Eventos</h3>
                <div class="mb-2">
                    <label>Ordenar por:</label>
                    <input type="radio" wire:model="filters.ordenar" value="date" checked> Data do Evento
                    <input type="radio" wire:model="filters.ordenar" value="id_decrescente"> Mais Recentes
                </div>
            </div>
            @endif
            
            <!-- Botão de busca -->
            <div class="text-center">
                <button type="submit" class="btn btn-primary">
                    @if(($searchType ?? 'users') === 'users')
                        Buscar Usuários
                    @elseif(($searchType ?? 'users') === 'posts')
                        Buscar Posts
                    @else
                        Buscar Eventos
                    @endif
                </button>
            </div>
        </form>
    </div>
    
    <!-- Resultados -->
    @if($hasSearched ?? false)
    <div class="mt-6 p-6 border rounded-lg">
        <h2>
            @if(($searchType ?? 'users') === 'users')
                👥 Usuários Encontrados
            @elseif(($searchType ?? 'users') === 'posts')
                📝 Posts Encontrados
            @else
                🎉 Eventos Encontrados
            @endif
        </h2>
        <p>{{ count($results ?? []) }} resultados encontrados</p>
        
        @if(($searchType ?? 'users') === 'users')
            <!-- Resultados de usuários -->
            @forelse($results ?? [] as $result)
                <div class="border p-4 mb-2">
                    <h3>{{ $result->name }}</h3>
                    <p>@{{ $result->username }}</p>
                </div>
            @empty
                <p>Nenhum usuário encontrado.</p>
            @endforelse
            
        @elseif(($searchType ?? 'users') === 'posts')
            <!-- Resultados de posts -->
            @forelse($results ?? [] as $post)
                <div class="border p-4 mb-2">
                    <h3>{{ $post->user->name }}</h3>
                    <p>{{ Str::limit($post->content, 100) }}</p>
                </div>
            @empty
                <p>Nenhum post encontrado.</p>
            @endforelse
            
        @else
            <!-- Resultados de eventos -->
            @forelse($results ?? [] as $event)
                <div class="border p-4 mb-2">
                    <h3>{{ $event->name }}</h3>
                    <p>{{ $event->date->format('d/m/Y') }}</p>
                </div>
            @empty
                <p>Nenhum evento encontrado.</p>
            @endforelse
        @endif
    </div>
    @endif
</div>
