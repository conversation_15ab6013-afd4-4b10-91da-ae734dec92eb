<div class="min-h-screen bg-black text-white">
    <div class="flex h-screen">
        <!-- <PERSON><PERSON> do Vídeo -->
        <div class="flex-1 flex flex-col">
            <!-- Header da Live -->
            <div class="bg-zinc-900 p-4 flex justify-between items-center">
                <div class="flex items-center gap-4">
                    <img src="{{ $liveStream->user->userPhotos->first() ? Storage::url($liveStream->user->userPhotos->first()->photo_path) : asset('images/default-avatar.jpg') }}" 
                         class="w-10 h-10 rounded-full">
                    <div>
                        <h1 class="text-xl font-bold">{{ $liveStream->title }}</h1>
                        <p class="text-gray-400">{{ $liveStream->user->name }}</p>
                        @if($liveStream->description)
                            <p class="text-sm text-gray-500">{{ $liveStream->description }}</p>
                        @endif
                    </div>
                </div>
                <div class="flex items-center gap-4">
                    <div class="flex items-center gap-2">
                        <x-flux::icon name="eye" class="w-5 h-5 text-purple-500" />
                        <span>{{ $liveStream->viewers_count }}</span>
                    </div>
                    @if($liveStream->isLive())
                        <div class="flex items-center gap-2">
                            <div class="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                            <span class="text-red-500 font-bold">AO VIVO</span>
                        </div>
                    @elseif($liveStream->isEnded())
                        <div class="flex items-center gap-2">
                            <div class="w-3 h-3 bg-gray-500 rounded-full"></div>
                            <span class="text-gray-500 font-bold">ENCERRADA</span>
                        </div>
                    @else
                        <div class="flex items-center gap-2">
                            <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                            <span class="text-yellow-500 font-bold">AGUARDANDO</span>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Área do Vídeo -->
            <div class="flex-1 bg-black relative">
                <div id="video-container" class="w-full h-full flex items-center justify-center">
                    @if($liveStream->isLive())
                        <video id="remoteVideo" autoplay class="w-full h-full object-cover"></video>

                        <!-- Status de Conexão -->
                        <div id="connection-status" class="absolute bottom-4 left-4 bg-black bg-opacity-70 text-white px-3 py-1 rounded text-xs">
                            🔄 Conectando ao stream...
                        </div>

                        <!-- Debug Info (apenas em desenvolvimento) -->
                        @if(config('app.debug'))
                        <div id="debug-info" class="absolute bottom-4 right-4 bg-black bg-opacity-70 text-white px-2 py-1 rounded text-xs max-w-xs">
                            <div>Stream ID: live-{{ $liveStream->id }}</div>
                            <div id="peer-id">Peer ID: ...</div>
                            <div id="connection-state">Estado: ...</div>
                        </div>
                        @endif
                    @elseif($liveStream->isEnded())
                        @if($liveStream->hasReplay())
                            <!-- Replay da Live -->
                            <div class="w-full h-full relative">
                                <video
                                    id="replayVideo"
                                    controls
                                    class="w-full h-full object-cover"
                                    poster="{{ asset('images/live-replay-poster.jpg') }}"
                                >
                                    <source src="{{ $liveStream->getReplayUrl() }}" type="video/mp4">
                                    Seu navegador não suporta reprodução de vídeo.
                                </video>

                                <!-- Overlay de Replay -->
                                <div class="absolute top-4 left-4 bg-black bg-opacity-70 text-white px-3 py-1 rounded-lg flex items-center gap-2">
                                    <x-flux::icon name="arrow-path" class="w-4 h-4" />
                                    <span class="text-sm font-medium">REPLAY</span>
                                    @if($liveStream->duration)
                                        <span class="text-xs text-gray-300">{{ $liveStream->getFormattedDuration() }}</span>
                                    @endif
                                </div>
                            </div>
                        @else
                            <div class="text-center">
                                <x-flux::icon name="stop-circle" class="w-16 h-16 mx-auto mb-4 text-gray-500" />
                                <p class="text-gray-400 mb-2">Live Encerrada</p>
                                <p class="text-sm text-gray-500">Replay não disponível</p>
                            </div>
                        @endif
                    @else
                        <div class="text-center">
                            <x-flux::icon name="clock" class="w-16 h-16 mx-auto mb-4 text-yellow-500" />
                            <p class="text-gray-400 mb-2">Aguardando Início</p>
                            <p class="text-sm text-gray-500">O streamer ainda não iniciou a transmissão</p>
                        </div>
                    @endif
                </div>

                <!-- Botão de Doação -->
                @if($liveStream->isLive())
                    <div class="absolute bottom-4 right-4">
                        <flux:button wire:click="openDonationModal" variant="primary" size="base">
                            <x-flux::icon name="heart" class="w-5 h-5 mr-2" />
                            Enviar Charm
                        </flux:button>
                    </div>
                @endif
            </div>
        </div>

        <!-- Chat da Live -->
        <div class="w-80 bg-zinc-900 flex flex-col">
            <!-- Header do Chat -->
            <div class="p-4 border-b border-zinc-700">
                <h3 class="font-bold">Chat da Live</h3>
            </div>

            <!-- Mensagens -->
            <div class="flex-1 overflow-y-auto p-4 space-y-3" id="chat-messages">
                @foreach($messages as $message)
                    <div class="flex gap-3">
                        @if($message['type'] === 'system')
                            <div class="w-full text-center">
                                <span class="text-yellow-400 text-sm">{{ $message['message'] }}</span>
                            </div>
                        @elseif($message['type'] === 'donation')
                            <div class="w-full bg-purple-900 bg-opacity-50 rounded-lg p-3">
                                <div class="flex items-center gap-2 mb-1">
                                    <img src="{{ $message['user']['user_photos'][0]['photo_path'] ?? asset('images/default-avatar.jpg') }}" 
                                         class="w-6 h-6 rounded-full">
                                    <span class="font-bold text-purple-300">{{ $message['user']['name'] }}</span>
                                    <x-flux::icon name="heart" class="w-4 h-4 text-red-500" />
                                </div>
                                <p class="text-sm">{{ $message['message'] }}</p>
                                <p class="text-xs text-purple-400 mt-1">
                                    Enviou {{ $message['data']['charm_name'] ?? 'um presente' }} 
                                    (R$ {{ number_format($message['data']['amount'] ?? 0, 2, ',', '.') }})
                                </p>
                            </div>
                        @else
                            <img src="{{ $message['user']['user_photos'][0]['photo_path'] ?? asset('images/default-avatar.jpg') }}" 
                                 class="w-8 h-8 rounded-full">
                            <div class="flex-1">
                                <div class="flex items-center gap-2">
                                    <span class="font-bold text-sm">{{ $message['user']['name'] }}</span>
                                    <span class="text-xs text-gray-400">
                                        {{ \Carbon\Carbon::parse($message['created_at'])->format('H:i') }}
                                    </span>
                                </div>
                                <p class="text-sm">{{ $message['message'] }}</p>
                            </div>
                        @endif
                    </div>
                @endforeach
            </div>

            <!-- Input de Mensagem -->
            @if($liveStream->isLive())
                <div class="p-4 border-t border-zinc-700">
                    <div class="flex gap-2">
                        <input 
                            wire:model="newMessage" 
                            wire:keydown.enter="sendMessage"
                            type="text" 
                            placeholder="Digite uma mensagem..."
                            class="flex-1 bg-zinc-800 border border-zinc-700 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                        >
                        <flux:button wire:click="sendMessage" variant="primary" size="sm">
                            <x-flux::icon name="paper-airplane" class="w-4 h-4" />
                        </flux:button>
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Modal de Doação -->
    @if($showDonationModal)
        <flux:modal wire:model="showDonationModal" size="lg">
            <flux:modal.header>
                <flux:heading size="base">Enviar Charm para {{ $liveStream->user->name }}</flux:heading>
            </flux:modal.header>

            <flux:modal.body>
                <div class="space-y-6">
                    <!-- Saldo da Carteira -->
                    <div class="bg-zinc-800 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <span class="text-gray-300">Saldo da Carteira:</span>
                            <span class="text-green-400 font-bold">R$ {{ number_format($walletBalance, 2, ',', '.') }}</span>
                        </div>
                    </div>

                    <!-- Seleção de Charms -->
                    <div>
                        <p class="text-gray-300 mb-4">Escolha um charm para enviar:</p>
                        <div class="grid grid-cols-2 gap-3 mb-4">
                            @foreach($charms as $type => $charm)
                                <button
                                    wire:click="selectCharm('{{ $type }}')"
                                    class="p-4 border rounded-lg text-center hover:bg-gray-700 transition-colors
                                        {{ $selectedCharm == $type ? 'bg-purple-900 border-purple-500' : 'border-zinc-600' }}"
                                >
                                    <div class="flex flex-col items-center">
                                        <x-flux::icon name="{{ $charm['icon'] }}" class="w-8 h-8 mb-2 {{ $selectedCharm == $type ? 'text-purple-400' : 'text-gray-400' }}" />
                                        <div class="font-bold text-gray-300">{{ $charm['name'] }}</div>
                                        <div class="text-sm text-gray-400">R$ {{ number_format($charm['price'], 2, ',', '.') }}</div>
                                        <div class="text-xs text-gray-500 mt-1">{{ $charm['description'] }}</div>
                                    </div>
                                </button>
                            @endforeach
                        </div>
                    </div>

                    <!-- Mensagem Opcional -->
                    <div>
                        <flux:field>
                            <flux:label>Mensagem (opcional)</flux:label>
                            <flux:textarea wire:model="donationMessage" placeholder="Deixe uma mensagem especial..." rows="3" />
                        </flux:field>
                    </div>

                    @error('donation')
                        <div class="text-red-400 text-sm">{{ $message }}</div>
                    @enderror
                </div>
            </flux:modal.body>

            <flux:modal.footer>
                <flux:button wire:click="closeDonationModal" variant="ghost">Cancelar</flux:button>
                <flux:button 
                    wire:click="sendDonation" 
                    variant="primary" 
                    :disabled="!$selectedCharm || $processing"
                >
                    @if($processing)
                        <x-flux::icon name="arrow-path" class="w-4 h-4 mr-2 animate-spin" />
                        Processando...
                    @else
                        <x-flux::icon name="heart" class="w-4 h-4 mr-2" />
                        Enviar Charm
                        @if($selectedCharm)
                            (R$ {{ number_format($charms[$selectedCharm]['price'], 2, ',', '.') }})
                        @endif
                    @endif
                </flux:button>
            </flux:modal.footer>
        </flux:modal>
    @endif
</div>

<script>
// Auto-scroll do chat - função global
window.scrollChatToBottom = function() {
    const chatMessages = document.getElementById('chat-messages');
    if (chatMessages) {
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
}

// Inicializar quando o DOM estiver pronto
document.addEventListener('DOMContentLoaded', function() {
    // Observer para auto-scroll quando novas mensagens chegarem
    const viewerChatObserver = new MutationObserver(window.scrollChatToBottom);
    const chatContainer = document.getElementById('chat-messages');
    if (chatContainer) {
        viewerChatObserver.observe(chatContainer, { childList: true, subtree: true });
    }
});

// Variáveis globais para viewer
let viewerStreamingService = null;
let connectionAttempts = 0;
const maxConnectionAttempts = 3;

// Função para conectar como viewer
async function connectAsViewer() {
    console.log('=== Tentando conectar como viewer à live:', @js($liveStream->id));

    const statusElement = document.getElementById('connection-status');
    const videoElement = document.getElementById('remoteVideo');

    if (statusElement) {
        statusElement.textContent = '🔄 Conectando...';
        statusElement.className = 'absolute bottom-4 left-4 bg-blue-600 text-white px-3 py-1 rounded text-xs';
    }

    try {
        connectionAttempts++;

        // Verificar se StreamingService está disponível globalmente
        let StreamingService;
        if (typeof window.StreamingService !== 'undefined') {
            StreamingService = window.StreamingService;
            console.log('✅ StreamingService encontrado globalmente');
        } else {
            console.log('⚠️ StreamingService não encontrado globalmente, usando fallback');
            throw new Error('WebRTC não disponível - usando modo de compatibilidade');
        }

        // Criar instância do streaming service
        viewerStreamingService = new StreamingService();

        // Conectar como viewer
        const streamId = 'live-' + @js($liveStream->id);
        console.log('🔗 Tentando conectar ao stream:', streamId);

        const remoteStream = await viewerStreamingService.connectAsViewer(streamId);

        // Conectar stream ao elemento de vídeo
        if (videoElement && remoteStream) {
            videoElement.srcObject = remoteStream;
            console.log('✅ Stream do broadcaster conectado com sucesso!');

            if (statusElement) {
                statusElement.textContent = '✅ Conectado via WebRTC';
                statusElement.className = 'absolute bottom-4 left-4 bg-green-600 text-white px-3 py-1 rounded text-xs';
            }

            return true;
        }

    } catch (error) {
        console.error('❌ Erro ao conectar via WebRTC:', error);

        // Fallback: modo de compatibilidade
        if (connectionAttempts < maxConnectionAttempts) {
            console.log('🔄 Tentando novamente em 3 segundos...');
            if (statusElement) {
                statusElement.textContent = `🔄 Tentativa ${connectionAttempts}/${maxConnectionAttempts}...`;
                statusElement.className = 'absolute bottom-4 left-4 bg-blue-600 text-white px-3 py-1 rounded text-xs';
            }
            setTimeout(connectAsViewer, 3000);
            return;
        }

        // Modo de compatibilidade - simular stream
        console.log('📺 Ativando modo de compatibilidade');

        if (statusElement) {
            statusElement.textContent = '📺 Live ativa (modo compatibilidade)';
            statusElement.className = 'absolute bottom-4 left-4 bg-green-600 text-white px-3 py-1 rounded text-xs';
        }

        // Mostrar simulação de live
        if (videoElement) {
            simulateLiveStream(videoElement);
        }
    }
}

// Função para simular live stream (fallback)
function simulateLiveStream(videoElement) {
    console.log('🎬 Iniciando simulação de live stream...');

    // Criar um canvas com animação de live
    const canvas = document.createElement('canvas');
    canvas.width = 640;
    canvas.height = 480;
    const ctx = canvas.getContext('2d');

    // Variáveis para animação
    let frame = 0;
    let time = 0;
    const startTime = Date.now();

    function animate() {
        time = (Date.now() - startTime) / 1000;

        // Background gradient animado
        const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
        gradient.addColorStop(0, '#1f2937');
        gradient.addColorStop(0.5, '#374151');
        gradient.addColorStop(1, '#1f2937');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // Efeito de "ondas" no background
        for (let i = 0; i < 5; i++) {
            const alpha = 0.1 + Math.sin(time + i) * 0.05;
            ctx.fillStyle = `rgba(124, 58, 237, ${alpha})`;
            ctx.beginPath();
            ctx.arc(
                canvas.width/2 + Math.sin(time + i) * 100,
                canvas.height/2 + Math.cos(time + i * 0.7) * 50,
                30 + Math.sin(time * 2 + i) * 10,
                0, Math.PI * 2
            );
            ctx.fill();
        }

        // Título principal
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 28px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('🔴 LIVE', canvas.width/2, canvas.height/2 - 60);

        // Subtítulo
        ctx.fillStyle = '#e5e7eb';
        ctx.font = '18px Arial';
        ctx.fillText('{{ $liveStream->title }}', canvas.width/2, canvas.height/2 - 20);

        // Status dinâmico
        ctx.fillStyle = '#9ca3af';
        ctx.font = '14px Arial';
        const statusMessages = [
            'Transmissão em andamento',
            '{{ $liveStream->user->name }} está ao vivo',
            'Live ativa - WebRTC indisponível',
            'Modo de compatibilidade ativo'
        ];
        const statusIndex = Math.floor(time / 3) % statusMessages.length;
        ctx.fillText(statusMessages[statusIndex], canvas.width/2, canvas.height/2 + 10);

        // Indicador de "ao vivo" pulsante
        const pulse = Math.sin(time * 3) * 0.5 + 0.5;
        ctx.fillStyle = `rgba(239, 68, 68, ${0.7 + pulse * 0.3})`;
        ctx.beginPath();
        ctx.arc(canvas.width/2 - 80, canvas.height/2 - 60, 8, 0, Math.PI * 2);
        ctx.fill();

        // Contador de tempo
        const minutes = Math.floor(time / 60);
        const seconds = Math.floor(time % 60);
        const timeStr = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        ctx.fillStyle = '#ffffff';
        ctx.font = '16px monospace';
        ctx.fillText(timeStr, canvas.width/2, canvas.height/2 + 40);

        // Botão de reconexão
        const buttonY = canvas.height/2 + 80;
        const buttonWidth = 160;
        const buttonHeight = 35;
        const buttonX = canvas.width/2 - buttonWidth/2;

        // Background do botão
        ctx.fillStyle = '#7c3aed';
        ctx.fillRect(buttonX, buttonY, buttonWidth, buttonHeight);

        // Texto do botão
        ctx.fillStyle = '#ffffff';
        ctx.font = '14px Arial';
        ctx.fillText('🔄 Tentar WebRTC', canvas.width/2, buttonY + 22);

        // Informações da live
        ctx.fillStyle = '#9ca3af';
        ctx.font = '12px Arial';

        // Viewers count
        const baseViewers = {{ $liveStream->viewers ?? 1 }};
        const viewersCount = baseViewers + Math.floor(Math.sin(time) * 2);
        ctx.fillText(`👥 ${Math.max(1, viewersCount)} assistindo`, canvas.width/2, canvas.height - 50);

        // Streamer info
        ctx.fillText(`🎙️ {{ $liveStream->user->name }}`, canvas.width/2, canvas.height - 30);

        // Tech info
        ctx.fillStyle = '#6b7280';
        ctx.font = '10px Arial';
        ctx.fillText('WebRTC indisponível - Usando simulação visual', canvas.width/2, canvas.height - 10);

        frame++;
        requestAnimationFrame(animate);
    }

    animate();

    // Converter canvas para stream
    const stream = canvas.captureStream(30);
    videoElement.srcObject = stream;

    // Adicionar click handler para reconexão
    videoElement.onclick = () => {
        console.log('🔄 Tentando reconectar via WebRTC...');
        connectAsViewer();
    };

    console.log('✅ Simulação de live stream ativa');
}

// Inicializar quando o DOM estiver pronto
document.addEventListener('livewire:init', async () => {
    console.log('=== Viewer inicializado para live:', @js($liveStream->id));

    // Escutar eventos do broadcaster
    Livewire.on('broadcaster-camera-preparing', (streamId) => {
        if (streamId == @js($liveStream->id)) {
            console.log('📹 Broadcaster preparando câmera...');
            const statusElement = document.getElementById('connection-status');
            if (statusElement) {
                statusElement.textContent = '📹 Broadcaster preparando câmera...';
                statusElement.className = 'absolute bottom-4 left-4 bg-yellow-600 text-white px-3 py-1 rounded text-xs';
            }
        }
    });

    Livewire.on('broadcaster-camera-active', (streamId) => {
        if (streamId == @js($liveStream->id)) {
            console.log('✅ Câmera do broadcaster ativa!');
            const statusElement = document.getElementById('connection-status');
            if (statusElement) {
                statusElement.textContent = '✅ Câmera ativa - Tentando conectar...';
                statusElement.className = 'absolute bottom-4 left-4 bg-green-600 text-white px-3 py-1 rounded text-xs';
            }

            // Tentar conectar novamente agora que a câmera está ativa
            setTimeout(() => {
                connectAsViewer();
            }, 1000);
        }
    });

    // Escutar mensagens do chat para mostrar na simulação
    Livewire.on('live-stream-message', (data) => {
        if (data.stream_id == @js($liveStream->id)) {
            console.log('💬 Nova mensagem no chat:', data.message.message);
            // A simulação pode mostrar as mensagens mais recentes
            window.lastChatMessage = data.message.message;
            window.lastChatUser = data.user.name;
        }
    });

    // Aguardar um pouco para garantir que os assets foram carregados
    setTimeout(() => {
        connectAsViewer();
    }, 2000);
});
</script>

