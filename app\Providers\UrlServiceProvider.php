<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\URL;

class UrlServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Forçar URL root em produção
        if ($this->app->environment('production')) {
            URL::forceRootUrl('https://www.swingcuritiba.com.br');
            URL::forceScheme('https');
        }
    }
}
