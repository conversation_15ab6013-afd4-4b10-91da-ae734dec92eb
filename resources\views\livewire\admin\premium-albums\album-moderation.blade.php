<div class="max-w-7xl mx-auto p-6">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-300 mb-2">Moderação de Álbuns Premium</h1>
        <p class="text-gray-400">Gerencie e modere álbuns premium da plataforma</p>
        
        <!-- Stats Cards -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
            <div class="bg-yellow-900 border border-yellow-600 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-yellow-400">{{ $stats['pending'] }}</div>
                <div class="text-sm text-yellow-200">Pendentes</div>
            </div>
            <div class="bg-green-900 border border-green-600 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-green-400">{{ $stats['approved'] }}</div>
                <div class="text-sm text-green-200">Aprovados</div>
            </div>
            <div class="bg-red-900 border border-red-600 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-red-400">{{ $stats['suspended'] }}</div>
                <div class="text-sm text-red-200">Suspensos</div>
            </div>
            <div class="bg-zinc-800 border border-zinc-600 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-gray-300">{{ $stats['total'] }}</div>
                <div class="text-sm text-gray-400">Total</div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg class="mb-6"">
        <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.body">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- Search -->
                <div>
                    <flux:label for="search">Buscar</flux:label>
                    <flux:input 
                        wire:model.live="search" 
                        id="search" 
                        placeholder="Nome, criador..."
                        class="mt-1"
                    />
                </div>

                <!-- Status Filter -->
                <div>
                    <flux:label for="status">Status</flux:label>
                    <flux:select wire:model.live="status" id="status" class="mt-1">
                        <option value="pending">Pendentes</option>
                        <option value="approved">Aprovados</option>
                        <option value="suspended">Suspensos</option>
                        <option value="all">Todos</option>
                    </flux:select>
                </div>

                <!-- Sort -->
                <div>
                    <flux:label for="sort">Ordenar por</flux:label>
                    <flux:select wire:model.live="sortBy" id="sort" class="mt-1">
                        <option value="created_at">Data de criação</option>
                        <option value="name">Nome</option>
                        <option value="price">Preço</option>
                        <option value="sales_count">Vendas</option>
                    </flux:select>
                </div>

                <!-- Direction -->
                <div>
                    <flux:label for="direction">Direção</flux:label>
                    <flux:select wire:model.live="sortDirection" id="direction" class="mt-1">
                        <option value="desc">Decrescente</option>
                        <option value="asc">Crescente</option>
                    </flux:select>
                </div>
            </div>
        </div>
    </div>

    <!-- Albums List -->
    @if($albums->count() > 0)
    <div class="space-y-4 mb-8">
        @foreach($albums as $album)
        <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg">
            <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.body">
                <div class="flex flex-col lg:flex-row gap-6">
                    <!-- Album Cover -->
                    <div class="lg:w-32 lg:h-32 w-full h-48 bg-zinc-800 rounded-lg overflow-hidden flex-shrink-0">
                        @if($album->medias->count() > 0)
                            @php $firstMedia = $album->medias->first(); @endphp
                            @if($firstMedia->type === 'photo')
                                <img 
                                    src="{{ route('premium-albums.media', [$album, $firstMedia->id]) }}" 
                                    alt="{{ $album->name }}"
                                    class="w-full h-full object-cover"
                                />
                            @else
                                <div class="w-full h-full flex items-center justify-center">
                                    <flux:icon name="film" class="w-8 h-8 text-gray-400" />
                                </div>
                            @endif
                        @else
                            <div class="w-full h-full flex items-center justify-center">
                                <flux:icon name="photo" class="w-8 h-8 text-gray-400" />
                            </div>
                        @endif
                    </div>

                    <!-- Album Info -->
                    <div class="flex-1">
                        <div class="flex justify-between items-start mb-4">
                            <div>
                                <h3 class="text-xl font-semibold text-gray-300 mb-2">{{ $album->name }}</h3>
                                
                                <!-- Creator -->
                                <div class="flex items-center mb-2">
                                    <img 
                                        src="{{ $album->user->avatar_url ?? '/default-avatar.png' }}" 
                                        alt="{{ $album->user->name }}"
                                        class="w-6 h-6 rounded-full mr-2"
                                    />
                                    <span class="text-gray-400">{{ $album->user->name }} (@{{ $album->user->username }})</span>
                                </div>

                                <!-- Description -->
                                @if($album->description)
                                <p class="text-gray-400 text-sm mb-3 line-clamp-2">{{ $album->description }}</p>
                                @endif
                            </div>

                            <!-- Status Badge -->
                            <span class="px-3 py-1 text-sm font-medium rounded-full {{ $this->getStatusBadgeClass($album) }}">
                                {{ $this->getStatusText($album) }}
                            </span>
                        </div>

                        <!-- Stats -->
                        <div class="grid grid-cols-2 md:grid-cols-5 gap-4 mb-4">
                            <div>
                                <span class="text-gray-400 text-sm">Preço</span>
                                <div class="text-purple-400 font-medium">R$ {{ number_format($album->price, 2, ',', '.') }}</div>
                            </div>
                            <div>
                                <span class="text-gray-400 text-sm">Arquivos</span>
                                <div class="text-gray-300">{{ $album->medias->count() }}</div>
                            </div>
                            <div>
                                <span class="text-gray-400 text-sm">Vendas</span>
                                <div class="text-gray-300">{{ $album->purchases_count }}</div>
                            </div>
                            <div>
                                <span class="text-gray-400 text-sm">Avaliações</span>
                                <div class="text-gray-300">{{ $album->reviews_count }}</div>
                            </div>
                            <div>
                                <span class="text-gray-400 text-sm">Criado em</span>
                                <div class="text-gray-300">{{ $album->created_at->format('d/m/Y') }}</div>
                            </div>
                        </div>

                        <!-- Tags -->
                        @if($album->tags)
                        <div class="mb-4">
                            <div class="flex flex-wrap gap-1">
                                @foreach(explode(',', $album->tags) as $tag)
                                <span class="bg-zinc-700 text-gray-300 px-2 py-1 text-xs rounded">
                                    #{{ trim($tag) }}
                                </span>
                                @endforeach
                            </div>
                        </div>
                        @endif

                        <!-- Actions -->
                        <div class="flex space-x-2">
                            <flux:button 
                                href="{{ route('premium-albums.show', $album) }}"
                                size="sm"
                                variant="outline"
                                target="_blank"
                            >
                                <flux:icon name="eye" class="w-4 h-4 mr-1" />
                                Visualizar
                            </flux:button>

                            @if(!$album->admin_approved && $album->status !== 'suspended')
                            <flux:button 
                                wire:click="confirmApproval({{ $album->id }})"
                                size="sm"
                                color="green"
                            >
                                <flux:icon name="check" class="w-4 h-4 mr-1" />
                                Aprovar
                            </flux:button>

                            <flux:button 
                                wire:click="confirmRejection({{ $album->id }})"
                                size="sm"
                                color="red"
                                variant="outline"
                            >
                                <flux:icon name="x-mark" class="w-4 h-4 mr-1" />
                                Rejeitar
                            </flux:button>
                            @endif

                            @if($album->status === 'suspended')
                            <flux:button 
                                wire:click="confirmApproval({{ $album->id }})"
                                size="sm"
                                color="blue"
                            >
                                <flux:icon name="arrow-path" class="w-4 h-4 mr-1" />
                                Reativar
                            </flux:button>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endforeach
    </div>

    <!-- Pagination -->
    {{ $albums->links() }}
    @else
    <!-- Empty State -->
    <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg class="text-center py-12"">
        <flux:icon name="document-magnifying-glass" class="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-300 mb-2">Nenhum álbum encontrado</h3>
        <p class="text-gray-400">
            @if($search)
                Tente ajustar os filtros de busca.
            @else
                Não há álbuns para moderar no momento.
            @endif
        </p>
    </div>
    @endif

    <!-- Approval Modal -->
    @if($showApprovalModal && $selectedAlbum)
    <flux:modal wire:model="showApprovalModal">
        <flux:modal.header>
            <h2 class="text-lg font-semibold text-gray-300">Aprovar Álbum</h2>
        </flux:modal.header>
        
        <flux:modal.body>
            <p class="text-gray-400 mb-4">
                Tem certeza que deseja aprovar o álbum <strong class="text-gray-300">"{{ $selectedAlbum->name }}"</strong> 
                de <strong class="text-gray-300">{{ $selectedAlbum->user->name }}</strong>?
            </p>
            <p class="text-green-400 text-sm">
                O álbum será publicado e ficará disponível para compra na plataforma.
            </p>
        </flux:modal.body>
        
        <flux:modal.footer>
            <flux:button wire:click="closeModals" variant="outline">
                Cancelar
            </flux:button>
            <flux:button wire:click="approveAlbum" color="green">
                Aprovar Álbum
            </flux:button>
        </flux:modal.footer>
    </flux:modal>
    @endif

    <!-- Rejection Modal -->
    @if($showRejectionModal && $selectedAlbum)
    <flux:modal wire:model="showRejectionModal">
        <flux:modal.header>
            <h2 class="text-lg font-semibold text-gray-300">Rejeitar Álbum</h2>
        </flux:modal.header>
        
        <flux:modal.body>
            <p class="text-gray-400 mb-4">
                Rejeitar o álbum <strong class="text-gray-300">"{{ $selectedAlbum->name }}"</strong> 
                de <strong class="text-gray-300">{{ $selectedAlbum->user->name }}</strong>.
            </p>
            
            <div>
                <flux:label for="rejectionReason">Motivo da Rejeição *</flux:label>
                <flux:textarea 
                    wire:model="rejectionReason" 
                    id="rejectionReason" 
                    placeholder="Explique o motivo da rejeição..."
                    rows="4"
                    class="mt-1"
                />
                @error('rejectionReason') <span class="text-red-400 text-sm">{{ $message }}</span> @enderror
            </div>
        </flux:modal.body>
        
        <flux:modal.footer>
            <flux:button wire:click="closeModals" variant="outline">
                Cancelar
            </flux:button>
            <flux:button wire:click="rejectAlbum" color="red">
                Rejeitar Álbum
            </flux:button>
        </flux:modal.footer>
    </flux:modal>
    @endif
</div>
