<div class="max-w-4xl mx-auto p-6">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-300 mb-2"><PERSON><PERSON>r <PERSON>m Premium</h1>
        <p class="text-gray-400">Crie seu álbum exclusivo e monetize seu conteúdo</p>
    </div>

    <!-- Progress Steps -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <!-- Step 1 -->
                <div class="flex items-center">
                    <div class="w-8 h-8 rounded-full flex items-center justify-center {{ $step >= 1 ? 'bg-purple-600 text-white' : 'bg-gray-600 text-gray-400' }}">
                        1
                    </div>
                    <span class="ml-2 text-sm {{ $step >= 1 ? 'text-purple-400' : 'text-gray-400' }}">Informações</span>
                </div>
                
                <!-- Connector -->
                <div class="w-16 h-1 {{ $step >= 2 ? 'bg-purple-600' : 'bg-gray-600' }}"></div>
                
                <!-- Step 2 -->
                <div class="flex items-center">
                    <div class="w-8 h-8 rounded-full flex items-center justify-center {{ $step >= 2 ? 'bg-purple-600 text-white' : 'bg-gray-600 text-gray-400' }}">
                        2
                    </div>
                    <span class="ml-2 text-sm {{ $step >= 2 ? 'text-purple-400' : 'text-gray-400' }}">Upload</span>
                </div>
                
                <!-- Connector -->
                <div class="w-16 h-1 {{ $step >= 3 ? 'bg-purple-600' : 'bg-gray-600' }}"></div>
                
                <!-- Step 3 -->
                <div class="flex items-center">
                    <div class="w-8 h-8 rounded-full flex items-center justify-center {{ $step >= 3 ? 'bg-purple-600 text-white' : 'bg-gray-600 text-gray-400' }}">
                        3
                    </div>
                    <span class="ml-2 text-sm {{ $step >= 3 ? 'text-purple-400' : 'text-gray-400' }}">Revisão</span>
                </div>
            </div>
        </div>
    </div>

    <form wire:submit.prevent="createAlbum">
        <!-- Step 1: Basic Information -->
        @if($step === 1)
        <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg class="mb-6"">
            <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.header">
                <h2 class="text-xl font-semibold text-gray-300">Informações Básicas</h2>
            </div>
            
            <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.body class="space-y-6"">
                <!-- Nome -->
                <div>
                    <flux:label for="name">Nome do Álbum *</flux:label>
                    <flux:input 
                        wire:model="name" 
                        id="name" 
                        placeholder="Digite o nome do seu álbum"
                        class="mt-1"
                    />
                    @error('name') <span class="text-red-400 text-sm">{{ $message }}</span> @enderror
                </div>

                <!-- Descrição -->
                <div>
                    <flux:label for="description">Descrição</flux:label>
                    <flux:textarea 
                        wire:model="description" 
                        id="description" 
                        placeholder="Descreva seu álbum..."
                        rows="4"
                        class="mt-1"
                    />
                    @error('description') <span class="text-red-400 text-sm">{{ $message }}</span> @enderror
                </div>

                <!-- Preço e Preview -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <flux:label for="price">Preço (R$) *</flux:label>
                        <flux:input 
                            wire:model="price" 
                            id="price" 
                            type="number" 
                            step="0.01"
                            min="{{ $settings['min_price'] }}"
                            max="{{ $settings['max_price'] }}"
                            class="mt-1"
                        />
                        <p class="text-xs text-gray-400 mt-1">
                            Entre R$ {{ number_format($settings['min_price'], 2, ',', '.') }} e R$ {{ number_format($settings['max_price'], 2, ',', '.') }}
                        </p>
                        @error('price') <span class="text-red-400 text-sm">{{ $message }}</span> @enderror
                    </div>

                    <div>
                        <flux:label for="preview_limit">Arquivos de Preview</flux:label>
                        <flux:input 
                            wire:model="preview_limit" 
                            id="preview_limit" 
                            type="number" 
                            min="1" 
                            max="10"
                            class="mt-1"
                        />
                        <p class="text-xs text-gray-400 mt-1">
                            Quantos arquivos mostrar como preview gratuito
                        </p>
                        @error('preview_limit') <span class="text-red-400 text-sm">{{ $message }}</span> @enderror
                    </div>
                </div>

                <!-- Tags -->
                <div>
                    <flux:label for="tags">Tags</flux:label>
                    <flux:input 
                        wire:model="tags" 
                        id="tags" 
                        placeholder="sensual, exclusivo, premium (separadas por vírgula)"
                        class="mt-1"
                    />
                    <p class="text-xs text-gray-400 mt-1">
                        Ajude os usuários a encontrar seu álbum
                    </p>
                    @error('tags') <span class="text-red-400 text-sm">{{ $message }}</span> @enderror
                </div>
            </div>
        </div>
        @endif

        <!-- Step 2: File Upload -->
        @if($step === 2)
        <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg class="mb-6"">
            <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.header">
                <h2 class="text-xl font-semibold text-gray-300">Upload de Arquivos</h2>
                <p class="text-sm text-gray-400">
                    Máximo {{ $settings['max_files'] }} arquivos, {{ $settings['max_file_size'] }}MB cada
                </p>
            </div>
            
            <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.body">
                <!-- Upload Area -->
                <div class="border-2 border-dashed border-gray-600 rounded-lg p-8 text-center mb-6">
                    <flux:icon name="cloud-arrow-up" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p class="text-gray-300 mb-2">Arraste arquivos aqui ou clique para selecionar</p>
                    <p class="text-sm text-gray-400 mb-4">
                        Tipos permitidos: {{ implode(', ', $settings['allowed_types']) }}
                    </p>
                    <input 
                        type="file" 
                        wire:model="files" 
                        multiple 
                        accept=".jpg,.jpeg,.png,.gif,.webp,.mp4,.mov,.avi,.webm"
                        class="hidden" 
                        id="file-upload"
                    />
                    <flux:button 
                        type="button" 
                        onclick="document.getElementById('file-upload').click()"
                        variant="outline"
                    >
                        Selecionar Arquivos
                    </flux:button>
                </div>

                @error('files') <span class="text-red-400 text-sm">{{ $message }}</span> @enderror
                @error('files.*') <span class="text-red-400 text-sm">{{ $message }}</span> @enderror

                <!-- File Preview -->
                @if(!empty($files))
                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    @foreach($files as $index => $file)
                        @php $preview = $this->getFilePreview($file); @endphp
                        <div class="relative bg-zinc-800 rounded-lg p-3">
                            @if($preview['type'] === 'image')
                                <img src="{{ $preview['url'] }}" alt="Preview" class="w-full h-24 object-cover rounded mb-2">
                            @else
                                <div class="w-full h-24 bg-zinc-700 rounded mb-2 flex items-center justify-center">
                                    <flux:icon name="film" class="w-8 h-8 text-gray-400" />
                                </div>
                            @endif
                            
                            <p class="text-xs text-gray-300 truncate">{{ $preview['name'] }}</p>
                            <p class="text-xs text-gray-400">{{ $preview['size'] }}</p>
                            
                            <button 
                                type="button"
                                wire:click="removeFile({{ $index }})"
                                class="absolute -top-2 -right-2 bg-red-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-700"
                            >
                                ×
                            </button>
                        </div>
                    @endforeach
                </div>
                @endif
            </div>
        </div>
        @endif

        <!-- Step 3: Review -->
        @if($step === 3)
        <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg class="mb-6"">
            <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.header">
                <h2 class="text-xl font-semibold text-gray-300">Revisão Final</h2>
            </div>
            
            <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.body class="space-y-4"">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="font-semibold text-gray-300 mb-2">Informações do Álbum</h3>
                        <div class="space-y-2 text-sm">
                            <p><span class="text-gray-400">Nome:</span> {{ $name }}</p>
                            <p><span class="text-gray-400">Preço:</span> R$ {{ number_format($price, 2, ',', '.') }}</p>
                            <p><span class="text-gray-400">Preview:</span> {{ $preview_limit }} arquivos</p>
                            @if($tags)
                            <p><span class="text-gray-400">Tags:</span> {{ $tags }}</p>
                            @endif
                        </div>
                    </div>
                    
                    <div>
                        <h3 class="font-semibold text-gray-300 mb-2">Arquivos</h3>
                        <p class="text-sm text-gray-400">{{ count($files) }} arquivo(s) selecionado(s)</p>
                    </div>
                </div>

                @if($description)
                <div>
                    <h3 class="font-semibold text-gray-300 mb-2">Descrição</h3>
                    <p class="text-sm text-gray-400">{{ $description }}</p>
                </div>
                @endif
            </div>
        </div>
        @endif

        <!-- Navigation Buttons -->
        <div class="flex justify-between">
            @if($step > 1)
            <flux:button 
                type="button" 
                wire:click="previousStep"
                variant="outline"
            >
                Voltar
            </flux:button>
            @else
            <div></div>
            @endif

            @if($step < 3)
            <flux:button 
                type="button" 
                wire:click="nextStep"
                color="purple"
            >
                Próximo
            </flux:button>
            @else
            <flux:button 
                type="submit" 
                color="purple"
                :disabled="$isLoading"
            >
                @if($isLoading)
                    <flux:icon name="arrow-path" class="w-4 h-4 animate-spin mr-2" />
                    Criando...
                @else
                    Criar Álbum
                @endif
            </flux:button>
            @endif
        </div>
    </form>
</div>
