<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;

class UserController extends Controller
{
    /**
     * Search for users by username or name.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function search(Request $request)
    {
        $query = $request->input('query');
        $limit = $request->input('limit', 5);

        if (empty($query)) {
            return response()->json([]);
        }

        $users = User::where(function ($q) use ($query) {
            $q->where('username', 'LIKE', "%{$query}%")
                ->orWhere('name', 'LIKE', "%{$query}%");
        })
            ->select(['id', 'name', 'username'])
            ->with(['currentPhoto', 'photos'])
            ->take((int)$limit)
            ->get()
            ->map(function ($user) {
                $currentPhoto = $user->currentPhoto ?? $user->photos->first();
                $avatarPath = $currentPhoto ? $currentPhoto->photo_path : null;
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'username' => $user->username,
                    'avatar' => $avatarPath ? \App\Helpers\StorageHelper::getAvatarUrl($avatarPath) : asset('images/default-avatar.jpg'),
                    'avatarUrl' => $avatarPath ? \App\Helpers\StorageHelper::getAvatarUrl($avatarPath) : asset('images/default-avatar.jpg'),
                ];
            });

        return response()->json($users);
    }
}
