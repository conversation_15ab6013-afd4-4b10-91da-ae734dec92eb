<div>
    @if($isVisible)
        <div
            wire:poll.60s="updateUsedTime"
            data-visitor-timer
            class="fixed bottom-0 left-0 right-0 z-[9999] bg-gradient-to-r from-red-500 to-orange-500 text-white shadow-lg border-t border-red-600"
            style="position: fixed !important; bottom: 0 !important; z-index: 9999 !important;"
            x-data="{
                timeRemaining: {{ $timeRemaining }},
                accessStartTime: {{ $accessStartTime }},
                totalSeconds: {{ $totalMinutes * 60 }},
                formatTime(seconds) {
                    const minutes = Math.floor(seconds / 60);
                    const secs = seconds % 60;
                    return String(minutes).padStart(2, '0') + ':' + String(secs).padStart(2, '0');
                },
                updateTimer() {
                    const now = Math.floor(Date.now() / 1000);
                    const elapsed = now - this.accessStartTime;
                    this.timeRemaining = Math.max(0, this.totalSeconds - elapsed);

                    if (this.timeRemaining <= 0) {
                        // Verificar se $wire está disponível antes de chamar
                        if (typeof $wire !== 'undefined' && $wire.redirectToHome) {
                            $wire.redirectToHome();
                        } else {
                            // Fallback: aguardar Livewire estar disponível
                            document.addEventListener('livewire:initialized', () => {
                                if (typeof $wire !== 'undefined' && $wire.redirectToHome) {
                                    $wire.redirectToHome();
                                }
                            }, { once: true });
                        }
                    }
                }
            }"
            x-init="
                // Atualizar timer a cada segundo
                setInterval(() => {
                    updateTimer();
                }, 1000);

                // Atualizar imediatamente
                updateTimer();
            "
        >
            <!-- Barra de progresso no topo -->
            <div class="w-full bg-red-700 h-1">
                <div
                    class="bg-white h-1 transition-all duration-1000"
                    :style="'width: ' + ((timeRemaining / totalSeconds) * 100) + '%'"
                ></div>
            </div>

            <!-- Container responsivo -->
            <div class="container mx-auto px-4 py-3">
                <div class="flex flex-col sm:flex-row items-center justify-between space-y-2 sm:space-y-0">
                    <!-- Timer info -->
                    <div class="flex items-center space-x-3">
                        <div class="flex items-center space-x-2">
                            <x-flux::icon name="clock" class="w-5 h-5 flex-shrink-0 animate-pulse" />
                            <span class="font-bold text-base">
                                <span class="hidden sm:inline">Tempo restante:</span>
                                <span class="sm:hidden">Tempo:</span>
                            </span>
                        </div>
                        <div class="bg-red-700 px-3 py-1 rounded-full">
                            <span x-text="formatTime(timeRemaining)" class="font-mono text-lg font-bold"></span>
                        </div>
                    </div>

                    <!-- Info e botão -->
                    <div class="flex items-center space-x-3">
                        <span class="text-xs opacity-90 hidden sm:block">
                            Conta Visitante - Limite diário
                        </span>
                        <a href="{{ route('renovar-vip') }}"
                           class="bg-yellow-400 text-red-600 px-4 py-2 rounded-full text-sm font-bold hover:bg-yellow-300 transition-colors flex-shrink-0 shadow-lg"
                           wire:navigate>
                            <span class="hidden sm:inline">🚀 Upgrade VIP</span>
                            <span class="sm:hidden">🚀 VIP</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Spacer para compensar o timer fixo no bottom -->
        <div class="h-24 sm:h-28" style="height: 90px !important;"></div>

        <!-- Adicionar padding-bottom ao body via JavaScript -->
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                if (document.querySelector('[data-visitor-timer]')) {
                    document.body.style.paddingBottom = '90px';
                }
            });
        </script>
    @endif
</div>
