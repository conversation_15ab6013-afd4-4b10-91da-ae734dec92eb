<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class HandleLivewireUploads
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if this is a Livewire upload request
        if ($request->is('livewire/upload-file*')) {
            // Validate that files are present and valid
            if (!$request->hasFile('files')) {
                return response()->json([
                    'error' => 'No files provided'
                ], 400);
            }

            $files = $request->file('files');
            
            // Ensure files is an array
            if (!is_array($files)) {
                $files = [$files];
            }

            // Validate each file
            foreach ($files as $file) {
                if (!$file || !$file->isValid()) {
                    return response()->json([
                        'error' => 'Invalid file detected'
                    ], 400);
                }

                // Check file size (200MB limit)
                $maxSize = 200 * 1024 * 1024; // 200MB in bytes
                if ($file->getSize() > $maxSize) {
                    return response()->json([
                        'error' => 'File too large. Maximum size: 200MB'
                    ], 413);
                }
            }
        }

        return $next($request);
    }
}
