<?php

namespace App\Console\Commands;

use App\Models\Group;
use App\Models\User;
use App\Models\UserPoint;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class TestGroupJoin extends Command
{
    protected $signature = 'test:group-join {user_id} {group_id}';
    protected $description = 'Testa a funcionalidade de entrar em um grupo';

    public function handle()
    {
        $userId = $this->argument('user_id');
        $groupId = $this->argument('group_id');

        try {
            $this->info("🧪 Testando entrada no grupo...");
            
            // Verificar se o usuário existe
            $user = User::find($userId);
            if (!$user) {
                $this->error("❌ Usuário não encontrado: {$userId}");
                return 1;
            }
            $this->info("✅ Usuário encontrado: {$user->name} (ID: {$user->id})");

            // Verificar se o grupo existe
            $group = Group::find($groupId);
            if (!$group) {
                $this->error("❌ Grupo não encontrado: {$groupId}");
                return 1;
            }
            $this->info("✅ Grupo encontrado: {$group->name} (ID: {$group->id})");

            // Verificar se o usuário já é membro
            if ($user->isMemberOf($group)) {
                $this->warn("⚠️  Usuário já é membro do grupo");
                return 0;
            }
            $this->info("✅ Usuário não é membro do grupo");

            // Testar entrada no grupo
            $this->info("🔄 Tentando entrar no grupo...");
            
            $user->groups()->attach($group->id, [
                'role' => 'member',
                'is_approved' => true,
                'joined_at' => now(),
            ]);
            $this->info("✅ Usuário adicionado ao grupo com sucesso");

            // Incrementar contador de membros
            $group->increment('members_count');
            $this->info("✅ Contador de membros incrementado");

            // Testar adição de pontos
            $this->info("🔄 Testando adição de pontos...");
            try {
                UserPoint::addPoints(
                    $user->id,
                    'group_joined',
                    5,
                    "Entrou no grupo: {$group->name}",
                    $group->id,
                    Group::class
                );
                $this->info("✅ Pontos adicionados com sucesso");
            } catch (\Exception $e) {
                $this->error("❌ Erro ao adicionar pontos: " . $e->getMessage());
                $this->error("Stack trace: " . $e->getTraceAsString());
            }

            // Verificar se o usuário agora é membro
            if ($user->fresh()->isMemberOf($group)) {
                $this->info("✅ Verificação final: Usuário é membro do grupo");
            } else {
                $this->error("❌ Verificação final: Usuário NÃO é membro do grupo");
            }

            $this->info("🎉 Teste concluído com sucesso!");
            return 0;

        } catch (\Exception $e) {
            $this->error("❌ Erro durante o teste: " . $e->getMessage());
            $this->error("Stack trace: " . $e->getTraceAsString());
            
            Log::error('Erro no teste de entrada no grupo', [
                'user_id' => $userId,
                'group_id' => $groupId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return 1;
        }
    }
}
