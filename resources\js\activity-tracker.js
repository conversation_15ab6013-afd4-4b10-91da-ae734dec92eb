/**
 * Sistema de tracking de atividade dos usuários
 * Captura cliques, tempo na página, scroll e outros eventos
 */

class ActivityTracker {
    constructor() {
        this.startTime = Date.now();
        this.maxScrollDepth = 0;
        this.isTracking = true;
        this.debounceTimeout = null;

        this.init();
    }

    init() {
        // Só inicia se o usuário estiver logado
        if (!document.querySelector('meta[name="user-id"]')) {
            return;
        }

        this.setupClickTracking();
        this.setupScrollTracking();
        this.setupPageUnloadTracking();
        this.setupFormTracking();
    }

    /**
     * Configura tracking de cliques
     */
    setupClickTracking() {
        document.addEventListener('click', (event) => {
            if (!this.isTracking) return;

            const element = event.target;

            // Ignora cliques em elementos de tracking
            if (element.closest('[data-no-track]')) {
                return;
            }

            // Ignora cliques em links com wire:navigate para evitar interferência
            const linkElement = element.closest('a[wire\\:navigate], [wire\\:navigate]');
            if (linkElement) {
                console.log('Ignorando tracking para link com wire:navigate:', linkElement.href);
                return;
            }

            // Ignora cliques em elementos Flux UI que podem ter navegação
            if (element.closest('[data-flux-navlist-item], [data-flux-navbar-item], [data-flux-menu-item]')) {
                console.log('Ignorando tracking para elemento Flux UI de navegação');
                return;
            }

            // Usa setTimeout para não bloquear a navegação
            setTimeout(() => {
                const data = {
                    element_type: element.tagName.toLowerCase(),
                    element_id: element.id || null,
                    element_class: element.className || null,
                    element_text: this.getElementText(element),
                    page_title: document.title,
                    url: window.location.href,
                    viewport_size: {
                        width: window.innerWidth,
                        height: window.innerHeight
                    },
                    additional_data: {
                        element_href: element.href || null,
                        element_value: element.value || null,
                        timestamp: Date.now()
                    }
                };

                this.sendTrackingData('/track/click', data);
            }, 0);
        }, { passive: true }); // Adiciona passive: true para melhor performance
    }

    /**
     * Configura tracking de scroll
     */
    setupScrollTracking() {
        let ticking = false;

        const updateScrollDepth = () => {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const documentHeight = document.documentElement.scrollHeight - window.innerHeight;
            const scrollDepth = Math.round((scrollTop / documentHeight) * 100);

            if (scrollDepth > this.maxScrollDepth) {
                this.maxScrollDepth = Math.min(scrollDepth, 100);
            }

            ticking = false;
        };

        window.addEventListener('scroll', () => {
            if (!ticking) {
                requestAnimationFrame(updateScrollDepth);
                ticking = true;
            }
        });
    }

    /**
     * Configura tracking quando o usuário sai da página
     */
    setupPageUnloadTracking() {
        const sendPageMetrics = () => {
            const timeOnPage = Math.round((Date.now() - this.startTime) / 1000);

            const data = {
                time_on_page: timeOnPage,
                scroll_depth: this.maxScrollDepth,
                url: window.location.href,
                page_title: document.title
            };

            // Usa sendBeacon para garantir que os dados sejam enviados
            if (navigator.sendBeacon) {
                const formData = new FormData();
                formData.append('_token', document.querySelector('meta[name="csrf-token"]').content);
                Object.keys(data).forEach(key => {
                    formData.append(key, data[key]);
                });

                navigator.sendBeacon('/track/page-metrics', formData);
            }
        };

        // Envia métricas quando o usuário sai da página
        window.addEventListener('beforeunload', sendPageMetrics);

        // Também envia periodicamente (a cada 30 segundos)
        setInterval(() => {
            if (this.isTracking) {
                const timeOnPage = Math.round((Date.now() - this.startTime) / 1000);

                if (timeOnPage > 5) { // Só envia se ficou mais de 5 segundos
                    const data = {
                        time_on_page: timeOnPage,
                        scroll_depth: this.maxScrollDepth,
                        url: window.location.href,
                        page_title: document.title
                    };

                    this.sendTrackingData('/track/page-metrics', data);
                }
            }
        }, 30000);
    }

    /**
     * Configura tracking de formulários
     */
    setupFormTracking() {
        document.addEventListener('submit', (event) => {
            if (!this.isTracking) return;

            const form = event.target;

            if (form.closest('[data-no-track]')) {
                return;
            }

            const data = {
                event_type: 'form_submit',
                event_data: {
                    form_id: form.id || null,
                    form_class: form.className || null,
                    form_action: form.action || null,
                    form_method: form.method || 'GET',
                    page_title: document.title,
                    url: window.location.href
                }
            };

            this.sendTrackingData('/track/event', data);
        });
    }

    /**
     * Extrai texto do elemento de forma inteligente
     */
    getElementText(element) {
        let text = '';

        if (element.alt) {
            text = element.alt;
        } else if (element.title) {
            text = element.title;
        } else if (element.textContent) {
            text = element.textContent.trim();
        } else if (element.value) {
            text = element.value;
        }

        // Limita o tamanho do texto
        return text.length > 100 ? text.substring(0, 100) + '...' : text;
    }

    /**
     * Envia dados de tracking para o servidor
     */
    sendTrackingData(endpoint, data) {
        // Envia imediatamente para não interferir com navegação
        // Usa requestIdleCallback se disponível para não bloquear a UI
        const sendData = () => {
            fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify(data)
            }).catch(error => {
                console.warn('Erro ao enviar dados de tracking:', error);
            });
        };

        if (window.requestIdleCallback) {
            requestIdleCallback(sendData);
        } else {
            setTimeout(sendData, 0);
        }
    }

    /**
     * Registra evento customizado
     */
    trackEvent(eventType, eventData = {}) {
        if (!this.isTracking) return;

        const data = {
            event_type: eventType,
            event_data: {
                ...eventData,
                page_title: document.title,
                url: window.location.href,
                timestamp: Date.now()
            }
        };

        this.sendTrackingData('/track/event', data);
    }

    /**
     * Para o tracking (útil para páginas administrativas)
     */
    stopTracking() {
        this.isTracking = false;
    }

    /**
     * Reinicia o tracking
     */
    startTracking() {
        this.isTracking = true;
    }
}

// Inicializa o tracker quando o DOM estiver pronto
document.addEventListener('DOMContentLoaded', () => {
    window.activityTracker = new ActivityTracker();
});

// Expõe função global para tracking manual
window.trackEvent = (eventType, eventData) => {
    if (window.activityTracker) {
        window.activityTracker.trackEvent(eventType, eventData);
    }
};
