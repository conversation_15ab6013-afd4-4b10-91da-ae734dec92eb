<div class="p-6 border border-neutral-200 dark:border-neutral-700 shadow-md rounded-lg">
    @if(session()->has('message'))
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4">
            {{ session('message') }}
        </div>
    @endif

    @if(session()->has('error'))
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4">
            {{ session('error') }}
        </div>
    @endif

    @if($errors->has('general'))
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4">
            {{ $errors->first('general') }}
        </div>
    @endif

    <form wire:submit.prevent="store" enctype="multipart/form-data">
        <div x-data="mentionHashtagHandler()" x-id="['create-post']" class="relative">
            <textarea
                class="mention-hashtag-input w-full rounded-lg border-gray-300 dark:border-gray-700 dark:bg-zinc-700 dark:text-gray-300 focus:border-indigo-500 dark:focus:border-indigo-600 focus:ring-indigo-500 dark:focus:ring-indigo-600 p-3"
                rows="3"
                placeholder="O que você está pensando?"
                wire:model="content"
                wire:keyup.debounce.200ms="handleContentInput"
                x-ref="textarea"
                :id="$id('create-post')"
                x-on:input="handleInput($event)"
                x-on:keydown="handleKeydown($event)"
            ></textarea>

            <!-- Sugestões de menções Alpine.js -->
            <div
                x-show="showMentions"
                x-transition
                :style="mentionStyle"
                class="bg-neutral-800 border border-neutral-700 rounded-md shadow-lg max-h-48 overflow-auto z-50"
                :id="$id('mentions-create-post')"
            >
                <template x-for="user in mentionSuggestions" :key="user.id">
                    <div
                        @click="selectMention(user.username)"
                        class="px-4 py-2 hover:bg-neutral-700 cursor-pointer text-gray-300 flex items-center"
                    >
                        <img :src="user.avatar" :alt="user.username" class="w-6 h-6 rounded-full mr-2">
                        <span x-text="user.name + ' (@' + user.username + ')'"></span>>
                    </div>
                </template>
            </div>

            <!-- Sugestões de hashtags Alpine.js -->
            <div
                x-show="showHashtags"
                x-transition
                :style="hashtagStyle"
                class="bg-neutral-800 border border-neutral-700 rounded-md shadow-lg max-h-48 overflow-auto z-50"
                :id="$id('hashtags-create-post')"
            >
                <template x-for="hashtag in hashtagSuggestions" :key="hashtag.id">
                    <div
                        @click="selectHashtag(hashtag.name)"
                        class="px-4 py-2 hover:bg-neutral-700 cursor-pointer text-gray-300"
                    >
                        <span x-text="`#${hashtag.name}`"></span>
                    </div>
                </template>
            </div>

            <!-- Sugestões de menções -->
            @if($showMentionSuggestions && !empty($mentionSuggestions))
                <div class="absolute z-50 bg-neutral-800 border border-neutral-700 rounded-md shadow-lg max-h-48 overflow-auto min-w-64 mt-1">
                    @foreach($mentionSuggestions as $user)
                        <div
                            wire:click="selectMention('{{ $user['username'] }}')"
                            class="px-4 py-2 hover:bg-neutral-700 cursor-pointer text-gray-300 flex items-center"
                        >
                            <img src="{{ $user['avatar'] ?? '/images/default-avatar.jpg' }}" alt="{{ $user['username'] }}" class="w-8 h-8 rounded-full mr-3">
                            <div class="flex flex-col">
                                <span class="font-medium">{{ $user['name'] }}</span>
                                <span class="text-sm text-gray-400">@{{ $user['username'] }}</span>
                            </div>
                        </div>
                    @endforeach
                </div>
            @endif

            <!-- Sugestões de hashtags -->
            @if($showHashtagSuggestions && !empty($hashtagSuggestions))
                <div class="absolute z-50 bg-neutral-800 border border-neutral-700 rounded-md shadow-lg max-h-48 overflow-auto min-w-48 mt-1">
                    @foreach($hashtagSuggestions as $hashtag)
                        <div
                            wire:click="selectHashtag('{{ $hashtag['name'] }}')"
                            class="px-4 py-2 hover:bg-neutral-700 cursor-pointer text-gray-300"
                        >
                            <span class="font-medium">#{{ $hashtag['name'] }}</span>
                            @if(isset($hashtag['posts_count']))
                                <span class="text-sm text-gray-400 ml-2">{{ $hashtag['posts_count'] }} posts</span>
                            @endif
                        </div>
                    @endforeach
                </div>
            @endif

            @error('content')
                <span class="text-red-500 text-sm mt-1 block">{{ $message }}</span>
            @enderror
        </div>

        @if($image)
            <div class="mt-2">
                <img src="{{ $image->temporaryUrl() }}"
                     class="max-w-xs h-auto rounded-lg shadow-sm"
                     alt="Preview">
            </div>
        @endif
        @error('image') 
            <span class="text-red-500 text-sm mt-1 block">{{ $message }}</span> 
        @enderror

        @if($video)
            <div class="mt-2">
                <video controls class="max-w-xs h-auto rounded-lg shadow-sm">
                    <source src="{{ $video->temporaryUrl() }}" type="video/mp4">
                    Seu navegador não suporta o elemento de vídeo.
                </video>
            </div>
        @endif
        @error('video') 
            <span class="text-red-500 text-sm mt-1 block">{{ $message }}</span> 
        @enderror

        <div class="flex justify-between mt-3">
            <div class="flex space-x-4 items-center">
                <!-- Botão de Imagem -->
                <label for="image" class="cursor-pointer flex items-center text-gray-500 hover:text-purple-500 transition-colors">
                    <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    <input wire:model="image" id="image" type="file" accept="image/*" class="hidden">
                </label>
                @if($image)
                    <span class="text-sm text-gray-500">
                        @if(is_object($image) && method_exists($image, 'getClientOriginalName'))
                            {{ $image->getClientOriginalName() }}
                        @else
                            Imagem selecionada
                        @endif
                    </span>
                @endif

                <!-- Botão de Vídeo -->
                <label for="video" class="cursor-pointer flex items-center text-gray-500 hover:text-purple-500 transition-colors">
                    <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                    <input wire:model="video" id="video" type="file" accept="video/*" class="hidden">
                </label>
                @if($video)
                    <span class="text-sm text-gray-500">
                        @if(is_object($video) && method_exists($video, 'getClientOriginalName'))
                            {{ $video->getClientOriginalName() }}
                        @else
                            Vídeo selecionado
                        @endif
                    </span>
                @endif

                <!-- Botão de Menções -->
                <button
                    type="button"
                    wire:click="showMentionsList"
                    class="cursor-pointer flex items-center text-gray-500 hover:text-blue-500 transition-colors"
                    title="Adicionar menção"
                >
                    <flux:icon name="at-symbol" class="w-5 h-5" />
                </button>

                <!-- Botão de Hashtags -->
                <button
                    type="button"
                    wire:click="showHashtagsList"
                    class="cursor-pointer flex items-center text-gray-500 hover:text-green-500 transition-colors"
                    title="Adicionar hashtag"
                >
                    <flux:icon name="hashtag" class="w-5 h-5" />
                </button>
            </div>
            <button type="submit" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg">
                Postar
            </button>
        </div>
    </form>

    <!-- Modal de Lista de Usuários para Menções -->
    @if($showMentionsModal)
        <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white dark:bg-zinc-800 rounded-lg p-6 w-96 max-h-96 overflow-hidden">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Selecionar Usuário</h3>
                    <button wire:click="closeMentionsModal" class="text-gray-500 hover:text-gray-700">
                        <flux:icon name="x-mark" class="w-5 h-5" />
                    </button>
                </div>

                <div class="mb-4">
                    <input
                        type="text"
                        wire:model.live="mentionSearchQuery"
                        placeholder="Buscar usuários..."
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-zinc-700 dark:text-white"
                    >
                </div>

                <div class="max-h-64 overflow-y-auto">
                    @if(!empty($mentionsList))
                        @foreach($mentionsList as $index => $user)
                            <div
                                wire:click="addMentionFromList({{ $index }})"
                                class="flex items-center p-2 hover:bg-gray-100 dark:hover:bg-zinc-700 cursor-pointer rounded"
                            >
                                <img src="{{ $user['avatar'] ?? '/images/default-avatar.jpg' }}" alt="{{ $user['username'] ?? '' }}" class="w-8 h-8 rounded-full mr-3">
                                <div>
                                    <div class="font-medium text-gray-900 dark:text-white">{{ $user['name'] ?? '' }}</div>
                                    <div class="text-sm text-gray-500">@@{{ $user['username'] ?? '' }}</div>
                                </div>
                            </div>
                        @endforeach
                    @else
                        <div class="text-center text-gray-500 py-4">Nenhum usuário encontrado</div>
                    @endif
                </div>
            </div>
        </div>
    @endif

    <!-- Modal de Lista de Hashtags -->
    @if($showHashtagsModal)
        <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white dark:bg-zinc-800 rounded-lg p-6 w-96 max-h-96 overflow-hidden">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Selecionar Hashtag</h3>
                    <button wire:click="closeHashtagsModal" class="text-gray-500 hover:text-gray-700">
                        <flux:icon name="x-mark" class="w-5 h-5" />
                    </button>
                </div>

                <div class="mb-4">
                    <input
                        type="text"
                        wire:model.live="hashtagSearchQuery"
                        placeholder="Buscar hashtags..."
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-zinc-700 dark:text-white"
                    >
                </div>

                <div class="max-h-64 overflow-y-auto">
                    @if(!empty($hashtagsList))
                        @foreach($hashtagsList as $index => $hashtag)
                            <div
                                wire:click="addHashtagFromList({{ $index }})"
                                class="flex items-center p-2 hover:bg-gray-100 dark:hover:bg-zinc-700 cursor-pointer rounded"
                            >
                                <div class="flex-1">
                                    <div class="font-medium text-gray-900 dark:text-white">#{{ $hashtag['name'] }}</div>
                                    @if(isset($hashtag['posts_count']))
                                        <div class="text-sm text-gray-500">{{ $hashtag['posts_count'] }} posts</div>
                                    @endif
                                </div>
                            </div>
                        @endforeach
                    @else
                        <div class="text-center text-gray-500 py-4">Nenhuma hashtag encontrada</div>
                    @endif
                </div>
            </div>
        </div>
    @endif
</div>
