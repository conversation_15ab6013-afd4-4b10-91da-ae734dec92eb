<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserLabel extends Model
{
    protected $fillable = [
        'user_id',
        'name',
        'color',
        'icon',
        'description',
        'is_active',
        'expires_at',
        'assigned_by'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'expires_at' => 'datetime'
    ];

    /**
     * Relacionamento com o usuário que possui a label
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Relacionamento com o usuário que atribuiu a label
     */
    public function assignedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_by');
    }

    /**
     * Scope para labels ativas
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
                    ->where(function ($q) {
                        $q->whereNull('expires_at')
                          ->orWhere('expires_at', '>', now());
                    });
    }

    /**
     * Verifica se a label está expirada
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * Cores disponíveis para as labels
     */
    public static function getAvailableColors(): array
    {
        return [
            'blue' => 'Azul',
            'green' => 'Verde',
            'yellow' => 'Amarelo',
            'red' => 'Vermelho',
            'purple' => 'Roxo',
            'pink' => 'Rosa',
            'orange' => 'Laranja',
            'teal' => 'Azul-verde',
            'gray' => 'Cinza',
            'gold' => 'Dourado',
            'silver' => 'Prateado'
        ];
    }
}
