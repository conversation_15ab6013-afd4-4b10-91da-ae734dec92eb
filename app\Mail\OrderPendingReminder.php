<?php

namespace App\Mail;

use App\Models\Order;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class OrderPendingReminder extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    protected $order;
    protected $user;
    protected $daysPending;
    protected $isAdminCopy;

    /**
     * Create a new message instance.
     */
    public function __construct(Order $order, User $user, int $daysPending, bool $isAdminCopy = false)
    {
        $this->order = $order;
        $this->user = $user;
        $this->daysPending = $daysPending;
        $this->isAdminCopy = $isAdminCopy;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $subject = $this->isAdminCopy
            ? '[ADMIN COPY] Pedido Pendente há ' . $this->daysPending . ' dias - #' . $this->order->id . ' - ' . $this->user->name
            : 'Pedido Pendente há ' . $this->daysPending . ' dias - Pedido #' . $this->order->id . ' - ' . config('app.name');

        return new Envelope(
            subject: $subject,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        $view = $this->isAdminCopy ? 'emails.admin-order-pending-reminder' : 'emails.order-pending-reminder';

        $data = [
            'order' => $this->order,
            'user' => $this->user,
            'daysPending' => $this->daysPending,
        ];

        if ($this->isAdminCopy) {
            $data['adminRecipient'] = (object) [
                'name' => 'Administração',
                'email' => '<EMAIL>'
            ];
        }

        return new Content(
            view: $view,
            with: $data,
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
