<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\URL;

class TestProductionUrls extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:production-urls';

    /**
     * The description of the console command.
     *
     * @var string
     */
    protected $description = 'Testa como as URLs serão geradas em produção';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧪 Testando URLs como serão em produção...');

        // Simular ambiente de produção temporariamente
        $originalEnv = config('app.env');
        $originalUrl = config('app.url');
        $originalAssetUrl = config('app.asset_url');

        // Configurar temporariamente para produção
        config([
            'app.env' => 'production',
            'app.url' => 'https://www.swingcuritiba.com.br',
            'app.asset_url' => 'https://www.swingcuritiba.com.br',
            'filesystems.disks.public.url' => 'https://www.swingcuritiba.com.br/storage'
        ]);

        // Forçar URL root para simular produção
        URL::forceRootUrl('https://www.swingcuritiba.com.br');
        URL::forceScheme('https');

        $this->info('📋 Configurações simuladas:');
        $this->line('APP_ENV: ' . config('app.env'));
        $this->line('APP_URL: ' . config('app.url'));
        $this->line('ASSET_URL: ' . config('app.asset_url'));
        $this->line('Storage URL: ' . config('filesystems.disks.public.url'));

        $this->info('');
        $this->info('🔗 URLs que serão geradas:');

        // Testar Storage::url()
        $testPath = 'posts/images/test.jpg';
        $storageUrl = Storage::disk('public')->url($testPath);
        $this->line("Storage::url('{$testPath}'): {$storageUrl}");

        // Testar asset()
        $assetUrl = asset('css/app.css');
        $this->line("asset('css/app.css'): {$assetUrl}");

        // Testar url()
        $urlHelper = url('/dashboard');
        $this->line("url('/dashboard'): {$urlHelper}");

        // Testar route()
        try {
            $routeUrl = route('dashboard');
            $this->line("route('dashboard'): {$routeUrl}");
        } catch (\Exception $e) {
            $this->line("route('dashboard'): Erro - " . $e->getMessage());
        }

        // Testar StorageHelper
        if (class_exists('\App\Helpers\StorageHelper')) {
            $helperUrl = \App\Helpers\StorageHelper::getSecureUrl($testPath);
            $this->line("StorageHelper::getSecureUrl('{$testPath}'): " . ($helperUrl ?: 'null'));

            $postImageUrl = \App\Helpers\StorageHelper::getPostImageUrl($testPath);
            $this->line("StorageHelper::getPostImageUrl('{$testPath}'): " . ($postImageUrl ?: 'null'));
        }

        $this->info('');
        $this->info('✅ Teste concluído!');

        // Verificar se as URLs estão corretas
        $correctUrls = true;

        if (!str_starts_with($storageUrl, 'https://www.swingcuritiba.com.br/storage/')) {
            $this->error("❌ Storage URL incorreta: {$storageUrl}");
            $correctUrls = false;
        }

        if (!str_starts_with($assetUrl, 'https://www.swingcuritiba.com.br/')) {
            $this->error("❌ Asset URL incorreta: {$assetUrl}");
            $correctUrls = false;
        }

        if (!str_starts_with($urlHelper, 'https://www.swingcuritiba.com.br/')) {
            $this->error("❌ URL helper incorreta: {$urlHelper}");
            $correctUrls = false;
        }

        if ($correctUrls) {
            $this->info('🎉 Todas as URLs estão corretas para produção!');
        } else {
            $this->warn('⚠️  Algumas URLs ainda estão incorretas');
        }

        // Restaurar configurações originais
        config([
            'app.env' => $originalEnv,
            'app.url' => $originalUrl,
            'app.asset_url' => $originalAssetUrl
        ]);

        return $correctUrls ? 0 : 1;
    }
}
