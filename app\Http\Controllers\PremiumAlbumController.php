<?php

namespace App\Http\Controllers;

use App\Models\Album;
use App\Services\AlbumPurchaseService;
use App\Services\PremiumAlbumService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class PremiumAlbumController extends Controller
{
    protected $purchaseService;
    protected $albumService;

    public function __construct(AlbumPurchaseService $purchaseService, PremiumAlbumService $albumService)
    {
        $this->purchaseService = $purchaseService;
        $this->albumService = $albumService;
    }

    /**
     * Marketplace de álbuns premium.
     */
    public function marketplace()
    {
        return view('premium-albums.marketplace');
    }

    /**
     * Visualizar álbum premium.
     */
    public function show(Album $album)
    {
        $user = Auth::user();

        // Verificar se pode acessar
        if (!$album->canUserAccess($user)) {
            // Se é premium e não comprou, mostrar preview
            if ($album->is_premium && !$album->isPurchasedBy($user)) {
                $medias = $album->getPreviewMedias();
                $isPurchased = false;
            } else {
                abort(403, 'Acesso negado a este álbum.');
            }
        } else {
            // Acesso completo
            $medias = $album->medias;
            $isPurchased = $album->isPurchasedBy($user);
        }

        $reviews = $album->reviews()
            ->with('user')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('premium-albums.show', compact('album', 'medias', 'isPurchased', 'reviews'));
    }

    /**
     * Processar compra de álbum.
     */
    public function purchase(Request $request, Album $album)
    {
        $request->validate([
            'payment_method' => 'required|in:wallet,stripe'
        ]);

        $user = Auth::user();
        $paymentMethod = $request->payment_method;

        try {
            $result = $this->purchaseService->purchaseAlbum($album, $user, $paymentMethod);

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => $result['message'],
                    'redirect' => route('premium-albums.show', $album)
                ]);
            } else {
                // Se requer Stripe
                if (isset($result['requires_stripe'])) {
                    return response()->json([
                        'success' => false,
                        'requires_stripe' => true,
                        'stripe_data' => $result['stripe_data']
                    ]);
                }

                return response()->json([
                    'success' => false,
                    'message' => $result['message']
                ], 400);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Meus álbuns comprados.
     */
    public function myPurchases()
    {
        return view('premium-albums.my-purchases');
    }

    /**
     * Meus álbuns premium criados.
     */
    public function myAlbums()
    {
        return view('premium-albums.my-albums');
    }

    /**
     * Estatísticas de vendas.
     */
    public function salesStats()
    {
        return view('premium-albums.sales-stats');
    }

    /**
     * Servir arquivo de mídia protegido.
     */
    public function serveMedia(Album $album, $mediaId)
    {
        $user = Auth::user();

        // Verificar acesso
        if (!$album->canUserAccess($user)) {
            abort(403, 'Acesso negado.');
        }

        $media = $album->medias()->findOrFail($mediaId);

        if (!Storage::exists($media->file_path)) {
            abort(404, 'Arquivo não encontrado.');
        }

        return Storage::response($media->file_path);
    }
}
