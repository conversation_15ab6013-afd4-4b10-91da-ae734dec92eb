<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('messages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('sender_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('receiver_id')->constrained('users')->onDelete('cascade');
            $table->text('body');
            $table->string('title')->nullable();
            $table->timestamp('read_at')->nullable();
            $table->timestamps();
        });

        // Tabela para indicadores de digitação
        Schema::create('typing_indicators', function (Blueprint $table) {
            $table->id();
            $table->foreignId('conversation_id')->constrained('conversations')->onDelete('cascade');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->timestamp('started_at');
            $table->timestamp('expires_at');
            $table->timestamps();

            $table->unique(['conversation_id', 'user_id']);
            $table->index(['conversation_id', 'expires_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('typing_indicators');
        Schema::table('messages', function (Blueprint $table) {
            $table->dropColumn('file_path');
        });
        Schema::dropIfExists('messages');
    }
};
