<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white dark:bg-zinc-800 shadow-md rounded-lg p-6">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                    Participantes - {{ $event->name }}
                </h1>
                <p class="text-gray-600 dark:text-gray-300 mt-1">
                    {{ $event->formatted_date }} às {{ $event->formatted_start_time }}
                </p>
            </div>
            <div class="flex gap-3">
                <flux:button wire:click="exportAttendees" variant="outline">
                    <flux:icon name="document-arrow-down" class="h-4 w-4 mr-2" />
                    Exportar
                </flux:button>
                <flux:button href="{{ route('events.show', $event->slug) }}" variant="outline">
                    <flux:icon name="eye" class="h-4 w-4 mr-2" />
                    Ver Evento
                </flux:button>
            </div>
        </div>
    </div>

    <!-- Estatísticas -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <div class="bg-white dark:bg-zinc-800 p-4 rounded-lg shadow">
            <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ $stats['total'] }}</div>
            <div class="text-sm text-gray-600 dark:text-gray-300">Total de Inscrições</div>
        </div>
        <div class="bg-white dark:bg-zinc-800 p-4 rounded-lg shadow">
            <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{ $stats['confirmed'] }}</div>
            <div class="text-sm text-gray-600 dark:text-gray-300">Confirmados</div>
        </div>
        <div class="bg-white dark:bg-zinc-800 p-4 rounded-lg shadow">
            <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">{{ $stats['checked_in'] }}</div>
            <div class="text-sm text-gray-600 dark:text-gray-300">Check-in Feito</div>
        </div>
        <div class="bg-white dark:bg-zinc-800 p-4 rounded-lg shadow">
            <div class="text-2xl font-bold text-orange-600 dark:text-orange-400">{{ $stats['pending_payment'] }}</div>
            <div class="text-sm text-gray-600 dark:text-gray-300">Pagamento Pendente</div>
        </div>
        <div class="bg-white dark:bg-zinc-800 p-4 rounded-lg shadow">
            <div class="text-2xl font-bold text-green-600 dark:text-green-400">R$ {{ number_format($stats['revenue'], 2, ',', '.') }}</div>
            <div class="text-sm text-gray-600 dark:text-gray-300">Receita Total</div>
        </div>
    </div>

    <!-- Filtros -->
    <div class="bg-white dark:bg-zinc-800 shadow-md rounded-lg p-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <flux:input 
                    wire:model.live.debounce.300ms="search" 
                    placeholder="Buscar por nome, email ou código..."
                    class="w-full"
                />
            </div>
            <div>
                <flux:select wire:model.live="statusFilter" placeholder="Status">
                    <flux:option value="">Todos os Status</flux:option>
                    <flux:option value="registered">Registrado</flux:option>
                    <flux:option value="confirmed">Confirmado</flux:option>
                    <flux:option value="attended">Presente</flux:option>
                    <flux:option value="cancelled">Cancelado</flux:option>
                </flux:select>
            </div>
            <div>
                <flux:select wire:model.live="paymentStatusFilter" placeholder="Pagamento">
                    <flux:option value="">Todos os Pagamentos</flux:option>
                    <flux:option value="pending">Pendente</flux:option>
                    <flux:option value="completed">Pago</flux:option>
                    <flux:option value="failed">Falhou</flux:option>
                    <flux:option value="refunded">Reembolsado</flux:option>
                </flux:select>
            </div>
            <div>
                <flux:select wire:model.live="perPage">
                    <flux:option value="10">10 por página</flux:option>
                    <flux:option value="15">15 por página</flux:option>
                    <flux:option value="25">25 por página</flux:option>
                    <flux:option value="50">50 por página</flux:option>
                </flux:select>
            </div>
        </div>
    </div>

    <!-- Tabela de Participantes -->
    <div class="bg-white dark:bg-zinc-800 shadow-md rounded-lg overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-zinc-700">
                <thead class="bg-gray-50 dark:bg-zinc-700">
                    <tr>
                        <th wire:click="sortBy('user.name')" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-zinc-600">
                            Participante
                            @if($sortBy === 'user.name')
                                <flux:icon name="{{ $sortDirection === 'asc' ? 'chevron-up' : 'chevron-down' }}" class="h-4 w-4 inline ml-1" />
                            @endif
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Código do Ingresso
                        </th>
                        <th wire:click="sortBy('status')" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-zinc-600">
                            Status
                            @if($sortBy === 'status')
                                <flux:icon name="{{ $sortDirection === 'asc' ? 'chevron-up' : 'chevron-down' }}" class="h-4 w-4 inline ml-1" />
                            @endif
                        </th>
                        <th wire:click="sortBy('payment_status')" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-zinc-600">
                            Pagamento
                            @if($sortBy === 'payment_status')
                                <flux:icon name="{{ $sortDirection === 'asc' ? 'chevron-up' : 'chevron-down' }}" class="h-4 w-4 inline ml-1" />
                            @endif
                        </th>
                        <th wire:click="sortBy('amount_paid')" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-zinc-600">
                            Valor Pago
                            @if($sortBy === 'amount_paid')
                                <flux:icon name="{{ $sortDirection === 'asc' ? 'chevron-up' : 'chevron-down' }}" class="h-4 w-4 inline ml-1" />
                            @endif
                        </th>
                        <th wire:click="sortBy('created_at')" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-zinc-600">
                            Data Inscrição
                            @if($sortBy === 'created_at')
                                <flux:icon name="{{ $sortDirection === 'asc' ? 'chevron-up' : 'chevron-down' }}" class="h-4 w-4 inline ml-1" />
                            @endif
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Check-in
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Ações
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-zinc-800 divide-y divide-gray-200 dark:divide-zinc-700">
                    @forelse($attendees as $attendee)
                        <tr class="hover:bg-gray-50 dark:hover:bg-zinc-700">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <img class="h-10 w-10 rounded-full object-cover" 
                                             src="{{ $attendee->user->avatar_url }}" 
                                             alt="{{ $attendee->user->name }}">
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900 dark:text-white">
                                            {{ $attendee->user->name }}
                                        </div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400">
                                            {{ $attendee->user->email }}
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="font-mono text-sm bg-gray-100 dark:bg-zinc-700 px-2 py-1 rounded">
                                    {{ $attendee->ticket_code ?: '-' }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @php
                                    $statusColors = [
                                        'registered' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
                                        'confirmed' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
                                        'attended' => 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
                                        'cancelled' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
                                    ];
                                    $statusLabels = [
                                        'registered' => 'Registrado',
                                        'confirmed' => 'Confirmado',
                                        'attended' => 'Presente',
                                        'cancelled' => 'Cancelado',
                                    ];
                                @endphp
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $statusColors[$attendee->status] ?? 'bg-gray-100 text-gray-800' }}">
                                    {{ $statusLabels[$attendee->status] ?? $attendee->status }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @php
                                    $paymentColors = [
                                        'pending' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
                                        'completed' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
                                        'failed' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
                                        'refunded' => 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
                                    ];
                                    $paymentLabels = [
                                        'pending' => 'Pendente',
                                        'completed' => 'Pago',
                                        'failed' => 'Falhou',
                                        'refunded' => 'Reembolsado',
                                    ];
                                @endphp
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $paymentColors[$attendee->payment_status] ?? 'bg-gray-100 text-gray-800' }}">
                                    {{ $paymentLabels[$attendee->payment_status] ?? $attendee->payment_status }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                R$ {{ number_format($attendee->amount_paid, 2, ',', '.') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                {{ $attendee->created_at->format('d/m/Y H:i') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                @if($attendee->checked_in_at)
                                    <span class="text-green-600 dark:text-green-400 font-medium">
                                        <flux:icon name="check-circle" class="h-4 w-4 inline mr-1" />
                                        {{ $attendee->checked_in_at->format('d/m/Y H:i') }}
                                    </span>
                                @else
                                    <span class="text-gray-400">Não realizado</span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                @if(!$attendee->checked_in_at && $attendee->payment_status === 'completed')
                                    <flux:button 
                                        wire:click="quickCheckIn({{ $attendee->id }})" 
                                        size="sm" 
                                        variant="outline"
                                        class="text-green-600 hover:text-green-900"
                                    >
                                        <flux:icon name="check" class="h-4 w-4" />
                                    </flux:button>
                                    <flux:button 
                                        wire:click="openCheckInModal({{ $attendee->id }})" 
                                        size="sm" 
                                        variant="outline"
                                    >
                                        <flux:icon name="qr-code" class="h-4 w-4" />
                                    </flux:button>
                                @endif
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="8" class="px-6 py-12 text-center text-gray-500 dark:text-gray-400">
                                <flux:icon name="users" class="h-12 w-12 mx-auto mb-4 text-gray-300" />
                                <p class="text-lg font-medium">Nenhum participante encontrado</p>
                                <p class="text-sm">Não há participantes para este evento ainda.</p>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Paginação -->
        @if($attendees->hasPages())
            <div class="px-6 py-4 border-t border-gray-200 dark:border-zinc-700">
                {{ $attendees->links() }}
            </div>
        @endif
    </div>

    <!-- Modal de Check-in -->
    @if($showCheckInModal && $selectedAttendee)
        <flux:modal wire:model="showCheckInModal" class="max-w-md">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                    Confirmar Check-in
                </h3>
                
                <div class="space-y-4">
                    <div>
                        <p class="text-sm text-gray-600 dark:text-gray-300">
                            <strong>Participante:</strong> {{ $selectedAttendee->user->name }}
                        </p>
                        <p class="text-sm text-gray-600 dark:text-gray-300">
                            <strong>Email:</strong> {{ $selectedAttendee->user->email }}
                        </p>
                    </div>
                    
                    <div>
                        <flux:field>
                            <flux:label>Código do Ingresso</flux:label>
                            <flux:input 
                                wire:model="ticketCodeInput" 
                                placeholder="Digite o código do ingresso"
                                class="font-mono"
                            />
                            <flux:description>
                                Código esperado: <strong class="font-mono">{{ $selectedAttendee->ticket_code }}</strong>
                            </flux:description>
                        </flux:field>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3 mt-6">
                    <flux:button wire:click="closeCheckInModal" variant="outline">
                        Cancelar
                    </flux:button>
                    <flux:button wire:click="checkInAttendee" variant="primary">
                        Confirmar Check-in
                    </flux:button>
                </div>
            </div>
        </flux:modal>
    @endif
</div>
