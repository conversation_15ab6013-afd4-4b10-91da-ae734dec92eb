<?php
use Illuminate\Support\Facades\Storage;
?>

<div>
    @if($errors->has('global'))
        <div class="mb-4 p-3 rounded bg-red-100 text-red-700 border border-red-400">
            {{ $errors->first('global') }}
        </div>
    @endif
     <flux:heading>Busca Avançada</flux:heading>
     <flux:subheading>Encontre usuários, posts e eventos na plataforma.</flux:subheading>

    <section id="searchform" class="p-6 border border-gray-200 dark:border-gray-7s0 shadow-md rounded-lg">
        <form wire:submit.prevent="search" class="space-y-6">

            {{-- Tipo de busca e campo principal --}}
            <div class="p-6 rounded-xl border border-purple-300 dark:border-purple-600">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div class="md:col-span-1">
                        <flux:field>
                            <flux:label>Tipo de Busca</flux:label>
                            <flux:select wire:model.live="searchType">
                                <option value="users">👥 Usuários</option>
                                <option value="posts">📝 Posts</option>
                                <option value="events">🎉 Eventos</option>
                            </flux:select>
                        </flux:field>
                    </div>
                    <div class="md:col-span-2">
                        <flux:field>
                            <flux:label>
                                @if(($searchType ?? 'users') === 'users')
                                    Buscar usuários por nome, username ou bio
                                @elseif(($searchType ?? 'users') === 'posts')
                                    Buscar posts por conteúdo
                                @else
                                    Buscar eventos por nome, descrição ou localização
                                @endif
                            </flux:label>
                            <flux:input
                                wire:model="searchQuery"
                                placeholder="Digite sua busca..."
                                icon="magnifying-glass"
                                value="{{ $searchQuery ?? '' }}"
                            />
                        </flux:field>
                    </div>
                </div>
            </div>

            {{-- Filtros específicos para usuários --}}
            @if(($searchType ?? 'users') === 'users')
            <div class="grid grid-cols-4 gap-4">
            <flux:field>
                <flux:label for="id">ID</flux:label>
                <flux:input id="id" wire:model="filters.id" />
                <flux:error name="filters.id" />
            </flux:field>

            <flux:field>
                <flux:label for="username">Username</flux:label>
                <flux:input id="username" wire:model="filters.username" />
                <flux:error name="filters.username" />
            </flux:field>

            {{-- Campo anúncio removido - coluna não existe na tabela users --}}
        </div>

            <div class="grid grid-cols-2 gap-4">
                <flux:field>
                    <flux:label for="estado">Estado</flux:label>
                    <flux:select id="estado" wire:model.live="selectedState" placeholder="Selecione">
                        @foreach(($states ?? []) as $state)
                            <option value="{{ $state->id }}">{{ $state->name }}</option>
                        @endforeach
                    </flux:select>
                    <flux:error name="selectedState" />
                </flux:field>

                <flux:field>
                    <flux:label for="cidade">Cidade</flux:label>
                    <flux:select id="cidade" wire:model.live="selectedCity" placeholder="Selecione">
                        @foreach(($cities ?? []) as $city)
                            <option value="{{ $city->id }}">{{ $city->name }}</option>
                        @endforeach
                    </flux:select>
                    <flux:error name="selectedCity" />
                </flux:field>
            </div>


            {{-- Filtros específicos para usuários --}}
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 bg-white dark:bg-zinc-800 p-6 rounded-xl shadow-inner">
                {{-- Coluna 1 --}}
                <div class="space-y-4">
                    <flux:radio.group label="Perfil com Foto?" wire:model="filters.foto">
                        <flux:radio value="1" label="Sim" checked />
                        <flux:radio value="0" label="Não" />
                    </flux:radio.group>

                    <label class="block text-sm font-medium text-gray-700 dark:text-white">Sexo:</label>
                    <div class="space-y-2">
                        <flux:checkbox wire:model="filters.sexo" value="casal" label="Casal" />
                        <flux:checkbox wire:model="filters.sexo" value="homem" label="Homem" />
                        <flux:checkbox wire:model="filters.sexo" value="mulher" label="Mulher" />
                    </div>
                </div>

                {{-- Coluna 2 --}}
                <div class="space-y-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-white">Ordenar por:</label>
                    <flux:radio.group wire:model="filters.ordenar">
                        <flux:radio value="id_crescente" label="Mais Antigos" />
                        <flux:radio value="id_decrescente" label="Mais Recentes" />
                        <flux:radio value="last_access" label="Último Acesso" checked />
                    </flux:radio.group>

                    <label class="block text-sm font-medium text-gray-700 dark:text-white">Cadastrados há:</label>
                    <flux:radio.group wire:model="filters.cadastrados">
                        <flux:radio value="7_dias" label="7 Dias" />
                        <flux:radio value="15_dias" label="15 Dias" />
                        <flux:radio value="30_dias" label="30 Dias" />
                        <flux:radio value="all" label="Todos" checked />
                    </flux:radio.group>
                </div>
            </div>
            @endif

            {{-- Filtros para posts --}}
            @if(($searchType ?? 'users') === 'posts')
            <div class="bg-white dark:bg-zinc-800 p-6 rounded-xl shadow-inner">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-white mb-2">Ordenar por:</label>
                        <flux:radio.group wire:model="filters.ordenar">
                            <flux:radio value="id_decrescente" label="Mais Recentes" checked />
                            <flux:radio value="id_crescente" label="Mais Antigos" />
                        </flux:radio.group>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-white mb-2">Posts de:</label>
                        <flux:radio.group wire:model="filters.cadastrados">
                            <flux:radio value="7_dias" label="Últimos 7 Dias" checked />
                            <flux:radio value="15_dias" label="Últimos 15 Dias" />
                            <flux:radio value="30_dias" label="Últimos 30 Dias" />
                            <flux:radio value="all" label="Todos os Tempos" />
                        </flux:radio.group>
                    </div>
                </div>
            </div>
            @endif

            {{-- Filtros para eventos --}}
            @if(($searchType ?? 'users') === 'events')
            <div class="bg-white dark:bg-zinc-800 p-6 rounded-xl shadow-inner">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-white mb-2">Ordenar por:</label>
                        <flux:radio.group wire:model="filters.ordenar">
                            <flux:radio value="date" label="Data do Evento" checked />
                            <flux:radio value="id_decrescente" label="Mais Recentes" />
                            <flux:radio value="id_crescente" label="Mais Antigos" />
                        </flux:radio.group>
                    </div>
                    <div>
                        <flux:field>
                            <flux:label>Estado</flux:label>
                            <flux:select wire:model.live="selectedState" placeholder="Selecione">
                                @foreach(($states ?? []) as $state)
                                    <option value="{{ $state->id }}">{{ $state->name }}</option>
                                @endforeach
                            </flux:select>
                        </flux:field>
                    </div>
                </div>
            </div>
            @endif



            {{-- Botão de busca --}}
            <div class="flex justify-center items-center gap-3 pt-4">
                <div wire:loading wire:target="search" class="text-sm text-gray-500 flex items-center">
                    <svg class="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Buscando...
                </div>
                <button type="submit" class="inline-flex items-center px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg shadow-sm transition-colors">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    @if(($searchType ?? 'users') === 'users')
                        Buscar Usuários
                    @elseif(($searchType ?? 'users') === 'posts')
                        Buscar Posts
                    @else
                        Buscar Eventos
                    @endif
                </button>
            </div>
        </form>
    </section>

    {{-- Resultados --}}
    @if($hasSearched ?? false)
        <section id="results" class="p-6 border border-neutral-200 dark:border-neutral-700 shadow-md rounded-lg mt-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-medium text-gray-300">
                    @if(($searchType ?? 'users') === 'users')
                        👥 Usuários Encontrados
                    @elseif(($searchType ?? 'users') === 'posts')
                        📝 Posts Encontrados
                    @else
                        🎉 Eventos Encontrados
                    @endif
                </h2>
                <p class="text-sm text-gray-500">
                    {{ count($results ?? []) }}
                    @if(($searchType ?? 'users') === 'users')
                        {{ count($results ?? []) == 1 ? 'usuário encontrado' : 'usuários encontrados' }}
                    @elseif(($searchType ?? 'users') === 'posts')
                        {{ count($results ?? []) == 1 ? 'post encontrado' : 'posts encontrados' }}
                    @else
                        {{ count($results ?? []) == 1 ? 'evento encontrado' : 'eventos encontrados' }}
                    @endif
                </p>
            </div>

            @if(($searchType ?? 'users') === 'users')
                {{-- Resultados de usuários --}}
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    @forelse($results as $result)
                    <div class="pb-6 border border-neutral-200 dark:border-neutral-700 relative rounded-lg shadow-md">
                        {{-- Cover photo --}}
                        <div class="relative h-32 bg-cover bg-center rounded-t-lg"
                            style="background-image: url('{{
                                $result->userCoverPhotos->first()
                                ? Storage::url($result->userCoverPhotos->first()->cropped_photo_path ?? $result->userCoverPhotos->first()->photo_path)
                                : asset('images/users/capa.svg')
                            }}'); background-size: cover; background-position: center;">
                        </div>

                        {{-- User info --}}
                        <div class="relative z-10 -mt-12 flex flex-col items-center">
                            {{-- Avatar with status indicator --}}
                            <div class="relative">
                                @php
                                    $searchResultAvatar = $result->currentPhoto ?? $result->userPhotos->first();
                                    $searchResultAvatarUrl = $searchResultAvatar ? Storage::url($searchResultAvatar->photo_path) : asset('images/default-avatar.jpg');
                                @endphp
                                <img src="{{ $searchResultAvatarUrl }}"
                                alt="Foto de Perfil" class="w-24 h-24 rounded-full border-4 border-white shadow-lg object-cover">
                            </div>

                            {{-- Name and username --}}
                            <h2 class="text-xl font-semibold mt-2 text-gray-300">{{ $result->name }}</h2>
                            <p class="text-gray-400">
                                @if($result->username)
                                    <a href="{{ route('user.profile', ['username' => $result->username]) }}" class="hover:underline">
                                        {{ '@' . $result->username }}
                                    </a>
                                @else
                                    <span>@sem-username</span>
                                @endif
                            </p>

                            {{-- User stats --}}
                            <div class="mt-4 flex justify-around w-full">
                                <div class="text-center">
                                    <p class="text-lg font-semibold text-gray-300">{{ count($result->posts) }}</p>
                                    <p class="text-gray-300">Posts</p>
                                </div>
                                <div class="text-center">
                                    <p class="text-lg font-semibold text-gray-300">{{ count($result->following) }}</p>
                                    <p class="text-gray-300">Seguindo</p>
                                </div>
                                <div class="text-center">
                                    <p class="text-lg font-semibold  text-gray-300">{{ count($result->followers) }}</p>
                                    <p class="text-gray-300">Seguidores</p>
                                </div>
                            </div>

                            {{-- Additional info --}}
                            @if($result->bio)
                                <div class="mt-3 px-4 w-full">
                                    <p class="text-sm text-gray-300 dark:text-gray-400 text-center">{{ Str::limit($result->bio, 100) }}</p>
                                </div>
                            @endif

                            {{-- Location info if available --}}
                            @if($result->city || $result->state)
                                <div class="mt-2 flex items-center justify-center text-sm text-gray-300">
                                    <x-flux::icon name="map-pin" class="w-4 h-4 mr-1" />
                                    {{ $result->city?->name ?? '' }} {{ $result->city && $result->state ? ',' : '' }} {{ $result->state?->name ?? '' }}
                                </div>
                            @endif

                            {{-- Follow button --}}
                            @if(auth()->check() && auth()->id() !== $result->id && $result->username)
                                <div class="mt-3">
                                    <a href="{{ route('user.profile', ['username' => $result->username]) }}"
                                       class="inline-flex items-center px-4 py-2 bg-red-500 text-white text-sm font-medium rounded-md shadow-sm hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                        Ver perfil
                                    </a>
                                </div>
                            @endif
                        </div>
                    </div>
                    @empty
                        <div class="col-span-full text-center py-12">
                            <svg class="w-16 h-16 mx-auto text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                            <p class="text-lg text-gray-300 mb-2">Nenhum usuário encontrado.</p>
                            <p class="text-sm text-gray-300">Tente ajustar seus critérios de busca.</p>
                        </div>
                    @endforelse
                </div>

            @elseif(($searchType ?? 'users') === 'posts')
                {{-- Resultados de posts --}}
                <div class="space-y-4">
                    @forelse($results as $post)
                        <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 p-6">
                            <div class="flex items-start space-x-4">
                                <div class="flex-shrink-0">
                                    @php
                                        $postAvatar = $post->user->currentPhoto ?? $post->user->userPhotos->first();
                                        $postAvatarUrl = $postAvatar ? Storage::url($postAvatar->photo_path) : asset('images/default-avatar.jpg');
                                    @endphp
                                    <img src="{{ $postAvatarUrl }}" alt="Avatar" class="w-12 h-12 rounded-full object-cover">
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center space-x-2 mb-2">
                                        <h3 class="text-sm font-medium text-gray-900 dark:text-white">{{ $post->user->name }}</h3>
                                        <span class="text-sm text-gray-500">@{{ $post->user->username }}</span>
                                        <span class="text-sm text-gray-400">•</span>
                                        <span class="text-sm text-gray-400">{{ $post->created_at->diffForHumans() }}</span>
                                    </div>
                                    <div class="text-gray-700 dark:text-gray-300 mb-3">
                                        {{ Str::limit($post->content, 200) }}
                                    </div>
                                    @if($post->image)
                                        <div class="mb-3">
                                            <img src="{{ Storage::url($post->image) }}" alt="Post image" class="rounded-lg max-w-sm h-auto">
                                        </div>
                                    @endif
                                    @if($post->video)
                                        <div class="mb-3">
                                            <video controls class="rounded-lg max-w-sm h-auto">
                                                <source src="{{ Storage::url($post->video) }}" type="video/mp4">
                                            </video>
                                        </div>
                                    @endif
                                    <div class="flex items-center space-x-4 text-sm text-gray-500">
                                        <span>❤️ {{ $post->likedByUsers->count() }} curtidas</span>
                                        <span>💬 {{ $post->comments->count() }} comentários</span>
                                        <a href="{{ route('user.profile', ['username' => $post->user->username]) }}" class="text-purple-600 hover:text-purple-800">Ver perfil</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-12">
                            <svg class="w-16 h-16 mx-auto text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <p class="text-lg text-gray-300 mb-2">Nenhum post encontrado.</p>
                            <p class="text-sm text-gray-300">Tente usar palavras-chave diferentes.</p>
                        </div>
                    @endforelse
                </div>

            @else
                {{-- Resultados de eventos --}}
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    @forelse($results as $event)
                        <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 overflow-hidden">
                            @if($event->image)
                                <div class="h-48 bg-cover bg-center" style="background-image: url('{{ Storage::url($event->image) }}')"></div>
                            @else
                                <div class="h-48 bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
                                    <svg class="w-16 h-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                            @endif
                            <div class="p-6">
                                <div class="flex items-center space-x-2 mb-2">
                                    <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                    <span class="text-sm text-gray-500">{{ $event->date->format('d/m/Y') }}</span>
                                    @if($event->start_time)
                                        <span class="text-sm text-gray-500">às {{ $event->start_time->format('H:i') }}</span>
                                    @endif
                                </div>
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">{{ $event->name }}</h3>
                                <p class="text-gray-600 dark:text-gray-300 text-sm mb-3">{{ Str::limit($event->description, 100) }}</p>
                                @if($event->location)
                                    <div class="flex items-center space-x-1 mb-3">
                                        <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        </svg>
                                        <span class="text-sm text-gray-500">{{ $event->location }}</span>
                                    </div>
                                @endif
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        @if($event->price > 0)
                                            <span class="text-lg font-bold text-green-600">R$ {{ number_format($event->price, 2, ',', '.') }}</span>
                                        @else
                                            <span class="text-lg font-bold text-green-600">Gratuito</span>
                                        @endif
                                    </div>
                                    <div class="text-sm text-gray-500">
                                        👥 {{ $event->attendees->count() }} participantes
                                    </div>
                                </div>
                                <div class="mt-4">
                                    <a href="{{ route('events.show', $event->slug) }}" class="w-full bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition-colors text-center block">
                                        Ver Evento
                                    </a>
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="col-span-full text-center py-12">
                            <svg class="w-16 h-16 mx-auto text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            <p class="text-lg text-gray-300 mb-2">Nenhum evento encontrado.</p>
                            <p class="text-sm text-gray-300">Tente ajustar sua busca ou localização.</p>
                        </div>
                    @endforelse
                </div>
            @endif
        </section>
    @endif
</div>
