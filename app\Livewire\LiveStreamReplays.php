<?php

namespace App\Livewire;

use App\Models\LiveStream;
use Livewire\Component;
use Livewire\WithPagination;

class LiveStreamReplays extends Component
{
    use WithPagination;

    public $search = '';

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function render()
    {
        // Buscar todas as lives com replay de forma simples
        $replays = LiveStream::where('has_replay', true)
            ->whereNotNull('video_path')
            ->with(['user'])
            ->when($this->search, function ($query) {
                $query->where('title', 'like', '%' . $this->search . '%');
            })
            ->latest('ended_at')
            ->paginate(12);

        return view('livewire.live-stream-replays', [
            'replays' => $replays
        ]);
    }
}
