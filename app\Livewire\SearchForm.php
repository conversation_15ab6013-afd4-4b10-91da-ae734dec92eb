<?php

namespace App\Livewire;

use App\Models\State;
use App\Models\City;
use App\Models\User;
use App\Models\Post;
use App\Models\Event;
use App\Models\Procura;
use App\Models\Hobby;
use Livewire\Component;

class SearchForm extends Component
{
    public $states = [];
    public $cities = [];
    public $selectedState = null;
    public $selectedCity = null;
    public $real_profiles = true;

    public $searchType = 'users'; // users, posts, events
    public $searchQuery = '';

    public $filters = [
        'id' => null,
        'username' => null,
        'foto' => '1',
        'ordenar' => 'last_access',
        'cadastrados' => 'all',
        'sexo' => [],
    ];

    public $procuras = [];
    public $results = [];
    public $hasSearched = false;

    public function rules()
    {
        return [
            'searchQuery' => ['nullable', 'string', 'max:255'],
            'searchType' => ['required', 'in:users,posts,events'],
            'filters.id' => ['nullable'],
            'filters.username' => ['nullable'],
            'selectedState' => ['nullable'],
            'selectedCity' => ['nullable'],
            'filters.sexo' => ['nullable', 'array'],
        ];
    }

    public function mount()
    {
        try {
            $this->states = State::orderBy('name', 'asc')->get() ?? collect([]);
            $this->procuras = Procura::orderBy('nome', 'asc')->get() ?? collect([]);
            $this->cities = collect([]); // Inicializar cities como coleção vazia
            $this->hasSearched = false;

            // Inicializar propriedades padrão
            if (empty($this->searchType)) {
                $this->searchType = 'users';
            }
            if (empty($this->searchQuery)) {
                $this->searchQuery = '';
            }
        } catch (\Exception $e) {
            \Log::error('Erro no mount do SearchForm', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Inicializar com valores padrão em caso de erro
            $this->states = collect([]);
            $this->procuras = collect([]);
            $this->cities = collect([]);
            $this->hasSearched = false;
            $this->searchType = 'users';
            $this->searchQuery = '';
        }
    }

    public function updatedSelectedState($stateId)
    {
        $this->cities = City::where('state_id', $stateId)->orderBy('name', 'asc')->get();
        $this->selectedCity = null;
    }

    public function search()
    {
        $this->resetErrorBag();
        $this->validate();

        // Verifica se pelo menos um filtro foi preenchido
        if (
            empty($this->searchQuery) &&
            empty($this->filters['id']) &&
            empty($this->filters['username']) &&
            empty($this->selectedState) &&
            empty($this->selectedCity) &&
            empty($this->filters['foto']) &&
            empty($this->filters['ordenar']) &&
            empty($this->filters['cadastrados']) &&
            empty($this->filters['sexo'])
        ) {
            $this->addError('global', 'Preencha pelo menos um campo para buscar.');
            $this->hasSearched = false;
            $this->results = [];
            return;
        }

        $this->hasSearched = true;

        // Buscar baseado no tipo selecionado
        switch ($this->searchType) {
            case 'posts':
                $this->results = $this->searchPosts();
                break;
            case 'events':
                $this->results = $this->searchEvents();
                break;
            default:
                $this->results = $this->searchUsers();
                break;
        }
    }

    private function searchUsers()
    {
        $query = User::query();

        // Busca geral por texto
        if ($this->searchQuery) {
            $query->where(function ($q) {
                $q->where('name', 'like', '%' . $this->searchQuery . '%')
                  ->orWhere('username', 'like', '%' . $this->searchQuery . '%')
                  ->orWhere('bio', 'like', '%' . $this->searchQuery . '%');
            });
        }

        if ($this->filters['id']) {
            $query->where('id', $this->filters['id']);
        }

        if ($this->filters['username']) {
            $query->where('username', 'like', '%' . $this->filters['username'] . '%');
        }

        // Campo 'anuncio' removido - coluna não existe na tabela users

        if ($this->selectedState) {
            $query->where('state_id', $this->selectedState);
        }

        if ($this->selectedCity) {
            $query->where('city_id', $this->selectedCity);
        }

        // Filtro por sexo
        if (!empty($this->filters['sexo'])) {
            $query->whereIn('sexo', $this->filters['sexo']);
        }

        // Filtro por foto (verifica se o usuário tem fotos)
        if ($this->filters['foto'] === '1') {
            $query->whereHas('userPhotos');
        } elseif ($this->filters['foto'] === '0') {
            $query->whereDoesntHave('userPhotos');
        }

        // Removidos os filtros "busco por" e "que buscam" para melhorar UX

        // Filtro por perfis reais (usuários com foto, bio e pelo menos um post)
        if ($this->real_profiles) {
            $query->whereHas('userPhotos')
                  ->whereNotNull('bio')
                  ->whereHas('posts');
        }

        // Ordenação
        if ($this->filters['ordenar']) {
            switch ($this->filters['ordenar']) {
                case 'id_crescente':
                    $query->orderBy('id', 'asc');
                    break;
                case 'id_decrescente':
                    $query->orderBy('id', 'desc');
                    break;
                case 'last_access':
                    $query->orderBy('last_seen', 'desc');
                    break;
                default:
                    $query->orderBy('id', 'desc');
            }
        }

        // Filtro por data de cadastro
        if ($this->filters['cadastrados']) {
            $days = match ($this->filters['cadastrados']) {
                '7_dias' => 7,
                '15_dias' => 15,
                '30_dias' => 30,
                default => null,
            };

            if ($days && $days !== 'all') {
                $query->where('created_at', '>=', now()->subDays($days));
            }
        }

        // Ensure we only get users with a username
        $query->whereNotNull('username');

        // Eager load relationships needed for the user cards
        return $query->with([
            'currentPhoto',
            'userPhotos',
            'userCoverPhotos',
            'posts',
            'followers',
            'following',
            'city',
            'state',
            'procuras'
        ])->get();
    }

    private function searchPosts()
    {
        $query = Post::query();

        // Busca geral por texto no conteúdo
        if ($this->searchQuery) {
            $query->where('content', 'like', '%' . $this->searchQuery . '%');
        }

        // Filtro por data de criação
        if ($this->filters['cadastrados'] && $this->filters['cadastrados'] !== 'all') {
            $days = match ($this->filters['cadastrados']) {
                '7_dias' => 7,
                '15_dias' => 15,
                '30_dias' => 30,
                default => null,
            };

            if ($days) {
                $query->where('created_at', '>=', now()->subDays($days));
            }
        }

        // Apenas posts públicos (não de grupos)
        $query->whereNull('group_id');

        // Ordenação
        switch ($this->filters['ordenar']) {
            case 'id_crescente':
                $query->orderBy('id', 'asc');
                break;
            case 'id_decrescente':
                $query->orderBy('id', 'desc');
                break;
            default:
                $query->latest();
                break;
        }

        return $query->with([
            'user.currentPhoto',
            'user.userPhotos',
            'likedByUsers',
            'comments.user.currentPhoto',
            'hashtags'
        ])->limit(50)->get();
    }

    private function searchEvents()
    {
        $query = Event::query();

        // Busca geral por texto
        if ($this->searchQuery) {
            $query->where(function ($q) {
                $q->where('name', 'like', '%' . $this->searchQuery . '%')
                  ->orWhere('description', 'like', '%' . $this->searchQuery . '%')
                  ->orWhere('location', 'like', '%' . $this->searchQuery . '%')
                  ->orWhere('city', 'like', '%' . $this->searchQuery . '%');
            });
        }

        // Filtro por estado/cidade
        if ($this->selectedState) {
            $query->where('state', $this->selectedState);
        }

        if ($this->selectedCity) {
            $query->where('city', $this->selectedCity);
        }

        // Apenas eventos ativos
        $query->where('is_active', true);

        // Ordenação
        switch ($this->filters['ordenar']) {
            case 'id_crescente':
                $query->orderBy('id', 'asc');
                break;
            case 'id_decrescente':
                $query->orderBy('id', 'desc');
                break;
            default:
                $query->orderBy('date', 'asc');
                break;
        }

        return $query->with([
            'creator.currentPhoto',
            'attendees'
        ])->limit(50)->get();
    }

    public function render()
    {
        return view('livewire.search-form', [
            'states' => $this->states,
            'cities' => $this->cities,
            'procuras' => $this->procuras,
        ]);
    }
}
