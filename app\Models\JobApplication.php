<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class JobApplication extends Model
{
    use HasFactory;

    protected $fillable = [
        'job_id',
        'job_vacancy_id',
        'user_id',
        'status',
        'cover_letter',
        'resume_path',
        'resume_filename',
        'admin_notes',
        'reviewed_at',
        'reviewed_by',
        'is_vip_priority',
        // Campos de auditoria
        'ip_address',
        'user_agent',
        'form_data',
        'email_sent_at',
        'email_confirmed_at',
        'email_log',
        // Campos adicionais do candidato
        'candidate_name',
        'candidate_email',
        'candidate_phone',
        'candidate_experience',
        // Campos de status avançado
        'priority_level',
        'status_history',
        'last_status_change',
        'status_changed_by',
    ];

    protected $casts = [
        'reviewed_at' => 'datetime',
        'is_vip_priority' => 'boolean',
        'form_data' => 'array',
        'email_log' => 'array',
        'status_history' => 'array',
        'email_sent_at' => 'datetime',
        'email_confirmed_at' => 'datetime',
        'last_status_change' => 'datetime',
    ];

    /**
     * Vaga da candidatura
     */
    public function job()
    {
        return $this->belongsTo(JobVacancy::class, 'job_id');
    }

    /**
     * Usuário candidato
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Admin que revisou a candidatura
     */
    public function reviewer()
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    /**
     * Scope para candidaturas pendentes
     */
    public function scopePending($query)
    {
        return $query->where('status', 'Pendente');
    }

    /**
     * Scope para candidaturas em análise
     */
    public function scopeInReview($query)
    {
        return $query->where('status', 'Em Análise');
    }

    /**
     * Scope para candidaturas aprovadas
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'Aprovada');
    }

    /**
     * Scope para candidaturas rejeitadas
     */
    public function scopeRejected($query)
    {
        return $query->where('status', 'Rejeitada');
    }

    /**
     * Scope para candidaturas VIP
     */
    public function scopeVip($query)
    {
        return $query->where('is_vip_priority', true);
    }

    /**
     * Scope para candidaturas não VIP
     */
    public function scopeNonVip($query)
    {
        return $query->where('is_vip_priority', false);
    }

    /**
     * Verificar se a candidatura foi revisada
     */
    public function isReviewed()
    {
        return !is_null($this->reviewed_at);
    }

    /**
     * Verificar se a candidatura é VIP
     */
    public function isVip()
    {
        return $this->is_vip_priority;
    }

    /**
     * Obter a classe CSS do status
     */
    public function getStatusClassAttribute()
    {
        return match ($this->status) {
            'Pendente' => 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200',
            'Em Análise' => 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200',
            'Aprovada' => 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200',
            'Rejeitada' => 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200',
            'Contratada' => 'bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200',
            default => 'bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200',
        };
    }

    /**
     * Obter o nome do arquivo do currículo
     */
    public function getResumeDisplayNameAttribute()
    {
        return $this->resume_filename ?: 'Currículo.pdf';
    }

    /**
     * Usuário que mudou o status
     */
    public function statusChanger()
    {
        return $this->belongsTo(User::class, 'status_changed_by');
    }

    /**
     * Scope para filtrar por nível de prioridade
     */
    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority_level', $priority);
    }

    /**
     * Scope para candidaturas de alta prioridade
     */
    public function scopeHighPriority($query)
    {
        return $query->whereIn('priority_level', ['alta', 'urgente']);
    }

    /**
     * Atualizar status com histórico
     */
    public function updateStatus($newStatus, $userId = null, $notes = null)
    {
        $oldStatus = $this->status;
        $userId = $userId ?: auth()->id();

        // Atualizar histórico de status
        $history = $this->status_history ?: [];
        $history[] = [
            'from' => $oldStatus,
            'to' => $newStatus,
            'changed_by' => $userId,
            'changed_at' => now()->toISOString(),
            'notes' => $notes,
        ];

        $this->update([
            'status' => $newStatus,
            'status_history' => $history,
            'last_status_change' => now(),
            'status_changed_by' => $userId,
        ]);

        // Log da mudança
        \Log::info('Status de candidatura alterado', [
            'application_id' => $this->id,
            'from_status' => $oldStatus,
            'to_status' => $newStatus,
            'changed_by' => $userId,
            'notes' => $notes,
        ]);
    }

    /**
     * Registrar envio de email
     */
    public function logEmailSent($type, $recipient, $success = true, $error = null)
    {
        $log = $this->email_log ?: [];
        $log[] = [
            'type' => $type,
            'recipient' => $recipient,
            'sent_at' => now()->toISOString(),
            'success' => $success,
            'error' => $error,
        ];

        $this->update([
            'email_log' => $log,
            'email_sent_at' => $success ? now() : $this->email_sent_at,
        ]);
    }

    /**
     * Obter classe CSS do nível de prioridade
     */
    public function getPriorityClassAttribute()
    {
        return match ($this->priority_level) {
            'baixa' => 'bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200',
            'normal' => 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200',
            'alta' => 'bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200',
            'urgente' => 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200',
            default => 'bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200',
        };
    }

    /**
     * Obter label do nível de prioridade
     */
    public function getPriorityLabelAttribute()
    {
        return match ($this->priority_level) {
            'baixa' => 'Baixa',
            'normal' => 'Normal',
            'alta' => 'Alta',
            'urgente' => 'Urgente',
            default => 'Normal',
        };
    }

    /**
     * Verificar se é candidatura de alta prioridade
     */
    public function isHighPriority()
    {
        return in_array($this->priority_level, ['alta', 'urgente']);
    }

    /**
     * Obter último status do histórico
     */
    public function getLastStatusChangeAttribute()
    {
        $history = $this->status_history ?: [];
        return end($history) ?: null;
    }

    /**
     * Verificar se tem currículo anexado
     */
    public function hasResume()
    {
        return !empty($this->resume_path);
    }

    /**
     * Verificar se tem carta de apresentação
     */
    public function hasCoverLetter()
    {
        return !empty($this->cover_letter);
    }
}
