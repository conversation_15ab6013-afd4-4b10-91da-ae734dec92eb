<?php

namespace App\Livewire\Shop;

use App\Models\Cart;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\ShippingAddress;
use App\Services\VipPurchaseService;
use App\Services\WalletCreditService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Livewire\Component;
use Livewire\Attributes;
use Stripe\Stripe;
use Stripe\Checkout\Session;

class Checkout extends Component
{
    public $cart;
    public $shippingAddress = [];
    public $paymentMethod = 'credit_card';
    public $notes = '';
    public $hasPhysicalProducts = false;
    public $hasDigitalProducts = false;

    // Campos do endereço
    public $address;
    public $city;
    public $state;
    public $zipCode;
    public $country = 'Brasil';
    public $phone;

    // Wallet
    public $walletBalance = 0;

    protected function rules()
    {
        return [
            'paymentMethod' => 'required|in:credit_card,pix,boleto,wallet',
            'notes' => 'nullable|string|max:500',
        ];
    }

    protected $validationAttributes = [
        'address' => 'endereço',
        'city' => 'cidade',
        'state' => 'estado',
        'zipCode' => 'CEP',
        'country' => 'país',
        'phone' => 'telefone',
        'paymentMethod' => 'método de pagamento',
        'notes' => 'observações',
    ];

    public function mount()
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $this->loadCart();

        if ($this->cart->items->isEmpty()) {
            session()->flash('info', 'Seu carrinho está vazio.');
            return redirect()->route('shop.cart');
        }

        // Carregar endereço salvo, se existir
        $savedAddress = ShippingAddress::where('user_id', Auth::id())->latest()->first();

        if ($savedAddress) {
            $this->address = $savedAddress->address;
            $this->city = $savedAddress->city;
            $this->state = $savedAddress->state;
            $this->zipCode = $savedAddress->zip_code;
            $this->country = $savedAddress->country;
            $this->phone = $savedAddress->phone;
        }

        $this->refreshWalletBalance();
    }

    protected function loadCart()
    {
        $this->cart = Cart::where('user_id', Auth::id())->with('items.product')->first();
        if (!$this->cart) {
            $this->cart = new Cart(); // Avoid errors on empty cart
        }
        $this->hasPhysicalProducts = $this->cart->items->contains(fn($item) => !$item->product->is_digital);
        $this->hasDigitalProducts = $this->cart->items->contains(fn($item) => $item->product->is_digital);
    }

    public function placeOrder()
    {
        logger()->info('Iniciando processamento do pedido', [
            'user_id' => Auth::id(),
            'payment_method' => $this->paymentMethod,
            'cart_total' => $this->cart ? $this->cart->getTotalWithDiscount() : 0,
        ]);

        $this->validate();

        // Para pagamentos com carteira, a lógica é imediata e transacional.
        if ($this->paymentMethod === 'wallet') {
            return $this->processWalletPayment();
        }

        // Para outros métodos (Cartão, PIX, Boleto), criamos o pedido como pendente primeiro.
        $order = $this->createPendingOrder();

        if (!$order) {
            $this->dispatch('notify', ['message' => 'Erro ao criar seu pedido. Tente novamente.', 'type' => 'error']);
            return;
        }

        // Se for Cartão, redireciona para o Stripe Checkout
        if ($this->paymentMethod === 'credit_card') {
            return $this->redirectToStripeCheckout($order);
        }

        // Para PIX e Boleto, redireciona para a página de detalhes do pedido
        // onde as instruções de pagamento serão exibidas.
        return redirect()->route('shop.order.show', $order->id);
    }

    private function createPendingOrder()
    {
        try {
            return DB::transaction(function () {
                $order = Order::create([
                    'user_id' => Auth::id(),
                    'total' => $this->cart->total,
                    'discount' => $this->cart->discount,
                    'status' => Order::STATUS_PENDING,
                    'payment_method' => $this->paymentMethod,
                    'shipping_address' => ['pickup' => true, 'message' => 'Produto para retirada no local'],
                    'notes' => $this->notes,
                    'coupon_id' => $this->cart->coupon_id,
                ]);

                foreach ($this->cart->items as $item) {
                    OrderItem::create([
                        'order_id' => $order->id,
                        'product_id' => $item->product_id,
                        'quantity' => $item->quantity,
                        'price' => $item->price,
                        'options' => $item->options,
                    ]);
                }
                return $order;
            });
        } catch (\Exception $e) {
            logger()->error('Falha ao criar pedido pendente', ['error' => $e->getMessage()]);
            return null;
        }
    }

    private function processWalletPayment()
    {
        $total = $this->cart->getTotalWithDiscount();
        if ($this->walletBalance < $total) {
            $this->dispatch('notify', ['message' => 'Saldo insuficiente na carteira.', 'type' => 'error']);
            return;
        }

        $order = null;
        try {
            DB::transaction(function () use ($total, &$order) {
                $order = $this->createPendingOrder();
                if (!$order) throw new \Exception("Falha ao criar o pedido base.");

                $user = Auth::user();
                $wallet = $user->wallet()->lockForUpdate()->first();

                if (!$wallet || $wallet->balance < $total) {
                    throw new \Exception('Saldo insuficiente na carteira.');
                }

                $transaction = $wallet->subtractFunds($total, 'purchase', 'Compra Pedido #' . $order->id, $order->id, Order::class);

                $order->markAsPaid('wallet', 'WALLET-' . $transaction->id);

                // Process VIP purchase if order contains VIP products
                $vipPurchaseService = app(VipPurchaseService::class);
                if ($vipPurchaseService->orderContainsVipProducts($order)) {
                    $vipPurchaseService->processVipPurchase($order);
                }

                // Process wallet credit if order contains wallet credit products
                $walletCreditService = app(WalletCreditService::class);
                if ($walletCreditService->orderContainsWalletCreditProducts($order)) {
                    $walletCreditService->processWalletCredit($order);
                }

                // Limpar o carrinho
                $this->cart->clear();
            });

            return redirect()->route('shop.order.success', $order->id);
        } catch (\Exception $e) {
            logger()->error('Erro ao processar pagamento com carteira', ['error' => $e->getMessage()]);
            $this->dispatch('notify', ['message' => 'Erro ao processar pagamento com carteira: ' . $e->getMessage(), 'type' => 'error']);
            return;
        }
    }

    private function redirectToStripeCheckout(Order $order)
    {
        try {
            Stripe::setApiKey(config('cashier.secret'));

            $line_items = [];
            foreach ($order->items as $item) {
                $line_items[] = [
                    'price_data' => [
                        'currency' => 'brl',
                        'product_data' => [
                            'name' => $item->product->name,
                            'images' => [$item->product->getImageUrl() ?? 'https://placehold.co/200'],
                        ],
                        'unit_amount' => (int)($item->price * 100),
                    ],
                    'quantity' => $item->quantity,
                ];
            }

            $session_params = [
                'payment_method_types' => ['card'],
                'line_items' => $line_items,
                'mode' => 'payment',
                'success_url' => route('shop.checkout.success', ['order' => $order->id]) . '?session_id={CHECKOUT_SESSION_ID}',
                'cancel_url' => route('shop.checkout.cancel', ['order' => $order->id]),
                'customer_email' => Auth::user()->email,
                'metadata' => ['order_id' => $order->id],
            ];

            if ($order->discount > 0) {
                $coupon = \Stripe\Coupon::create([
                    'amount_off' => (int)($order->discount * 100),
                    'currency' => 'brl',
                    'duration' => 'once',
                    'name' => 'Desconto Pedido #' . $order->id
                ]);
                $session_params['discounts'] = [['coupon' => $coupon->id]];
            }

            $session = Session::create($session_params);
            $order->update(['payment_id' => $session->id]);

            // Redirecionar para a página de pagamento do Stripe (sem o navigate: true)
            return $this->redirect($session->url);
        } catch (\Exception $e) {
            logger()->error('Erro ao criar sessão Stripe para o pedido: ' . $order->id, ['error' => $e->getMessage()]);
            $order->update(['status' => Order::STATUS_FAILED]);
            $this->dispatch('notify', ['message' => 'Não foi possível iniciar o pagamento. Tente novamente.', 'type' => 'error']);
            return null;
        }
    }

    public function updatedPaymentMethod($value)
    {
        $this->dispatch('paymentMethodChanged', $value);
    }

    public function refreshWalletBalance()
    {
        if (Auth::check()) {
            $this->walletBalance = Auth::user()->wallet?->balance ?? 0;
        }
    }

    public function render()
    {
        return view('livewire.shop.checkout');
    }
}
