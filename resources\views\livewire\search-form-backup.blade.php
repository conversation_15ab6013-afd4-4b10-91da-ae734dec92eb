<?php
use Illuminate\Support\Facades\Storage;
?>

<div>
    @if($errors->has('global'))
        <div class="mb-4 p-3 rounded bg-red-100 text-red-700 border border-red-400">
            {{ $errors->first('global') }}
        </div>
    @endif
     <flux:heading>Busca Avançada</flux:heading>
     <flux:subheading>Encontre usuários, posts e eventos na plataforma.</flux:subheading>
    
    <section id="searchform" class="p-6 border border-neutral-200 dark:border-neutral-700 shadow-md rounded-lg">
        <form wire:submit.prevent="search" class="space-y-6">
            
            {{-- Tipo de busca e campo principal --}}
            <div class="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 p-6 rounded-xl border border-purple-200 dark:border-purple-700">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div class="md:col-span-1">
                        <flux:field>
                            <flux:label>Tipo de Busca</flux:label>
                            <flux:select wire:model.live="searchType">
                                <flux:option value="users">👥 Usuários</flux:option>
                                <flux:option value="posts">📝 Posts</flux:option>
                                <flux:option value="events">🎉 Eventos</flux:option>
                            </flux:select>
                        </flux:field>
                    </div>
                    <div class="md:col-span-2">
                        <flux:field>
                            <flux:label>
                                @if(($searchType ?? 'users') === 'users')
                                    Buscar usuários por nome, username ou bio
                                @elseif(($searchType ?? 'users') === 'posts')
                                    Buscar posts por conteúdo
                                @else
                                    Buscar eventos por nome, descrição ou localização
                                @endif
                            </flux:label>
                            <flux:input 
                                wire:model="searchQuery" 
                                placeholder="Digite sua busca..."
                                icon="magnifying-glass"
                                value="{{ $searchQuery ?? '' }}"
                            />
                        </flux:field>
                    </div>
                </div>
            </div>

            {{-- Filtros específicos para usuários --}}
            @if(($searchType ?? 'users') === 'users')
            <div class="grid grid-cols-4 gap-4">
                <flux:field>
                    <flux:label for="id">ID</flux:label>
                    <flux:input id="id" wire:model="filters.id" />
                    <flux:error name="filters.id" />
                </flux:field>

                <flux:field>
                    <flux:label for="username">Username</flux:label>
                    <flux:input id="username" wire:model="filters.username" />
                    <flux:error name="filters.username" />
                </flux:field>

                <flux:field>
                    <flux:label for="anuncio">Anúncio</flux:label>
                    <flux:input id="anuncio" wire:model="filters.anuncio" />
                    <flux:error name="filters.anuncio" />
                </flux:field>
            </div>

            {{-- Filtros específicos para usuários --}}
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 bg-white dark:bg-zinc-800 p-6 rounded-xl shadow-inner">
                {{-- Coluna 1 --}}
                <div class="space-y-4">
                    <flux:radio.group label="Perfil com Foto?" wire:model="filters.foto">
                        <flux:radio value="1" label="Sim" checked />
                        <flux:radio value="0" label="Não" />
                    </flux:radio.group>

                    <label class="block text-sm font-medium text-gray-700 dark:text-white">Sexo:</label>
                    <div class="space-y-2">
                        <flux:checkbox wire:model="filters.sexo" value="casal" label="Casal" />
                        <flux:checkbox wire:model="filters.sexo" value="homem" label="Homem" />
                        <flux:checkbox wire:model="filters.sexo" value="mulher" label="Mulher" />
                    </div>
                </div>

                {{-- Coluna 2 --}}
                <div class="space-y-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-white">Ordenar por:</label>
                    <flux:radio.group wire:model="filters.ordenar">
                        <flux:radio value="id_crescente" label="Mais Antigos" />
                        <flux:radio value="id_decrescente" label="Mais Recentes" />
                        <flux:radio value="last_access" label="Último Acesso" checked />
                    </flux:radio.group>

                    <label class="block text-sm font-medium text-gray-700 dark:text-white">Cadastrados há:</label>
                    <flux:radio.group wire:model="filters.cadastrados">
                        <flux:radio value="7_dias" label="7 Dias" checked />
                        <flux:radio value="15_dias" label="15 Dias" />
                        <flux:radio value="30_dias" label="30 Dias" />
                        <flux:radio value="all" label="Todos" />
                    </flux:radio.group>
                </div>
            </div>
            @endif

            {{-- Botão de busca --}}
            <div class="flex justify-center items-center gap-3 pt-4">
                <div wire:loading wire:target="search" class="text-sm text-gray-500 flex items-center">
                    <flux:icon name="arrow-path" class="w-4 h-4 mr-2 animate-spin" />
                    Buscando...
                </div>
                <flux:button type="submit" variant="primary" size="lg" icon="magnifying-glass">
                    @if(($searchType ?? 'users') === 'users')
                        Buscar Usuários
                    @elseif(($searchType ?? 'users') === 'posts')
                        Buscar Posts
                    @else
                        Buscar Eventos
                    @endif
                </flux:button>
            </div>
        </form>
    </section>

    {{-- Resultados --}}
    @if($hasSearched ?? false)
        <section id="results" class="p-6 border border-neutral-200 dark:border-neutral-700 shadow-md rounded-lg mt-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-medium text-gray-300">
                    @if(($searchType ?? 'users') === 'users')
                        👥 Usuários Encontrados
                    @elseif(($searchType ?? 'users') === 'posts')
                        📝 Posts Encontrados
                    @else
                        🎉 Eventos Encontrados
                    @endif
                </h2>
                <p class="text-sm text-gray-500">
                    {{ count($results ?? []) }}
                    @if(($searchType ?? 'users') === 'users')
                        {{ count($results ?? []) == 1 ? 'usuário encontrado' : 'usuários encontrados' }}
                    @elseif(($searchType ?? 'users') === 'posts')
                        {{ count($results ?? []) == 1 ? 'post encontrado' : 'posts encontrados' }}
                    @else
                        {{ count($results ?? []) == 1 ? 'evento encontrado' : 'eventos encontrados' }}
                    @endif
                </p>
            </div>

            @if(($searchType ?? 'users') === 'users')
                {{-- Resultados de usuários --}}
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    @forelse($results ?? [] as $result)
                    <div class="pb-6 border border-neutral-200 dark:border-neutral-700 relative rounded-lg shadow-md">
                        <div class="p-4">
                            <h3 class="text-lg font-semibold">{{ $result->name }}</h3>
                            <p class="text-gray-500">@{{ $result->username }}</p>
                        </div>
                    </div>
                    @empty
                        <div class="col-span-full text-center py-12">
                            <flux:icon name="user-group" class="w-16 h-16 mx-auto text-gray-300 mb-4" />
                            <p class="text-lg text-gray-300 mb-2">Nenhum usuário encontrado.</p>
                            <p class="text-sm text-gray-300">Tente ajustar seus critérios de busca.</p>
                        </div>
                    @endforelse
                </div>
            @endif
        </section>
    @endif
</div>
