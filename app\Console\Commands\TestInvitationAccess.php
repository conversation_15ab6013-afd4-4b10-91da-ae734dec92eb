<?php

namespace App\Console\Commands;

use App\Models\Group;
use App\Models\GroupInvitation;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class TestInvitationAccess extends Command
{
    protected $signature = 'test:invitation-access';
    protected $description = 'Testa o acesso ao convite simulando uma requisição GET';

    public function handle()
    {
        $this->info('Testando acesso ao convite...');

        // Criar usuários de teste
        $inviter = User::create([
            'name' => 'Usuário Convidador',
            'username' => 'convidador_' . now()->timestamp,
            'email' => 'convidador' . now()->timestamp . '@exemplo.com',
            'password' => bcrypt('password'),
            'role' => 'vip',
            'active' => true,
        ]);

        $invited = User::create([
            'name' => 'Usuário Convidado',
            'username' => 'convidado_' . now()->timestamp,
            'email' => 'convidado' . now()->timestamp . '@exemplo.com',
            'password' => bcrypt('password'),
            'role' => 'vip',
            'active' => true,
        ]);

        // Criar um grupo
        $group = Group::create([
            'name' => 'Grupo Teste Acesso ' . now()->format('H:i:s'),
            'slug' => Str::slug('Grupo Teste Acesso ' . now()->format('H:i:s')),
            'description' => 'Grupo para testar acesso',
            'privacy' => 'private',
            'creator_id' => $inviter->id,
            'posts_require_approval' => false,
        ]);

        // O criador é automaticamente adicionado como admin pelo evento 'created' do modelo Group

        // Criar um convite
        $invitation = GroupInvitation::create([
            'group_id' => $group->id,
            'user_id' => $invited->id,
            'invited_by' => $inviter->id,
            'status' => 'pending',
        ]);

        $this->info("Convite criado: ID {$invitation->id}");
        $this->info("Usuário convidado: {$invited->name} (ID: {$invited->id})");

        // Simular login do usuário convidado
        Auth::login($invited);
        $this->info("Usuário logado: " . Auth::user()->name);

        // Tentar aceitar o convite
        try {
            $controller = new \App\Http\Controllers\GroupInvitationController();
            $result = $controller->accept($invitation);

            $this->info("✓ Convite aceito com sucesso!");

            // Verificar se o usuário foi adicionado ao grupo
            $isMember = $group->fresh()->members()->where('user_id', $invited->id)->exists();
            if ($isMember) {
                $this->info("✓ Usuário foi adicionado ao grupo como membro");
            } else {
                $this->error("✗ Usuário não foi adicionado ao grupo");
            }

            // Verificar status do convite
            $invitation->refresh();
            $this->info("Status do convite: " . $invitation->status);
        } catch (\Exception $e) {
            $this->error("✗ Erro ao aceitar convite: " . $e->getMessage());
            $this->error("Trace: " . $e->getTraceAsString());
        }

        // Logout
        Auth::logout();

        $this->info("\nTeste concluído!");
        return 0;
    }
}
