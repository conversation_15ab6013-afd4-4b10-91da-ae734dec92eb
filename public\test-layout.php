<?php
/**
 * Script de teste do layout
 * Execute acessando: https://www.swingcuritiba.com.br/test-layout.php
 * 
 * IMPORTANTE: Delete este arquivo após usar!
 */

// Verificar se está no ambiente correto
if (!file_exists('../artisan')) {
    die('❌ Erro: Execute no diretório public do Laravel');
}

// Mudar para o diretório raiz do Laravel
chdir('..');

// Verificar se é o ambiente de produção
if (!str_contains($_SERVER['HTTP_HOST'] ?? '', 'swingcuritiba.com.br')) {
    die('❌ Este script só pode ser executado na produção');
}

?>
<!DOCTYPE html>
<html lang="pt-BR" class="dark">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste de Layout - Desiree Swing Club</title>
    
    <!-- Carregar CSS compilado -->
    <?php
    $manifestFile = 'public/build/manifest.json';
    if (file_exists($manifestFile)) {
        $manifest = json_decode(file_get_contents($manifestFile), true);
        $cssFile = $manifest['resources/css/app.css']['file'] ?? null;
        if ($cssFile) {
            echo '<link rel="stylesheet" href="/build/' . $cssFile . '">';
        }
    }
    ?>
    
    <style>
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #374151;
            border-radius: 8px;
            background: #1f2937;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        .test-card {
            padding: 1rem;
            background: #374151;
            border-radius: 0.5rem;
            color: white;
        }
    </style>
</head>
<body class="min-h-screen bg-zinc-800 text-white">
    <div class="container mx-auto p-6">
        <h1 class="text-3xl font-bold text-center mb-8 text-neon-cyan">🧪 Teste de Layout</h1>
        
        <!-- Teste 1: Grid Responsivo -->
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-4 text-neon-pink">📱 Grid Responsivo</h2>
            <div class="test-grid">
                <div class="test-card">Card 1</div>
                <div class="test-card">Card 2</div>
                <div class="test-card">Card 3</div>
                <div class="test-card">Card 4</div>
            </div>
        </div>
        
        <!-- Teste 2: Cores Neon -->
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-4 text-neon-yellow">🌈 Cores Neon</h2>
            <div class="flex flex-wrap gap-4">
                <div class="px-4 py-2 bg-neon-pink text-black rounded">Neon Pink</div>
                <div class="px-4 py-2 bg-neon-cyan text-black rounded">Neon Cyan</div>
                <div class="px-4 py-2 bg-neon-yellow text-black rounded">Neon Yellow</div>
            </div>
        </div>
        
        <!-- Teste 3: Sidebar Simulada -->
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-4 text-neon-cyan">📋 Sidebar Simulada</h2>
            <div class="flex gap-4">
                <div class="w-64 bg-zinc-900 p-4 rounded">
                    <h3 class="font-semibold mb-4">Menu</h3>
                    <ul class="space-y-2">
                        <li class="p-2 bg-zinc-800 rounded">Principal</li>
                        <li class="p-2 bg-zinc-800 rounded">Busca</li>
                        <li class="p-2 bg-zinc-800 rounded">Radar</li>
                        <li class="p-2 bg-zinc-800 rounded">Mensagens</li>
                    </ul>
                </div>
                <div class="flex-1 bg-zinc-700 p-4 rounded">
                    <h3 class="font-semibold mb-4">Conteúdo Principal</h3>
                    <p>Este é o conteúdo principal da página.</p>
                </div>
            </div>
        </div>
        
        <!-- Teste 4: Mobile Responsivo -->
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-4 text-neon-pink">📱 Teste Mobile</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div class="test-card">Mobile: 1 coluna</div>
                <div class="test-card">Tablet: 2 colunas</div>
                <div class="test-card">Desktop: 3 colunas</div>
            </div>
        </div>
        
        <!-- Teste 5: Scrollbar -->
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-4 text-neon-yellow">📜 Teste de Scrollbar</h2>
            <div class="h-32 overflow-y-auto bg-zinc-700 p-4 rounded">
                <p>Linha 1 - Teste de scroll</p>
                <p>Linha 2 - Teste de scroll</p>
                <p>Linha 3 - Teste de scroll</p>
                <p>Linha 4 - Teste de scroll</p>
                <p>Linha 5 - Teste de scroll</p>
                <p>Linha 6 - Teste de scroll</p>
                <p>Linha 7 - Teste de scroll</p>
                <p>Linha 8 - Teste de scroll</p>
                <p>Linha 9 - Teste de scroll</p>
                <p>Linha 10 - Teste de scroll</p>
            </div>
        </div>
        
        <!-- Informações do Sistema -->
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-4 text-neon-cyan">ℹ️ Informações do Sistema</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                    <strong>CSS Carregado:</strong><br>
                    <?php
                    if (isset($cssFile)) {
                        echo "✅ /build/{$cssFile}";
                    } else {
                        echo "❌ CSS não encontrado";
                    }
                    ?>
                </div>
                <div>
                    <strong>Manifest:</strong><br>
                    <?php echo file_exists($manifestFile) ? "✅ Existe" : "❌ Não existe"; ?>
                </div>
                <div>
                    <strong>Tailwind Config:</strong><br>
                    <?php echo file_exists('tailwind.config.js') ? "✅ Existe" : "❌ Não existe"; ?>
                </div>
                <div>
                    <strong>Timestamp:</strong><br>
                    <?php echo date('Y-m-d H:i:s'); ?>
                </div>
            </div>
        </div>
        
        <!-- Instruções -->
        <div class="test-section">
            <h2 class="text-xl font-semibold mb-4 text-neon-yellow">📋 Instruções</h2>
            <ol class="list-decimal list-inside space-y-2">
                <li>Verifique se as cores neon estão funcionando</li>
                <li>Teste a responsividade redimensionando a janela</li>
                <li>Verifique se a scrollbar customizada aparece</li>
                <li>Teste no mobile/tablet</li>
                <li>Abra o console (F12) para verificar erros</li>
                <li><strong>Delete este arquivo após o teste!</strong></li>
            </ol>
        </div>
    </div>
    
    <!-- JavaScript para testes -->
    <script>
        console.log('🧪 Teste de Layout carregado');
        console.log('📱 Largura da tela:', window.innerWidth);
        console.log('📏 Altura da tela:', window.innerHeight);
        
        // Teste de responsividade
        window.addEventListener('resize', function() {
            console.log('📱 Nova largura:', window.innerWidth);
        });
        
        // Verificar se Tailwind está funcionando
        const testElement = document.createElement('div');
        testElement.className = 'bg-red-500';
        document.body.appendChild(testElement);
        const computedStyle = window.getComputedStyle(testElement);
        const bgColor = computedStyle.backgroundColor;
        document.body.removeChild(testElement);
        
        if (bgColor === 'rgb(239, 68, 68)' || bgColor === 'rgb(239,68,68)') {
            console.log('✅ Tailwind CSS está funcionando');
        } else {
            console.log('❌ Tailwind CSS pode não estar funcionando. Cor detectada:', bgColor);
        }
    </script>
</body>
</html>
