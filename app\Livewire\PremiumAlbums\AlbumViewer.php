<?php

namespace App\Livewire\PremiumAlbums;

use App\Models\Album;
use App\Models\AlbumReview;
use App\Services\AlbumPurchaseService;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Auth;

class AlbumViewer extends Component
{
    use WithPagination;

    public Album $album;
    public $showPurchaseModal = false;
    public $paymentMethod = 'wallet';
    public $isProcessing = false;
    
    // Review form
    public $showReviewModal = false;
    public $reviewRating = 5;
    public $reviewComment = '';

    protected $rules = [
        'reviewRating' => 'required|integer|min:1|max:5',
        'reviewComment' => 'required|string|min:10|max:500',
    ];

    public function mount(Album $album)
    {
        $this->album = $album;
    }

    public function openPurchaseModal()
    {
        if (!Auth::check()) {
            session()->flash('error', 'Faça login para comprar álbuns.');
            return redirect()->route('login');
        }

        if (Auth::user()->role === 'visitante') {
            session()->flash('error', 'Apenas usuários VIP podem comprar álbuns premium.');
            return;
        }

        $this->showPurchaseModal = true;
    }

    public function closePurchaseModal()
    {
        $this->showPurchaseModal = false;
        $this->paymentMethod = 'wallet';
        $this->isProcessing = false;
    }

    public function purchaseAlbum()
    {
        if (!Auth::check()) {
            return;
        }

        $this->isProcessing = true;

        try {
            $purchaseService = new AlbumPurchaseService();
            $result = $purchaseService->purchaseAlbum($this->album, Auth::user(), $this->paymentMethod);

            if ($result['success']) {
                session()->flash('success', $result['message']);
                $this->closePurchaseModal();
                $this->album = $this->album->fresh();
                return;
            }

            // Se requer Stripe
            if (isset($result['requires_stripe'])) {
                $this->dispatch('create-stripe-session', $result['stripe_data']);
                return;
            }

            session()->flash('error', $result['message']);

        } catch (\Exception $e) {
            session()->flash('error', $e->getMessage());
        } finally {
            $this->isProcessing = false;
        }
    }

    public function openReviewModal()
    {
        if (!Auth::check()) {
            session()->flash('error', 'Faça login para avaliar álbuns.');
            return;
        }

        if (!$this->album->isPurchasedBy(Auth::user())) {
            session()->flash('error', 'Apenas quem comprou pode avaliar o álbum.');
            return;
        }

        // Verificar se já avaliou
        $existingReview = AlbumReview::where('album_id', $this->album->id)
                                   ->where('user_id', Auth::id())
                                   ->first();

        if ($existingReview) {
            $this->reviewRating = $existingReview->rating;
            $this->reviewComment = $existingReview->comment;
        }

        $this->showReviewModal = true;
    }

    public function closeReviewModal()
    {
        $this->showReviewModal = false;
        $this->reviewRating = 5;
        $this->reviewComment = '';
        $this->resetValidation();
    }

    public function submitReview()
    {
        $this->validate();

        try {
            AlbumReview::updateOrCreate(
                [
                    'album_id' => $this->album->id,
                    'user_id' => Auth::id(),
                ],
                [
                    'rating' => $this->reviewRating,
                    'comment' => $this->reviewComment,
                    'is_verified_purchase' => true,
                ]
            );

            session()->flash('success', 'Avaliação enviada com sucesso!');
            $this->closeReviewModal();
            $this->album = $this->album->fresh();

        } catch (\Exception $e) {
            session()->flash('error', 'Erro ao enviar avaliação.');
        }
    }

    public function getCanAccessProperty()
    {
        return $this->album->canUserAccess(Auth::user());
    }

    public function getIsPurchasedProperty()
    {
        return Auth::check() && $this->album->isPurchasedBy(Auth::user());
    }

    public function getMediasProperty()
    {
        if ($this->canAccess) {
            return $this->album->medias;
        } else {
            return $this->album->getPreviewMedias();
        }
    }

    public function getReviewsProperty()
    {
        return $this->album->reviews()
                          ->with('user')
                          ->orderBy('created_at', 'desc')
                          ->paginate(10);
    }

    public function getUserWalletBalanceProperty()
    {
        if (!Auth::check()) {
            return 0;
        }

        return Auth::user()->wallet?->balance ?? 0;
    }

    public function getCanReviewProperty()
    {
        if (!Auth::check()) {
            return false;
        }

        return $this->isPurchased && !AlbumReview::where('album_id', $this->album->id)
                                                ->where('user_id', Auth::id())
                                                ->exists();
    }

    public function render()
    {
        return view('livewire.premium-albums.album-viewer', [
            'canAccess' => $this->canAccess,
            'isPurchased' => $this->isPurchased,
            'medias' => $this->medias,
            'reviews' => $this->reviews,
            'userWalletBalance' => $this->userWalletBalance,
            'canReview' => $this->canReview,
        ]);
    }
}
