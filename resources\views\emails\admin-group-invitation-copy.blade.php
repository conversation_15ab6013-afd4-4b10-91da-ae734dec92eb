<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>[ADMIN COPY] Convite para Grupo - {{ $group->name }}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8fafc;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            background: linear-gradient(135deg, #7c3aed, #1f2937);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: bold;
        }
        .admin-badge {
            background-color: rgba(255, 255, 255, 0.2);
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            margin-top: 10px;
            display: inline-block;
        }
        .content {
            padding: 30px 20px;
        }
        .info-section {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #7c3aed;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e9ecef;
        }
        .info-row:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
        }
        .info-label {
            font-weight: bold;
            color: #666;
            min-width: 120px;
        }
        .info-value {
            color: #333;
            flex: 1;
        }
        .group-details {
            background-color: #e8f4fd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #3b82f6;
        }
        .action-buttons {
            text-align: center;
            margin: 30px 0;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            margin: 0 5px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: bold;
            font-size: 14px;
            color: white;
        }
        .btn-primary {
            background-color: #7c3aed;
        }
        .btn-secondary {
            background-color: #6b7280;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            font-size: 14px;
            color: #666;
            border-top: 1px solid #e9ecef;
        }
        .footer a {
            color: #7c3aed;
            text-decoration: none;
        }
        .timestamp {
            font-size: 12px;
            color: #888;
            text-align: right;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📧 Cópia Administrativa</h1>
            <div class="admin-badge">CONVITE PARA GRUPO</div>
        </div>

        <div class="content">
            <p>Olá <strong>{{ $adminRecipient->name }}</strong>,</p>
            
            <p>Esta é uma cópia administrativa de um convite para grupo enviado na plataforma.</p>

            <div class="info-section">
                <h3 style="margin-top: 0; color: #7c3aed;">📋 Detalhes do Convite</h3>
                
                <div class="info-row">
                    <span class="info-label">Quem convidou:</span>
                    <span class="info-value">{{ $inviter->name }} ({{ $inviter->email }})</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">Usuário convidado:</span>
                    <span class="info-value">{{ $invitedUser->name }} ({{ $invitedUser->email }})</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">Data do convite:</span>
                    <span class="info-value">{{ $invitation->created_at->format('d/m/Y H:i:s') }}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">Status:</span>
                    <span class="info-value">
                        @if($invitation->status === 'pending')
                            🟡 Pendente
                        @elseif($invitation->status === 'accepted')
                            🟢 Aceito
                        @elseif($invitation->status === 'declined')
                            🔴 Recusado
                        @endif
                    </span>
                </div>
            </div>

            <div class="group-details">
                <h3 style="margin-top: 0; color: #3b82f6;">👥 Informações do Grupo</h3>
                
                <div class="info-row">
                    <span class="info-label">Nome:</span>
                    <span class="info-value">{{ $group->name }}</span>
                </div>
                
                @if($group->description)
                <div class="info-row">
                    <span class="info-label">Descrição:</span>
                    <span class="info-value">{{ $group->description }}</span>
                </div>
                @endif
                
                <div class="info-row">
                    <span class="info-label">Privacidade:</span>
                    <span class="info-value">
                        @if($group->privacy === 'public')
                            🌐 Público
                        @elseif($group->privacy === 'private')
                            🔒 Privado
                        @else
                            🔐 Secreto
                        @endif
                    </span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">Membros:</span>
                    <span class="info-value">{{ $group->members_count }} {{ $group->members_count == 1 ? 'membro' : 'membros' }}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">Criador:</span>
                    <span class="info-value">{{ $group->creator->name }}</span>
                </div>
            </div>

            <div class="action-buttons">
                <a href="{{ route('grupos.show', $group->slug) }}" class="btn btn-primary">
                    👁️ Ver Grupo
                </a>
                <a href="{{ route('user.profile', $inviter->username) }}" class="btn btn-secondary">
                    👤 Ver Perfil do Convidador
                </a>
                <a href="{{ route('user.profile', $invitedUser->username) }}" class="btn btn-secondary">
                    👤 Ver Perfil do Convidado
                </a>
            </div>

            <div class="timestamp">
                Email gerado automaticamente em {{ now()->format('d/m/Y H:i:s') }}
            </div>
        </div>

        <div class="footer">
            <p><strong>{{ config('app.name') }}</strong> - Sistema de Administração</p>
            <p>
                <a href="{{ config('app.url') }}">Acessar plataforma</a> | 
                <a href="{{ route('admin.dashboard') }}">Painel administrativo</a>
            </p>
        </div>
    </div>
</body>
</html>
