<?php

namespace App\Services;

use App\Models\Order;
use App\Models\User;
use App\Models\VipSubscription;
use App\Notifications\VipPurchaseNotification;
use App\Services\VipSubscriptionService;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class VipPurchaseService
{
    protected $vipSubscriptionService;

    public function __construct(VipSubscriptionService $vipSubscriptionService)
    {
        $this->vipSubscriptionService = $vipSubscriptionService;
    }

    /**
     * Process VIP purchase from an order
     */
    public function processVipPurchase(Order $order): bool
    {
        try {
            DB::beginTransaction();

            // Get VIP products from the order
            $vipProducts = $this->getVipProductsFromOrder($order);

            if (empty($vipProducts)) {
                Log::info('No VIP products found in order', ['order_id' => $order->id]);
                return true; // Not an error, just no VIP products
            }

            $user = $order->user;
            $totalVipDays = collect($vipProducts)->sum('vip_duration_days');

            Log::info('Processing VIP purchase', [
                'order_id' => $order->id,
                'user_id' => $user->id,
                'total_vip_days' => $totalVipDays,
                'vip_products_count' => count($vipProducts)
            ]);

            // Activate or extend VIP subscription
            $this->activateVipSubscription($user, $totalVipDays, $order, $vipProducts);

            // Send notifications
            $this->sendNotifications($order, $user, $vipProducts);

            DB::commit();

            Log::info('VIP purchase processed successfully', [
                'order_id' => $order->id,
                'user_id' => $user->id,
                'new_role' => $user->fresh()->role
            ]);

            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to process VIP purchase', [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Get VIP products from order
     */
    protected function getVipProductsFromOrder(Order $order): array
    {
        $vipProducts = [];

        foreach ($order->items as $item) {
            $product = $item->product;
            
            if ($product && $product->grantsVipAccess()) {
                $vipProducts[] = [
                    'id' => $product->id,
                    'name' => $product->name,
                    'price' => $item->price,
                    'quantity' => $item->quantity,
                    'vip_duration_days' => $product->getVipDurationDays(),
                    'vip_plan_type' => $product->getVipPlanType(),
                    'vip_benefits' => $product->getVipBenefitsFormatted(),
                    'total_vip_days' => $product->getVipDurationDays() * $item->quantity
                ];
            }
        }

        return $vipProducts;
    }

    /**
     * Activate or extend VIP subscription
     */
    protected function activateVipSubscription(User $user, int $totalVipDays, Order $order, array $vipProducts): void
    {
        // Check if user has an active VIP subscription
        $activeSubscription = VipSubscription::where('user_id', $user->id)
            ->where('status', 'active')
            ->where('expires_at', '>', Carbon::now())
            ->first();

        if ($activeSubscription) {
            // Extend existing subscription
            $newExpiryDate = Carbon::parse($activeSubscription->expires_at)->addDays($totalVipDays);
            
            $activeSubscription->update([
                'expires_at' => $newExpiryDate,
                'plan_days' => $activeSubscription->plan_days + $totalVipDays,
                'amount' => $activeSubscription->amount + $order->total,
            ]);

            Log::info('Extended existing VIP subscription', [
                'subscription_id' => $activeSubscription->id,
                'user_id' => $user->id,
                'added_days' => $totalVipDays,
                'new_expiry' => $newExpiryDate->format('Y-m-d H:i:s')
            ]);

        } else {
            // Create new VIP subscription
            $expiresAt = Carbon::now()->addDays($totalVipDays);
            
            $subscription = VipSubscription::create([
                'user_id' => $user->id,
                'plan_days' => $totalVipDays,
                'amount' => $order->total,
                'status' => 'active',
                'stripe_session_id' => $order->payment_id,
                'stripe_payment_id' => $order->payment_id,
                'activated_at' => Carbon::now(),
                'expires_at' => $expiresAt,
            ]);

            Log::info('Created new VIP subscription', [
                'subscription_id' => $subscription->id,
                'user_id' => $user->id,
                'days' => $totalVipDays,
                'expires_at' => $expiresAt->format('Y-m-d H:i:s')
            ]);
        }

        // Update user role to VIP
        $user->update(['role' => 'vip']);

        Log::info('User role updated to VIP', [
            'user_id' => $user->id,
            'old_role' => $user->getOriginal('role'),
            'new_role' => 'vip'
        ]);
    }

    /**
     * Send notifications about VIP purchase
     */
    protected function sendNotifications(Order $order, User $user, array $vipProducts): void
    {
        try {
            // Send confirmation email to user
            $user->notify(new VipPurchaseNotification($order, $user, $vipProducts, false));

            // Send notification to admins
            $adminUsers = User::where('role', 'admin')->get();
            
            foreach ($adminUsers as $admin) {
                $admin->notify(new VipPurchaseNotification($order, $user, $vipProducts, true));
            }

            // Send copy to contact email
            $contactEmail = '<EMAIL>';
            try {
                \Mail::to($contactEmail)->send(new \App\Mail\VipPurchaseAdminCopy($order, $user, $vipProducts));
            } catch (\Exception $e) {
                Log::warning('Failed to send VIP purchase notification to contact email', [
                    'email' => $contactEmail,
                    'error' => $e->getMessage()
                ]);
            }

            Log::info('VIP purchase notifications sent', [
                'order_id' => $order->id,
                'user_id' => $user->id,
                'admin_count' => $adminUsers->count()
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send VIP purchase notifications', [
                'order_id' => $order->id,
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            // Don't throw exception here as the main process should continue
        }
    }

    /**
     * Check if order contains VIP products
     */
    public function orderContainsVipProducts(Order $order): bool
    {
        foreach ($order->items as $item) {
            if ($item->product && $item->product->grantsVipAccess()) {
                return true;
            }
        }
        return false;
    }
}
