<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class TypingIndicator extends Model
{
    protected $fillable = [
        'conversation_id',
        'user_id',
        'started_at',
        'expires_at',
    ];

    protected $casts = [
        'started_at' => 'datetime',
        'expires_at' => 'datetime',
    ];

    /**
     * Conversa à qual o indicador pertence
     */
    public function conversation(): BelongsTo
    {
        return $this->belongsTo(Conversation::class);
    }

    /**
     * Usuário que está digitando
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Verifica se o indicador ainda é válido
     */
    public function isValid(): bool
    {
        return $this->expires_at->isFuture();
    }

    /**
     * Cria ou atualiza um indicador de digitação
     */
    public static function setTyping(int $conversationId, int $userId, int $durationSeconds = 10): self
    {
        $now = Carbon::now();
        
        return static::updateOrCreate(
            [
                'conversation_id' => $conversationId,
                'user_id' => $userId,
            ],
            [
                'started_at' => $now,
                'expires_at' => $now->addSeconds($durationSeconds),
            ]
        );
    }

    /**
     * Remove indicador de digitação
     */
    public static function stopTyping(int $conversationId, int $userId): bool
    {
        return static::where('conversation_id', $conversationId)
            ->where('user_id', $userId)
            ->delete() > 0;
    }

    /**
     * Obtém usuários que estão digitando em uma conversa
     */
    public static function getTypingUsers(int $conversationId, int $excludeUserId = null): \Illuminate\Database\Eloquent\Collection
    {
        $query = static::where('conversation_id', $conversationId)
            ->where('expires_at', '>', Carbon::now())
            ->with('user');

        if ($excludeUserId) {
            $query->where('user_id', '!=', $excludeUserId);
        }

        return $query->get();
    }

    /**
     * Remove indicadores expirados
     */
    public static function cleanExpired(): int
    {
        return static::where('expires_at', '<=', Carbon::now())->delete();
    }
}
