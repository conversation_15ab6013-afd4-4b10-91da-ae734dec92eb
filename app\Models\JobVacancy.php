<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use Carbon\Carbon;

class JobVacancy extends Model
{
    use HasFactory;

    protected $table = 'job_vacancies';

    protected $fillable = [
        'title',
        'slug',
        'description',
        'requirements',
        'benefits',
        'category_id',
        'contract_type',
        'work_mode',
        'location',
        'salary_min',
        'salary_max',
        'salary_period',
        'salary_negotiable',
        'vacancies',
        'application_deadline',
        'experience_level',
        'requires_resume',
        'requires_cover_letter',
        'is_featured',
        'is_active',
        'views_count',
        'applications_count',
    ];

    protected $casts = [
        'salary_min' => 'decimal:2',
        'salary_max' => 'decimal:2',
        'salary_negotiable' => 'boolean',
        'requires_resume' => 'boolean',
        'requires_cover_letter' => 'boolean',
        'is_featured' => 'boolean',
        'is_active' => 'boolean',
        'vacancies' => 'integer',
        'views_count' => 'integer',
        'applications_count' => 'integer',
        'application_deadline' => 'date',
    ];

    /**
     * Boot do modelo
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($job) {
            if (empty($job->slug)) {
                $job->slug = Str::slug($job->title);
            }
        });
    }

    /**
     * Categoria da vaga
     */
    public function category()
    {
        return $this->belongsTo(JobCategory::class, 'category_id');
    }

    /**
     * Candidaturas para esta vaga
     */
    public function applications()
    {
        return $this->hasMany(JobApplication::class, 'job_id');
    }

    /**
     * Candidaturas pendentes
     */
    public function pendingApplications()
    {
        return $this->hasMany(JobApplication::class, 'job_id')->where('status', 'Pendente');
    }

    /**
     * Candidaturas VIP
     */
    public function vipApplications()
    {
        return $this->hasMany(JobApplication::class, 'job_id')->where('is_vip_priority', true);
    }

    /**
     * Scope para vagas ativas
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope para vagas em destaque
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope para vagas com prazo válido
     */
    public function scopeValidDeadline($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('application_deadline')
                ->orWhere('application_deadline', '>=', Carbon::today());
        });
    }

    /**
     * Scope para filtrar por categoria
     */
    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    /**
     * Scope para filtrar por tipo de contrato
     */
    public function scopeByContractType($query, $contractType)
    {
        return $query->where('contract_type', $contractType);
    }

    /**
     * Scope para filtrar por modo de trabalho
     */
    public function scopeByWorkMode($query, $workMode)
    {
        return $query->where('work_mode', $workMode);
    }

    /**
     * Incrementar contador de visualizações
     */
    public function incrementViews()
    {
        $this->increment('views_count');
    }

    /**
     * Verificar se a vaga ainda está aceitando candidaturas
     */
    public function isAcceptingApplications()
    {
        if (!$this->is_active) {
            return false;
        }

        if ($this->application_deadline && $this->application_deadline < Carbon::today()) {
            return false;
        }

        return true;
    }

    /**
     * Obter o salário formatado
     */
    public function getFormattedSalaryAttribute()
    {
        if ($this->salary_negotiable) {
            return 'A combinar';
        }

        if ($this->salary_min && $this->salary_max) {
            return 'R$ ' . number_format($this->salary_min, 2, ',', '.') . ' - R$ ' . number_format($this->salary_max, 2, ',', '.') . ' ' . $this->salary_period;
        }

        if ($this->salary_min) {
            return 'A partir de R$ ' . number_format($this->salary_min, 2, ',', '.') . ' ' . $this->salary_period;
        }

        return 'A combinar';
    }

    /**
     * Verificar se um usuário já se candidatou
     */
    public function hasUserApplied($userId)
    {
        return $this->applications()->where('user_id', $userId)->exists();
    }

    /**
     * Obter a candidatura de um usuário
     */
    public function getUserApplication($userId)
    {
        return $this->applications()->where('user_id', $userId)->first();
    }
}
