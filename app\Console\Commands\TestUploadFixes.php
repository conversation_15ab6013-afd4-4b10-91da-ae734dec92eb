<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\UserPhoto;
use App\Models\User;
use Illuminate\Support\Facades\Storage;

class TestUploadFixes extends Command
{
    protected $signature = 'test:upload-fixes';
    protected $description = 'Test if upload fixes are working correctly';

    public function handle()
    {
        $this->info('Testing upload fixes...');

        // Test 1: Check UserPhoto model fillable fields
        $this->testUserPhotoFillable();

        // Test 2: Check avatar consistency
        $this->testAvatarConsistency();

        // Test 3: Check storage structure
        $this->testStorageStructure();

        $this->info('All tests completed!');
    }

    private function testUserPhotoFillable()
    {
        $this->info('Testing UserPhoto fillable fields...');

        $userPhoto = new UserPhoto();
        $fillable = $userPhoto->getFillable();

        if (in_array('is_current', $fillable)) {
            $this->info('✓ is_current field is fillable');
        } else {
            $this->error('✗ is_current field is NOT fillable');
        }

        if (in_array('user_id', $fillable)) {
            $this->info('✓ user_id field is fillable');
        } else {
            $this->error('✗ user_id field is NOT fillable');
        }

        if (in_array('photo_path', $fillable)) {
            $this->info('✓ photo_path field is fillable');
        } else {
            $this->error('✗ photo_path field is NOT fillable');
        }
    }

    private function testAvatarConsistency()
    {
        $this->info('Testing avatar consistency...');

        // Check users with photos
        $usersWithPhotos = User::whereHas('userPhotos')->get();
        $inconsistentUsers = 0;

        foreach ($usersWithPhotos as $user) {
            $currentPhotos = $user->userPhotos()->where('is_current', true)->count();
            
            if ($currentPhotos === 0) {
                $this->warn("User {$user->username} has photos but no current avatar");
                $inconsistentUsers++;
            } elseif ($currentPhotos > 1) {
                $this->warn("User {$user->username} has multiple current avatars");
                $inconsistentUsers++;
            }
        }

        if ($inconsistentUsers === 0) {
            $this->info('✓ All users have consistent avatar settings');
        } else {
            $this->error("✗ Found {$inconsistentUsers} users with inconsistent avatar settings");
        }
    }

    private function testStorageStructure()
    {
        $this->info('Testing storage structure...');

        $disk = Storage::disk('public');

        // Check if avatars directory exists
        if ($disk->exists('avatars')) {
            $this->info('✓ Avatars directory exists');
        } else {
            $this->error('✗ Avatars directory does not exist');
        }

        // Check if livewire-tmp directory exists
        if ($disk->exists('livewire-tmp')) {
            $this->info('✓ Livewire temporary directory exists');
        } else {
            $this->warn('! Livewire temporary directory does not exist (will be created on first upload)');
        }

        // Check storage link
        if (is_link(public_path('storage'))) {
            $this->info('✓ Storage link exists');
        } else {
            $this->error('✗ Storage link does not exist - run php artisan storage:link');
        }
    }
}
