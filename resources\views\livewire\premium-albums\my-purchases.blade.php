<div class="max-w-7xl mx-auto p-6">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-300 mb-2">Me<PERSON> Álbu<PERSON></h1>
        <p class="text-gray-400">Sua coleção de álbuns premium adquiridos</p>
        
        <!-- Stats -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
            <div class="bg-zinc-800 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-purple-400">{{ $stats['total_purchases'] }}</div>
                <div class="text-sm text-gray-400">Álbuns Comprados</div>
            </div>
            <div class="bg-zinc-800 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-green-400">R$ {{ number_format($stats['total_spent'], 2, ',', '.') }}</div>
                <div class="text-sm text-gray-400">Total Gasto</div>
            </div>
            <div class="bg-zinc-800 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-cyan-400">{{ $stats['total_files'] }}</div>
                <div class="text-sm text-gray-400">Arquivos Totais</div>
            </div>
            <div class="bg-zinc-800 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-yellow-400">{{ $stats['unique_creators'] }}</div>
                <div class="text-sm text-gray-400">Criadores Únicos</div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg mb-6">
        <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 px-6 py-4">
            <div class="flex flex-col md:flex-row gap-4">
                <div class="flex-1">
                    <flux:input 
                        wire:model.live.debounce.300ms="search" 
                        placeholder="Buscar álbuns, criadores..."
                        class="w-full"
                    >
                        <x-slot name="iconTrailing">
                            <flux:icon name="magnifying-glass" class="w-5 h-5" />
                        </x-slot>
                    </flux:input>
                </div>
                
                <div class="md:w-48">
                    <flux:select wire:model.live="sortBy" class="w-full">
                        <option value="purchased_at">Mais Recentes</option>
                        <option value="album_name">Nome do Álbum</option>
                        <option value="creator_name">Nome do Criador</option>
                        <option value="amount">Valor Pago</option>
                    </flux:select>
                </div>
                
                @if($search)
                <flux:button 
                    wire:click="clearSearch"
                    color="red"
                    variant="outline"
                >
                    Limpar
                </flux:button>
                @endif
            </div>
        </div>
    </div>

    <!-- Albums Grid -->
    @if($purchases->count() > 0)
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
        @foreach($purchases as $purchase)
        <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg overflow-hidden hover:shadow-lg transition-shadow group">
            <a href="{{ route('premium-albums.show', $purchase->album) }}" class="block">
                <!-- Album Cover -->
                <div class="relative h-48 bg-zinc-800">
                    @if($purchase->album->medias->count() > 0)
                        @php $firstMedia = $purchase->album->medias->first(); @endphp
                        @if($firstMedia->type === 'photo')
                            <img 
                                src="{{ route('premium-albums.media', [$purchase->album, $firstMedia->id]) }}" 
                                alt="{{ $purchase->album->name }}"
                                class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                            />
                        @else
                            <div class="w-full h-full flex items-center justify-center">
                                <flux:icon name="film" class="w-12 h-12 text-gray-400" />
                            </div>
                        @endif
                    @else
                        <div class="w-full h-full flex items-center justify-center">
                            <flux:icon name="photo" class="w-12 h-12 text-gray-400" />
                        </div>
                    @endif

                    <!-- Owned Badge -->
                    <div class="absolute top-2 left-2">
                        <span class="bg-green-600 text-white px-2 py-1 text-xs font-bold rounded-full">
                            ADQUIRIDO
                        </span>
                    </div>

                    <!-- Media Count -->
                    <div class="absolute bottom-2 right-2">
                        <span class="bg-black bg-opacity-75 text-white px-2 py-1 text-xs rounded">
                            {{ $purchase->album->medias->count() }} {{ $purchase->album->medias->count() === 1 ? 'arquivo' : 'arquivos' }}
                        </span>
                    </div>

                    <!-- View Overlay -->
                    <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
                        <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <flux:icon name="eye" class="w-8 h-8 text-white" />
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 px-6 py-4 class="p-4"">
                    <!-- Album Info -->
                    <h3 class="font-semibold text-gray-300 mb-2 truncate group-hover:text-purple-400 transition-colors">
                        {{ $purchase->album->name }}
                    </h3>
                    
                    <!-- Creator -->
                    <div class="flex items-center mb-3">
                        <img 
                            src="{{ $purchase->album->user->avatar_url ?? '/default-avatar.png' }}" 
                            alt="{{ $purchase->album->user->name }}"
                            class="w-6 h-6 rounded-full mr-2"
                        />
                        <span class="text-sm text-gray-400">{{ $purchase->album->user->name }}</span>
                    </div>

                    <!-- Purchase Info -->
                    <div class="flex items-center justify-between text-sm text-gray-400 mb-3">
                        <div>
                            <span class="text-green-400 font-medium">
                                R$ {{ number_format($purchase->amount, 2, ',', '.') }}
                            </span>
                        </div>
                        <div class="text-right">
                            <div class="text-xs">
                                {{ $purchase->purchased_at->format('d/m/Y') }}
                            </div>
                        </div>
                    </div>

                    <!-- Payment Method -->
                    <div class="flex items-center justify-between">
                        <span class="px-2 py-1 text-xs font-medium rounded {{ $purchase->payment_method === 'wallet' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800' }}">
                            {{ $purchase->payment_method === 'wallet' ? 'Carteira' : 'Cartão' }}
                        </span>
                        
                        @if($purchase->album->rating > 0)
                        <div class="flex items-center">
                            <flux:icon name="star" class="w-4 h-4 text-yellow-400 mr-1" />
                            <span class="text-xs">{{ number_format($purchase->album->rating, 1) }}</span>
                        </div>
                        @endif
                    </div>
                </div>
            </a>
        </div>
        @endforeach
    </div>

    <!-- Pagination -->
    {{ $purchases->links() }}
    @else
    <!-- Empty State -->
    <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg text-center py-12">
        @if($search)
        <flux:icon name="magnifying-glass" class="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-300 mb-2">Nenhum álbum encontrado</h3>
        <p class="text-gray-400 mb-6">Tente ajustar os termos de busca.</p>
        <flux:button wire:click="clearSearch" color="purple">
            Limpar Busca
        </flux:button>
        @else
        <flux:icon name="shopping-bag" class="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-300 mb-2">Nenhum álbum adquirido</h3>
        <p class="text-gray-400 mb-6">Você ainda não comprou nenhum álbum premium.</p>
        <flux:button href="{{ route('premium-albums.marketplace') }}" color="purple">
            Explorar Marketplace
        </flux:button>
        @endif
    </div>
    @endif

    <!-- Purchase History Modal -->
    @if($showHistoryModal)
    <flux:modal wire:model="showHistoryModal" size="lg">
        <flux:modal.header>
            <h2 class="text-lg font-semibold text-gray-300">Histórico de Compras</h2>
        </flux:modal.header>
        
        <flux:modal.body>
            <div class="space-y-4">
                @foreach($purchases->take(10) as $purchase)
                <div class="flex items-center space-x-4 p-4 bg-zinc-800 rounded-lg">
                    <div class="w-12 h-12 bg-zinc-700 rounded-lg overflow-hidden">
                        @if($purchase->album->medias->count() > 0)
                            @php $firstMedia = $purchase->album->medias->first(); @endphp
                            @if($firstMedia->type === 'photo')
                                <img 
                                    src="{{ route('premium-albums.media', [$purchase->album, $firstMedia->id]) }}" 
                                    alt="{{ $purchase->album->name }}"
                                    class="w-full h-full object-cover"
                                />
                            @endif
                        @endif
                    </div>
                    <div class="flex-1">
                        <h4 class="font-medium text-gray-300">{{ $purchase->album->name }}</h4>
                        <p class="text-sm text-gray-400">{{ $purchase->album->user->name }}</p>
                    </div>
                    <div class="text-right">
                        <div class="text-green-400 font-medium">
                            R$ {{ number_format($purchase->amount, 2, ',', '.') }}
                        </div>
                        <div class="text-xs text-gray-400">
                            {{ $purchase->purchased_at->format('d/m/Y H:i') }}
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </flux:modal.body>
        
        <flux:modal.footer>
            <flux:button wire:click="closeHistoryModal">
                Fechar
            </flux:button>
        </flux:modal.footer>
    </flux:modal>
    @endif
</div>
