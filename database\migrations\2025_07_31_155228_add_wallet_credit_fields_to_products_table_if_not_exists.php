<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // Verificar se as colunas já existem antes de adicioná-las
            if (!Schema::hasColumn('products', 'adds_wallet_credit')) {
                $table->boolean('adds_wallet_credit')->default(false)->after('is_digital');
                $table->index('adds_wallet_credit');
            }

            if (!Schema::hasColumn('products', 'wallet_credit_amount')) {
                $table->decimal('wallet_credit_amount', 10, 2)->nullable()->after('adds_wallet_credit');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            if (Schema::hasColumn('products', 'adds_wallet_credit')) {
                $table->dropIndex(['adds_wallet_credit']);
                $table->dropColumn('adds_wallet_credit');
            }

            if (Schema::hasColumn('products', 'wallet_credit_amount')) {
                $table->dropColumn('wallet_credit_amount');
            }
        });
    }
};
