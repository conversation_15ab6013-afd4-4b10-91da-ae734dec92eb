<?php

namespace App\Services;

use App\Models\LiveStream;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class StreamOptimizationService
{
    /**
     * Cache de streams ativos
     */
    public function cacheActiveStreams()
    {
        $activeStreams = LiveStream::where('status', 'live')
            ->with(['user:id,name', 'user.userPhotos:id,user_id,photo_path'])
            ->select(['id', 'user_id', 'title', 'viewers_count', 'started_at'])
            ->get();

        Cache::put('active_streams', $activeStreams, now()->addMinutes(1));
        
        return $activeStreams;
    }

    /**
     * Obter streams ativos do cache
     */
    public function getActiveStreams()
    {
        return Cache::remember('active_streams', 60, function () {
            return $this->cacheActiveStreams();
        });
    }

    /**
     * Cache de replays populares
     */
    public function cachePopularReplays()
    {
        $popularReplays = LiveStream::where('has_replay', true)
            ->whereNotNull('video_path')
            ->with(['user:id,name', 'user.userPhotos:id,user_id,photo_path'])
            ->orderByDesc('viewers_count')
            ->limit(20)
            ->get();

        Cache::put('popular_replays', $popularReplays, now()->addHours(1));
        
        return $popularReplays;
    }

    /**
     * Obter replays populares do cache
     */
    public function getPopularReplays()
    {
        return Cache::remember('popular_replays', 3600, function () {
            return $this->cachePopularReplays();
        });
    }

    /**
     * Otimizar vídeo para diferentes qualidades
     */
    public function optimizeVideo($videoPath, $streamId)
    {
        try {
            $originalPath = Storage::path('public/' . $videoPath);
            
            if (!file_exists($originalPath)) {
                Log::error('Arquivo de vídeo não encontrado: ' . $originalPath);
                return false;
            }

            // Criar versões otimizadas
            $qualities = [
                '720p' => [
                    'width' => 1280,
                    'height' => 720,
                    'bitrate' => '2500k'
                ],
                '480p' => [
                    'width' => 854,
                    'height' => 480,
                    'bitrate' => '1500k'
                ],
                '360p' => [
                    'width' => 640,
                    'height' => 360,
                    'bitrate' => '800k'
                ]
            ];

            $optimizedPaths = [];

            foreach ($qualities as $quality => $settings) {
                $outputPath = 'live-streams/optimized/' . $streamId . '-' . $quality . '.mp4';
                $fullOutputPath = Storage::path('public/' . $outputPath);

                // Criar diretório se não existir
                $outputDir = dirname($fullOutputPath);
                if (!is_dir($outputDir)) {
                    mkdir($outputDir, 0755, true);
                }

                // Comando FFmpeg para otimização (se disponível)
                $command = sprintf(
                    'ffmpeg -i "%s" -vf scale=%d:%d -b:v %s -c:v libx264 -preset fast -c:a aac -b:a 128k "%s"',
                    $originalPath,
                    $settings['width'],
                    $settings['height'],
                    $settings['bitrate'],
                    $fullOutputPath
                );

                // Verificar se FFmpeg está disponível
                if ($this->isFFmpegAvailable()) {
                    exec($command . ' 2>&1', $output, $returnCode);
                    
                    if ($returnCode === 0 && file_exists($fullOutputPath)) {
                        $optimizedPaths[$quality] = $outputPath;
                        Log::info("Vídeo otimizado para {$quality}: {$outputPath}");
                    } else {
                        Log::warning("Falha ao otimizar vídeo para {$quality}: " . implode("\n", $output));
                    }
                } else {
                    // Fallback: copiar arquivo original
                    copy($originalPath, $fullOutputPath);
                    $optimizedPaths[$quality] = $outputPath;
                    Log::info("FFmpeg não disponível, copiando arquivo original para {$quality}");
                }
            }

            // Salvar caminhos otimizados no banco
            $liveStream = LiveStream::find($streamId);
            if ($liveStream) {
                $liveStream->update([
                    'optimized_paths' => json_encode($optimizedPaths)
                ]);
            }

            return $optimizedPaths;

        } catch (\Exception $e) {
            Log::error('Erro ao otimizar vídeo: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Verificar se FFmpeg está disponível
     */
    private function isFFmpegAvailable()
    {
        exec('ffmpeg -version 2>&1', $output, $returnCode);
        return $returnCode === 0;
    }

    /**
     * Comprimir vídeo usando algoritmos web-friendly
     */
    public function compressVideo($inputPath, $outputPath, $quality = 'medium')
    {
        try {
            $compressionSettings = [
                'low' => [
                    'crf' => 28,
                    'preset' => 'fast',
                    'maxrate' => '1M',
                    'bufsize' => '2M'
                ],
                'medium' => [
                    'crf' => 23,
                    'preset' => 'medium',
                    'maxrate' => '2.5M',
                    'bufsize' => '5M'
                ],
                'high' => [
                    'crf' => 18,
                    'preset' => 'slow',
                    'maxrate' => '5M',
                    'bufsize' => '10M'
                ]
            ];

            $settings = $compressionSettings[$quality] ?? $compressionSettings['medium'];

            $command = sprintf(
                'ffmpeg -i "%s" -c:v libx264 -crf %d -preset %s -maxrate %s -bufsize %s -c:a aac -b:a 128k -movflags +faststart "%s"',
                $inputPath,
                $settings['crf'],
                $settings['preset'],
                $settings['maxrate'],
                $settings['bufsize'],
                $outputPath
            );

            if ($this->isFFmpegAvailable()) {
                exec($command . ' 2>&1', $output, $returnCode);
                
                if ($returnCode === 0 && file_exists($outputPath)) {
                    Log::info("Vídeo comprimido com sucesso: {$outputPath}");
                    return true;
                } else {
                    Log::error("Falha na compressão: " . implode("\n", $output));
                    return false;
                }
            } else {
                // Fallback: copiar arquivo sem compressão
                copy($inputPath, $outputPath);
                Log::info("FFmpeg não disponível, arquivo copiado sem compressão");
                return true;
            }

        } catch (\Exception $e) {
            Log::error('Erro ao comprimir vídeo: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Limpar cache de streams
     */
    public function clearStreamCache()
    {
        Cache::forget('active_streams');
        Cache::forget('popular_replays');
        
        // Limpar cache de streams específicos
        $cacheKeys = Cache::getRedis()->keys('stream_*');
        foreach ($cacheKeys as $key) {
            Cache::forget($key);
        }
        
        Log::info('Cache de streams limpo');
    }

    /**
     * Otimizar performance do banco de dados
     */
    public function optimizeDatabase()
    {
        try {
            // Limpar streams antigos sem replay
            $deletedCount = LiveStream::where('status', 'ended')
                ->where('has_replay', false)
                ->where('ended_at', '<', now()->subDays(7))
                ->delete();

            Log::info("Streams antigos removidos: {$deletedCount}");

            // Atualizar estatísticas de viewers para streams inativos
            LiveStream::where('status', '!=', 'live')
                ->where('viewers_count', '>', 0)
                ->update(['viewers_count' => 0]);

            Log::info('Banco de dados otimizado');
            
            return true;

        } catch (\Exception $e) {
            Log::error('Erro ao otimizar banco de dados: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Monitorar performance do sistema
     */
    public function getPerformanceMetrics()
    {
        return [
            'active_streams_count' => $this->getActiveStreams()->count(),
            'total_replays' => LiveStream::where('has_replay', true)->count(),
            'cache_hits' => Cache::get('cache_hits', 0),
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true),
            'disk_space' => disk_free_space(storage_path()),
            'database_size' => $this->getDatabaseSize()
        ];
    }

    /**
     * Obter tamanho do banco de dados
     */
    private function getDatabaseSize()
    {
        try {
            $result = \DB::select("SELECT SUM(data_length + index_length) as size FROM information_schema.tables WHERE table_schema = DATABASE()");
            return $result[0]->size ?? 0;
        } catch (\Exception $e) {
            return 0;
        }
    }
}
