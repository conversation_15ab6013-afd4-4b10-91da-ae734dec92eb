<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\UserActivity;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class UserActivityTracker
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Só registra atividade para requisições GET (páginas)
        if ($request->method() !== 'GET') {
            return $response;
        }

        // Ignora requisições AJAX/API e assets
        if ($request->ajax() || 
            $request->wantsJson() || 
            $this->shouldIgnoreUrl($request->url())) {
            return $response;
        }

        // Registra a atividade de forma assíncrona para não impactar performance
        $this->logActivity($request);

        return $response;
    }

    /**
     * Registra a atividade do usuário
     */
    private function logActivity(Request $request): void
    {
        try {
            // Obtém informações da rota
            $route = $request->route();
            $routeName = $route ? $route->getName() : null;

            // Dados adicionais
            $additionalData = [
                'page_title' => $this->extractPageTitle($request),
                'query_params' => $request->query(),
            ];

            UserActivity::logPageView(
                url: $request->url(),
                routeName: $routeName,
                additionalData: $additionalData
            );
        } catch (\Exception $e) {
            // Log do erro mas não interrompe a aplicação
            \Log::error('Erro ao registrar atividade do usuário: ' . $e->getMessage());
        }
    }

    /**
     * Verifica se a URL deve ser ignorada
     */
    private function shouldIgnoreUrl(string $url): bool
    {
        $ignoredPatterns = [
            '/livewire/',
            '/api/',
            '/admin/analytics', // Evita loop infinito no dashboard de analytics
            '/_debugbar/',
            '/telescope/',
            '/horizon/',
            '/css/',
            '/js/',
            '/images/',
            '/storage/',
            '/favicon.ico',
            '/robots.txt',
            '/sitemap.xml',
        ];

        foreach ($ignoredPatterns as $pattern) {
            if (Str::contains($url, $pattern)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Extrai o título da página (se disponível)
     */
    private function extractPageTitle(Request $request): ?string
    {
        // Mapeia rotas para títulos conhecidos
        $routeTitles = [
            'dashboard' => 'Dashboard',
            'profile.show' => 'Perfil',
            'messages.index' => 'Mensagens',
            'groups.index' => 'Grupos',
            'events.index' => 'Eventos',
            'shop.index' => 'Loja',
            'wallet.index' => 'Carteira',
            'ranking' => 'Ranking',
            'radar' => 'Radar',
            'feed' => 'Feed',
            'contos.index' => 'Contos',
            'search' => 'Busca',
        ];

        $routeName = $request->route()?->getName();
        
        if ($routeName && isset($routeTitles[$routeName])) {
            return $routeTitles[$routeName];
        }

        // Tenta extrair do path
        $path = trim($request->path(), '/');
        if (empty($path) || $path === '/') {
            return 'Home';
        }

        return ucfirst(str_replace(['-', '_'], ' ', $path));
    }
}
