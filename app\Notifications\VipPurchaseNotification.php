<?php

namespace App\Notifications;

use App\Models\Order;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class VipPurchaseNotification extends Notification
{
    use Queueable;

    protected $order;
    protected $user;
    protected $vipProducts;
    protected $isAdminCopy;

    /**
     * Create a new notification instance.
     */
    public function __construct(Order $order, User $user, array $vipProducts, bool $isAdminCopy = false)
    {
        $this->order = $order;
        $this->user = $user;
        $this->vipProducts = $vipProducts;
        $this->isAdminCopy = $isAdminCopy;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via($notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable): MailMessage
    {
        if ($this->isAdminCopy) {
            return $this->buildAdminEmail($notifiable);
        } else {
            return $this->buildUserEmail($notifiable);
        }
    }

    /**
     * Build email for admin notification.
     */
    protected function buildAdminEmail($notifiable): MailMessage
    {
        $totalVipDays = collect($this->vipProducts)->sum('vip_duration_days');
        $productNames = collect($this->vipProducts)->pluck('name')->implode(', ');

        return (new MailMessage)
            ->subject('[ADMIN] Nova Compra VIP - Pedido #' . $this->order->id)
            ->view('emails.admin-vip-purchase', [
                'order' => $this->order,
                'user' => $this->user,
                'vipProducts' => $this->vipProducts,
                'totalVipDays' => $totalVipDays,
                'productNames' => $productNames,
                'adminRecipient' => $notifiable,
            ]);
    }

    /**
     * Build email for user confirmation.
     */
    protected function buildUserEmail($notifiable): MailMessage
    {
        $totalVipDays = collect($this->vipProducts)->sum('vip_duration_days');
        $productNames = collect($this->vipProducts)->pluck('name')->implode(', ');

        return (new MailMessage)
            ->subject('🎉 Parabéns! Seu acesso VIP foi ativado - ' . config('app.name'))
            ->view('emails.user-vip-purchase-confirmation', [
                'order' => $this->order,
                'user' => $this->user,
                'vipProducts' => $this->vipProducts,
                'totalVipDays' => $totalVipDays,
                'productNames' => $productNames,
            ]);
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray($notifiable): array
    {
        return [
            'order_id' => $this->order->id,
            'user_id' => $this->user->id,
            'total_vip_days' => collect($this->vipProducts)->sum('vip_duration_days'),
            'product_count' => count($this->vipProducts),
        ];
    }
}
