<?php

namespace App\Livewire\Events;

use App\Models\EventAttendee;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Auth;

class MyEvents extends Component
{
    use WithPagination;

    public $filter = 'all'; // all, upcoming, past, confirmed, pending
    public $search = '';
    public $perPage = 12;

    protected $queryString = [
        'filter' => ['except' => 'all'],
        'search' => ['except' => ''],
    ];

    public function mount()
    {
        // Garantir que o usuário está autenticado
        if (!Auth::check()) {
            return redirect()->route('login');
        }
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingFilter()
    {
        $this->resetPage();
    }

    public function setFilter($filter)
    {
        $this->filter = $filter;
        $this->resetPage();
    }

    public function cancelRegistration($attendeeId)
    {
        $attendee = EventAttendee::where('id', $attendeeId)
            ->where('user_id', Auth::id())
            ->first();

        if (!$attendee) {
            session()->flash('error', 'Inscrição não encontrada.');
            return;
        }

        // Só permite cancelar se ainda não foi confirmado ou se o evento ainda não aconteceu
        if ($attendee->status === 'attended') {
            session()->flash('error', 'Não é possível cancelar após o check-in.');
            return;
        }

        if ($attendee->event->date < now()) {
            session()->flash('error', 'Não é possível cancelar eventos que já aconteceram.');
            return;
        }

        // Se o pagamento foi confirmado, seria necessário processar reembolso
        if ($attendee->payment_status === 'completed') {
            session()->flash('error', 'Para cancelar eventos pagos, entre em contato com o suporte.');
            return;
        }

        $attendee->update(['status' => 'cancelled']);
        session()->flash('success', 'Inscrição cancelada com sucesso.');
    }

    public function render()
    {
        $query = EventAttendee::where('user_id', Auth::id())
            ->with(['event' => function ($q) {
                $q->orderBy('date', 'desc');
            }])
            ->whereHas('event', function ($q) {
                if ($this->search) {
                    $q->where('name', 'like', '%' . $this->search . '%')
                        ->orWhere('location', 'like', '%' . $this->search . '%');
                }
            });

        // Aplicar filtros
        switch ($this->filter) {
            case 'upcoming':
                $query->whereHas('event', function ($q) {
                    $q->where('date', '>=', now());
                });
                break;
            case 'past':
                $query->whereHas('event', function ($q) {
                    $q->where('date', '<', now());
                });
                break;
            case 'confirmed':
                $query->where('status', 'confirmed')
                    ->where('payment_status', 'completed');
                break;
            case 'pending':
                $query->where(function ($q) {
                    $q->where('payment_status', 'pending')
                        ->orWhere('status', 'registered');
                });
                break;
        }

        $attendees = $query->orderBy('created_at', 'desc')
            ->paginate($this->perPage);

        // Estatísticas
        $stats = [
            'total' => EventAttendee::where('user_id', Auth::id())->count(),
            'upcoming' => EventAttendee::where('user_id', Auth::id())
                ->whereHas('event', function ($q) {
                    $q->where('date', '>=', now());
                })->count(),
            'confirmed' => EventAttendee::where('user_id', Auth::id())
                ->where('status', 'confirmed')
                ->where('payment_status', 'completed')
                ->count(),
            'attended' => EventAttendee::where('user_id', Auth::id())
                ->where('status', 'attended')
                ->count(),
        ];

        return view('livewire.events.my-events', [
            'attendees' => $attendees,
            'stats' => $stats,
        ]);
    }
}
