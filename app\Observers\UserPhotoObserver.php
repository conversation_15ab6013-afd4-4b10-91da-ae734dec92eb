<?php

namespace App\Observers;

use App\Models\UserPhoto;
use Illuminate\Support\Facades\Log;

class UserPhotoObserver
{
    /**
     * Handle the UserPhoto "created" event.
     */
    public function created(UserPhoto $userPhoto): void
    {
        $this->syncUserPhotoFile($userPhoto);
    }

    /**
     * Handle the UserPhoto "updated" event.
     */
    public function updated(UserPhoto $userPhoto): void
    {
        $this->syncUserPhotoFile($userPhoto);
    }

    /**
     * Handle the UserPhoto "deleted" event.
     */
    public function deleted(UserPhoto $userPhoto): void
    {
        $this->cleanupUserPhotoFile($userPhoto);
    }

    /**
     * Sincroniza arquivo da foto do usuário para public/storage em produção
     */
    private function syncUserPhotoFile(UserPhoto $userPhoto): void
    {
        // Apenas em produção
        if (!app()->environment('production')) {
            return;
        }

        try {
            if ($userPhoto->photo_path) {
                $this->syncFile($userPhoto->photo_path);
            }
        } catch (\Exception $e) {
            Log::error('Erro ao sincronizar foto do usuário', [
                'user_photo_id' => $userPhoto->id,
                'user_id' => $userPhoto->user_id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Remove arquivo da foto do usuário de public/storage
     */
    private function cleanupUserPhotoFile(UserPhoto $userPhoto): void
    {
        // Apenas em produção
        if (!app()->environment('production')) {
            return;
        }

        try {
            if ($userPhoto->photo_path) {
                $this->removeFile($userPhoto->photo_path);
            }
        } catch (\Exception $e) {
            Log::error('Erro ao limpar foto do usuário', [
                'user_photo_id' => $userPhoto->id,
                'user_id' => $userPhoto->user_id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Sincroniza um arquivo específico
     */
    private function syncFile(string $path): void
    {
        $sourcePath = storage_path('app/public/' . $path);
        $publicPath = public_path('storage/' . $path);

        if (!file_exists($sourcePath)) {
            return;
        }

        // Criar diretório se não existir
        $publicDir = dirname($publicPath);
        if (!is_dir($publicDir)) {
            mkdir($publicDir, 0755, true);
        }

        // Copiar arquivo
        if (copy($sourcePath, $publicPath)) {
            Log::info("Foto de usuário sincronizada automaticamente: {$path}");
        } else {
            Log::error("Falha ao sincronizar foto de usuário: {$path}");
        }
    }

    /**
     * Remove um arquivo específico
     */
    private function removeFile(string $path): void
    {
        $publicPath = public_path('storage/' . $path);

        if (file_exists($publicPath)) {
            if (unlink($publicPath)) {
                Log::info("Foto de usuário removida de public/storage: {$path}");
            } else {
                Log::error("Falha ao remover foto de usuário: {$path}");
            }
        }
    }
}
