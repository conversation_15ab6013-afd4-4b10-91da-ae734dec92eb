<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class PremiumAlbumSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'key',
        'value',
        'type',
        'description',
    ];

    /**
     * Cache das configurações.
     */
    private static $cachedSettings = null;

    /**
     * Obtém uma configuração pelo key.
     */
    public static function get($key, $default = null)
    {
        $settings = self::getAllCached();
        
        if (!isset($settings[$key])) {
            return $default;
        }

        $setting = $settings[$key];
        
        return self::castValue($setting['value'], $setting['type']);
    }

    /**
     * Define uma configuração.
     */
    public static function set($key, $value, $type = 'string', $description = null)
    {
        $setting = self::updateOrCreate(
            ['key' => $key],
            [
                'value' => $value,
                'type' => $type,
                'description' => $description,
            ]
        );

        // Limpar cache
        self::clearCache();

        return $setting;
    }

    /**
     * Obtém todas as configurações em cache.
     */
    private static function getAllCached()
    {
        if (self::$cachedSettings === null) {
            self::$cachedSettings = Cache::remember('premium_album_settings', 3600, function () {
                return self::all()->keyBy('key')->toArray();
            });
        }

        return self::$cachedSettings;
    }

    /**
     * Converte o valor para o tipo correto.
     */
    private static function castValue($value, $type)
    {
        switch ($type) {
            case 'boolean':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case 'integer':
                return (int) $value;
            case 'decimal':
            case 'float':
                return (float) $value;
            case 'json':
                return json_decode($value, true);
            case 'array':
                return is_array($value) ? $value : json_decode($value, true);
            default:
                return $value;
        }
    }

    /**
     * Limpa o cache das configurações.
     */
    public static function clearCache()
    {
        self::$cachedSettings = null;
        Cache::forget('premium_album_settings');
    }

    /**
     * Boot do modelo para limpar cache ao salvar.
     */
    protected static function boot()
    {
        parent::boot();

        static::saved(function () {
            self::clearCache();
        });

        static::deleted(function () {
            self::clearCache();
        });
    }

    /**
     * Configurações padrão do sistema.
     */
    public static function getDefaults()
    {
        return [
            'default_commission_rate' => 10.00,
            'min_album_price' => 5.00,
            'max_album_price' => 500.00,
            'max_files_per_album' => 50,
            'max_file_size_mb' => 50,
            'auto_approve_albums' => false,
            'allowed_file_types' => ['jpg', 'jpeg', 'png', 'gif', 'webp', 'mp4', 'mov', 'avi', 'webm'],
        ];
    }

    /**
     * Métodos de conveniência para configurações específicas.
     */
    public static function getCommissionRate()
    {
        return self::get('default_commission_rate', 10.00);
    }

    public static function getMinPrice()
    {
        return self::get('min_album_price', 5.00);
    }

    public static function getMaxPrice()
    {
        return self::get('max_album_price', 500.00);
    }

    public static function getMaxFilesPerAlbum()
    {
        return self::get('max_files_per_album', 50);
    }

    public static function getMaxFileSize()
    {
        return self::get('max_file_size_mb', 50);
    }

    public static function shouldAutoApprove()
    {
        return self::get('auto_approve_albums', false);
    }

    public static function getAllowedFileTypes()
    {
        return self::get('allowed_file_types', ['jpg', 'jpeg', 'png', 'gif', 'webp', 'mp4', 'mov', 'avi', 'webm']);
    }
}
