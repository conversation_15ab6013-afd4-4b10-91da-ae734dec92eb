<?php

namespace App\Livewire\Admin;

use App\Models\Event;
use App\Models\EventAttendee;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Auth;

class EventAttendees extends Component
{
    use WithPagination;

    public Event $event;
    public $search = '';
    public $statusFilter = '';
    public $paymentStatusFilter = '';
    public $sortBy = 'created_at';
    public $sortDirection = 'desc';
    public $perPage = 15;

    // Check-in modal
    public $showCheckInModal = false;
    public $selectedAttendee = null;
    public $ticketCodeInput = '';

    protected $queryString = [
        'search' => ['except' => ''],
        'statusFilter' => ['except' => ''],
        'paymentStatusFilter' => ['except' => ''],
        'sortBy' => ['except' => 'created_at'],
        'sortDirection' => ['except' => 'desc'],
    ];

    public function mount(Event $event)
    {
        // Verificar se o usuário é admin
        if (!Auth::user()->isAdmin()) {
            abort(403, 'Acesso negado.');
        }

        $this->event = $event;
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingStatusFilter()
    {
        $this->resetPage();
    }

    public function updatingPaymentStatusFilter()
    {
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortBy === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $field;
            $this->sortDirection = 'asc';
        }
    }

    public function openCheckInModal($attendeeId)
    {
        $this->selectedAttendee = EventAttendee::find($attendeeId);
        $this->ticketCodeInput = '';
        $this->showCheckInModal = true;
    }

    public function closeCheckInModal()
    {
        $this->showCheckInModal = false;
        $this->selectedAttendee = null;
        $this->ticketCodeInput = '';
    }

    public function checkInAttendee()
    {
        if (!$this->selectedAttendee) {
            session()->flash('error', 'Participante não encontrado.');
            return;
        }

        // Verificar se o código do ingresso está correto
        if (strtoupper($this->ticketCodeInput) !== strtoupper($this->selectedAttendee->ticket_code)) {
            session()->flash('error', 'Código do ingresso incorreto.');
            return;
        }

        // Verificar se já foi feito check-in
        if ($this->selectedAttendee->checked_in_at) {
            session()->flash('error', 'Check-in já foi realizado para este participante.');
            return;
        }

        // Realizar check-in
        $this->selectedAttendee->checkIn();

        session()->flash('success', 'Check-in realizado com sucesso!');
        $this->closeCheckInModal();
    }

    public function quickCheckIn($attendeeId)
    {
        $attendee = EventAttendee::find($attendeeId);

        if (!$attendee) {
            session()->flash('error', 'Participante não encontrado.');
            return;
        }

        if ($attendee->checked_in_at) {
            session()->flash('error', 'Check-in já foi realizado para este participante.');
            return;
        }

        if ($attendee->payment_status !== 'completed') {
            session()->flash('error', 'Não é possível fazer check-in. Pagamento não confirmado.');
            return;
        }

        $attendee->checkIn();
        session()->flash('success', 'Check-in realizado com sucesso!');
    }

    public function exportAttendees()
    {
        // TODO: Implementar exportação para CSV/Excel
        session()->flash('info', 'Funcionalidade de exportação será implementada em breve.');
    }

    public function render()
    {
        $query = EventAttendee::where('event_id', $this->event->id)
            ->with(['user']);

        // Aplicar filtros
        if ($this->search) {
            $query->whereHas('user', function ($q) {
                $q->where('name', 'like', '%' . $this->search . '%')
                  ->orWhere('email', 'like', '%' . $this->search . '%')
                  ->orWhere('username', 'like', '%' . $this->search . '%');
            })->orWhere('ticket_code', 'like', '%' . $this->search . '%');
        }

        if ($this->statusFilter) {
            $query->where('status', $this->statusFilter);
        }

        if ($this->paymentStatusFilter) {
            $query->where('payment_status', $this->paymentStatusFilter);
        }

        // Aplicar ordenação
        $query->orderBy($this->sortBy, $this->sortDirection);

        $attendees = $query->paginate($this->perPage);

        // Estatísticas
        $stats = [
            'total' => $this->event->attendees()->count(),
            'confirmed' => $this->event->attendees()->where('status', 'confirmed')->count(),
            'checked_in' => $this->event->attendees()->whereNotNull('checked_in_at')->count(),
            'pending_payment' => $this->event->attendees()->where('payment_status', 'pending')->count(),
            'revenue' => $this->event->attendees()->where('payment_status', 'completed')->sum('amount_paid'),
        ];

        return view('livewire.admin.event-attendees', [
            'attendees' => $attendees,
            'stats' => $stats,
        ])->layout('layouts.app');
    }
}
