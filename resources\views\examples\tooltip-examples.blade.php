{{-- Exemplos de Tooltips Customizados --}}
<div class="p-6 space-y-8">
    <h1 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">
        Exemplos de Tooltips Customizados
    </h1>

    {{-- Toolt<PERSON> --}}
    <div class="space-y-4">
        <h2 class="text-lg font-semibold text-gray-800 dark:text-gray-200">
            Toolt<PERSON> (Pequeno)
        </h2>
        
        <div class="flex gap-4 items-center">
            <flux:tooltip content="Este é um tooltip padrão" position="top">
                <flux:button variant="primary">
                    Hover para tooltip padrão
                </flux:button>
            </flux:tooltip>

            <flux:tooltip content="Buscar usuários" position="bottom">
                <flux:button variant="outline" icon="magnifying-glass">
                    Buscar
                </flux:button>
            </flux:tooltip>

            <flux:tooltip content="Configurações do sistema" position="right">
                <flux:button variant="ghost" icon="cog-6-tooth">
                    Configurações
                </flux:button>
            </flux:tooltip>
        </div>
    </div>

    {{-- Tooltip com Texto Longo --}}
    <div class="space-y-4">
        <h2 class="text-lg font-semibold text-gray-800 dark:text-gray-200">
            Tooltip com Texto Longo
        </h2>
        
        <div class="flex gap-4 items-center">
            <flux:tooltip content="Este é um tooltip com texto mais longo que demonstra como o tooltip se adapta automaticamente ao conteúdo maior" position="top">
                <flux:button variant="primary">
                    Tooltip com texto longo
                </flux:button>
            </flux:tooltip>

            <flux:tooltip content="Aqui temos uma explicação detalhada sobre esta funcionalidade que requer mais espaço para ser exibida adequadamente" position="bottom">
                <flux:button variant="outline">
                    Explicação detalhada
                </flux:button>
            </flux:tooltip>
        </div>
    </div>

    {{-- Tooltip com Atalho de Teclado --}}
    <div class="space-y-4">
        <h2 class="text-lg font-semibold text-gray-800 dark:text-gray-200">
            Tooltip com Atalho de Teclado
        </h2>
        
        <div class="flex gap-4 items-center">
            <flux:tooltip content="Abrir busca" kbd="Ctrl+K" position="top">
                <flux:button variant="primary" icon="magnifying-glass">
                    Buscar
                </flux:button>
            </flux:tooltip>

            <flux:tooltip content="Salvar documento" kbd="Ctrl+S" position="bottom">
                <flux:button variant="outline" icon="document-arrow-down">
                    Salvar
                </flux:button>
            </flux:tooltip>

            <flux:tooltip content="Copiar para área de transferência" kbd="Ctrl+C" position="right">
                <flux:button variant="ghost" icon="clipboard">
                    Copiar
                </flux:button>
            </flux:tooltip>
        </div>
    </div>

    {{-- Tooltip em Ícones --}}
    <div class="space-y-4">
        <h2 class="text-lg font-semibold text-gray-800 dark:text-gray-200">
            Tooltip em Ícones
        </h2>
        
        <div class="flex gap-4 items-center">
            <flux:tooltip content="Página inicial" position="top">
                <button class="p-2 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white">
                    <x-flux::icon name="home" class="w-6 h-6" />
                </button>
            </flux:tooltip>

            <flux:tooltip content="Notificações" position="top">
                <button class="p-2 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white relative">
                    <x-flux::icon name="bell" class="w-6 h-6" />
                    <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">3</span>
                </button>
            </flux:tooltip>

            <flux:tooltip content="Perfil do usuário" position="top">
                <button class="p-2 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white">
                    <x-flux::icon name="user-circle" class="w-6 h-6" />
                </button>
            </flux:tooltip>
        </div>
    </div>

    {{-- Tooltip Interativo --}}
    <div class="space-y-4">
        <h2 class="text-lg font-semibold text-gray-800 dark:text-gray-200">
            Tooltip Interativo (Clicável)
        </h2>
        
        <div class="flex gap-4 items-center">
            <flux:tooltip content="Clique para mais informações sobre esta funcionalidade avançada" toggleable position="top">
                <flux:button variant="primary">
                    Tooltip clicável
                </flux:button>
            </flux:tooltip>

            <flux:tooltip content="Este tooltip permanece aberto até você clicar fora dele" toggleable position="bottom">
                <flux:button variant="outline">
                    Tooltip persistente
                </flux:button>
            </flux:tooltip>
        </div>
    </div>

    {{-- Tooltip em Diferentes Posições --}}
    <div class="space-y-4">
        <h2 class="text-lg font-semibold text-gray-800 dark:text-gray-200">
            Diferentes Posições
        </h2>
        
        <div class="grid grid-cols-2 gap-4 max-w-md mx-auto">
            <flux:tooltip content="Tooltip no topo" position="top">
                <flux:button variant="outline" class="w-full">
                    Top
                </flux:button>
            </flux:tooltip>

            <flux:tooltip content="Tooltip à direita" position="right">
                <flux:button variant="outline" class="w-full">
                    Right
                </flux:button>
            </flux:tooltip>

            <flux:tooltip content="Tooltip embaixo" position="bottom">
                <flux:button variant="outline" class="w-full">
                    Bottom
                </flux:button>
            </flux:tooltip>

            <flux:tooltip content="Tooltip à esquerda" position="left">
                <flux:button variant="outline" class="w-full">
                    Left
                </flux:button>
            </flux:tooltip>
        </div>
    </div>

    {{-- Informações sobre Customização --}}
    <div class="mt-8 p-4 bg-gray-100 dark:bg-zinc-800 rounded-lg">
        <h3 class="text-md font-semibold text-gray-800 dark:text-gray-200 mb-2">
            Customizações Aplicadas:
        </h3>
        <ul class="text-sm text-gray-600 dark:text-gray-400 space-y-1">
            <li>• <strong>Fundo:</strong> Preto (#000000)</li>
            <li>• <strong>Texto:</strong> Branco (#ffffff)</li>
            <li>• <strong>Tamanho:</strong> text-sm (14px) em vez de text-xs (12px)</li>
            <li>• <strong>Padding:</strong> py-3 px-4 em vez de py-2 px-2.5</li>
            <li>• <strong>Largura mínima:</strong> 120px</li>
            <li>• <strong>Largura máxima:</strong> 300px</li>
            <li>• <strong>Borda:</strong> Cinza escuro com sombra</li>
            <li>• <strong>Z-index:</strong> 9999 para ficar sempre no topo</li>
        </ul>
    </div>
</div>
