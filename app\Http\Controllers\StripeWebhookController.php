<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\VipSubscription;
use App\Models\SubscriptionPlan;
use App\Models\Order;
use App\Models\Event;
use App\Models\EventAttendee;
use App\Services\VipSubscriptionService;
use App\Services\VipPurchaseService;
use Illuminate\Support\Facades\Log;
use Laravel\Cashier\Http\Controllers\WebhookController as CashierWebhookController;
use Symfony\Component\HttpFoundation\Response;
use Carbon\Carbon;

class StripeWebhookController extends CashierWebhookController
{
    /**
     * Handle Stripe checkout session completed event for subscriptions.
     *
     * @param  array  $payload
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function handleCheckoutSessionCompleted(array $payload)
    {
        $session = $payload['data']['object'];

        Log::info('Checkout session completed webhook received', [
            'session_id' => $session['id'],
            'mode' => $session['mode'],
            'metadata' => $session['metadata'] ?? [],
        ]);

        // Handle subscription checkout sessions
        if ($session['mode'] === 'subscription' && isset($session['subscription'])) {
            return $this->handleSubscriptionCheckout($session);
        }

        // Handle legacy one-time payment sessions (backward compatibility)
        if (isset($session['metadata']['subscription_id'])) {
            return $this->handleLegacySubscriptionPayment($session);
        }

        // Handle wallet deposit payments
        if (isset($session['metadata']['type']) && $session['metadata']['type'] === 'wallet_deposit') {
            return $this->handleWalletDepositPayment($session);
        }

        // Handle shop order payments (VIP products)
        if (isset($session['metadata']['order_id'])) {
            return $this->handleShopOrderPayment($session);
        }

        // Handle event ticket payments
        if (isset($session['metadata']['event_id']) && isset($session['metadata']['attendee_id'])) {
            return $this->handleEventTicketPayment($session);
        }

        return new Response('Webhook Handled', 200);
    }

    /**
     * Handle subscription checkout completion
     */
    private function handleSubscriptionCheckout(array $session)
    {
        try {
            $stripeSubscriptionId = $session['subscription'];
            $userId = $session['metadata']['user_id'] ?? null;
            $planId = $session['metadata']['plan_id'] ?? null;

            if (!$userId || !$planId) {
                Log::warning('Missing user_id or plan_id in subscription checkout metadata', [
                    'session_id' => $session['id'],
                    'metadata' => $session['metadata'],
                ]);
                return new Response('Missing metadata', 400);
            }

            $user = User::find($userId);
            $plan = SubscriptionPlan::find($planId);

            if (!$user || !$plan) {
                Log::error('User or plan not found for subscription checkout', [
                    'user_id' => $userId,
                    'plan_id' => $planId,
                    'session_id' => $session['id'],
                ]);
                return new Response('User or plan not found', 404);
            }

            // Check if we already have this subscription
            $existingSubscription = VipSubscription::where('stripe_subscription_id', $stripeSubscriptionId)->first();

            if ($existingSubscription) {
                Log::info('Subscription already exists, skipping creation', [
                    'subscription_id' => $existingSubscription->id,
                    'stripe_subscription_id' => $stripeSubscriptionId,
                ]);
                return new Response('Subscription already exists', 200);
            }

            // Get the Stripe subscription details
            $stripeSubscription = \Stripe\Subscription::retrieve($stripeSubscriptionId);

            // Create local subscription record
            $vipSubscription = VipSubscription::create([
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'plan_days' => $this->calculatePlanDays($plan),
                'amount' => $plan->price,
                'status' => $this->mapStripeStatus($stripeSubscription->status),
                'stripe_subscription_id' => $stripeSubscription->id,
                'stripe_customer_id' => $user->stripe_id,
                'activated_at' => $stripeSubscription->status === 'active' ? Carbon::now() : null,
                'trial_ends_at' => $stripeSubscription->trial_end ? Carbon::createFromTimestamp($stripeSubscription->trial_end) : null,
                'expires_at' => Carbon::createFromTimestamp($stripeSubscription->current_period_end),
            ]);

            // Update user role
            if (in_array($stripeSubscription->status, ['active', 'trialing'])) {
                $user->update(['role' => $plan->user_role]);
            }

            Log::info('Subscription created via checkout webhook', [
                'subscription_id' => $vipSubscription->id,
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'stripe_subscription_id' => $stripeSubscription->id,
                'status' => $stripeSubscription->status,
            ]);

            return new Response('Subscription created', 200);
        } catch (\Exception $e) {
            Log::error('Error handling subscription checkout webhook', [
                'session_id' => $session['id'],
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return new Response('Error processing webhook', 500);
        }
    }

    /**
     * Handle legacy one-time payment (backward compatibility)
     */
    private function handleLegacySubscriptionPayment(array $session)
    {
        $subscriptionId = $session['metadata']['subscription_id'];
        $subscription = VipSubscription::find($subscriptionId);

        if ($subscription) {
            $subscription->update([
                'stripe_payment_id' => $session['payment_intent'],
            ]);

            $vipSubscriptionService = app(VipSubscriptionService::class);
            $vipSubscriptionService->activateSubscription($subscription);

            Log::info('Legacy VIP subscription activated via webhook', [
                'subscription_id' => $subscriptionId,
                'user_id' => $subscription->user_id,
                'plan_days' => $subscription->plan_days,
            ]);
        }

        return new Response('Legacy subscription handled', 200);
    }

    /**
     * Handle customer subscription created event
     */
    public function handleCustomerSubscriptionCreated(array $payload)
    {
        $stripeSubscription = $payload['data']['object'];

        Log::info('Customer subscription created webhook received', [
            'stripe_subscription_id' => $stripeSubscription['id'],
            'customer' => $stripeSubscription['customer'],
            'status' => $stripeSubscription['status'],
        ]);

        // This is usually handled by checkout.session.completed
        // But we can use this as a fallback or for subscriptions created via API

        return new Response('Subscription created webhook handled', 200);
    }

    /**
     * Handle customer subscription updated event
     */
    public function handleCustomerSubscriptionUpdated(array $payload)
    {
        $stripeSubscription = $payload['data']['object'];

        Log::info('Customer subscription updated webhook received', [
            'stripe_subscription_id' => $stripeSubscription['id'],
            'status' => $stripeSubscription['status'],
            'current_period_end' => $stripeSubscription['current_period_end'],
        ]);

        try {
            $localSubscription = VipSubscription::where('stripe_subscription_id', $stripeSubscription['id'])->first();

            if ($localSubscription) {
                $oldStatus = $localSubscription->status;

                $localSubscription->update([
                    'status' => $this->mapStripeStatus($stripeSubscription['status']),
                    'expires_at' => Carbon::createFromTimestamp($stripeSubscription['current_period_end']),
                    'trial_ends_at' => $stripeSubscription['trial_end'] ? Carbon::createFromTimestamp($stripeSubscription['trial_end']) : null,
                    'canceled_at' => $stripeSubscription['canceled_at'] ? Carbon::createFromTimestamp($stripeSubscription['canceled_at']) : null,
                ]);

                // Update user role based on subscription status
                $user = $localSubscription->user;
                if (in_array($stripeSubscription['status'], ['active', 'trialing'])) {
                    $user->update(['role' => $localSubscription->plan->user_role ?? 'vip']);
                } elseif (in_array($stripeSubscription['status'], ['canceled', 'unpaid', 'past_due'])) {
                    // Only downgrade if user has no other active subscriptions
                    if (!$user->hasActiveVipSubscription()) {
                        $user->update(['role' => 'visitante']);
                    }
                }

                Log::info('Subscription updated via webhook', [
                    'subscription_id' => $localSubscription->id,
                    'user_id' => $localSubscription->user_id,
                    'old_status' => $oldStatus,
                    'new_status' => $localSubscription->status,
                ]);
            }

            return new Response('Subscription updated', 200);
        } catch (\Exception $e) {
            Log::error('Error handling subscription updated webhook', [
                'stripe_subscription_id' => $stripeSubscription['id'],
                'error' => $e->getMessage(),
            ]);

            return new Response('Error processing webhook', 500);
        }
    }

    /**
     * Handle customer subscription deleted event
     */
    public function handleCustomerSubscriptionDeleted(array $payload)
    {
        $stripeSubscription = $payload['data']['object'];

        Log::info('Customer subscription deleted webhook received', [
            'stripe_subscription_id' => $stripeSubscription['id'],
            'status' => $stripeSubscription['status'],
        ]);

        try {
            $localSubscription = VipSubscription::where('stripe_subscription_id', $stripeSubscription['id'])->first();

            if ($localSubscription) {
                $localSubscription->update([
                    'status' => VipSubscription::STATUS_CANCELED,
                    'canceled_at' => Carbon::now(),
                    'ends_at' => Carbon::now(),
                ]);

                // Downgrade user role if no other active subscriptions
                $user = $localSubscription->user;
                if (!$user->hasActiveVipSubscription()) {
                    $user->update(['role' => 'visitante']);
                }

                Log::info('Subscription deleted via webhook', [
                    'subscription_id' => $localSubscription->id,
                    'user_id' => $localSubscription->user_id,
                ]);
            }

            return new Response('Subscription deleted', 200);
        } catch (\Exception $e) {
            Log::error('Error handling subscription deleted webhook', [
                'stripe_subscription_id' => $stripeSubscription['id'],
                'error' => $e->getMessage(),
            ]);

            return new Response('Error processing webhook', 500);
        }
    }

    /**
     * Handle invoice payment succeeded event
     */
    public function handleInvoicePaymentSucceeded(array $payload)
    {
        $invoice = $payload['data']['object'];

        Log::info('Invoice payment succeeded webhook received', [
            'invoice_id' => $invoice['id'],
            'subscription' => $invoice['subscription'],
            'amount_paid' => $invoice['amount_paid'],
        ]);

        // For subscription renewals, ensure the subscription is active
        if ($invoice['subscription']) {
            $localSubscription = VipSubscription::where('stripe_subscription_id', $invoice['subscription'])->first();

            if ($localSubscription && $localSubscription->status !== VipSubscription::STATUS_ACTIVE) {
                $localSubscription->update([
                    'status' => VipSubscription::STATUS_ACTIVE,
                    'activated_at' => Carbon::now(),
                ]);

                // Ensure user has VIP role
                $user = $localSubscription->user;
                if ($user->role !== ($localSubscription->plan->user_role ?? 'vip')) {
                    $user->update(['role' => $localSubscription->plan->user_role ?? 'vip']);
                }

                Log::info('Subscription reactivated after successful payment', [
                    'subscription_id' => $localSubscription->id,
                    'user_id' => $localSubscription->user_id,
                ]);
            }
        }

        return new Response('Invoice payment handled', 200);
    }

    /**
     * Handle invoice payment failed event
     */
    public function handleInvoicePaymentFailed(array $payload)
    {
        $invoice = $payload['data']['object'];

        Log::warning('Invoice payment failed webhook received', [
            'invoice_id' => $invoice['id'],
            'subscription' => $invoice['subscription'],
            'amount_due' => $invoice['amount_due'],
            'attempt_count' => $invoice['attempt_count'],
        ]);

        // Mark subscription as past due
        if ($invoice['subscription']) {
            $localSubscription = VipSubscription::where('stripe_subscription_id', $invoice['subscription'])->first();

            if ($localSubscription) {
                $localSubscription->update([
                    'status' => VipSubscription::STATUS_PAST_DUE,
                ]);

                Log::info('Subscription marked as past due after failed payment', [
                    'subscription_id' => $localSubscription->id,
                    'user_id' => $localSubscription->user_id,
                    'attempt_count' => $invoice['attempt_count'],
                ]);

                // TODO: Send notification to user about failed payment
                // You can implement email notifications here
            }
        }

        return new Response('Invoice payment failed handled', 200);
    }

    /**
     * Handle shop order payment completion
     */
    private function handleShopOrderPayment(array $session)
    {
        try {
            $orderId = $session['metadata']['order_id'];
            $order = Order::find($orderId);

            if (!$order) {
                Log::warning('Order not found for shop payment webhook', [
                    'session_id' => $session['id'],
                    'order_id' => $orderId,
                ]);
                return new Response('Order not found', 404);
            }

            // Check if order is already paid
            if ($order->isPaid()) {
                Log::info('Order already paid, skipping webhook processing', [
                    'session_id' => $session['id'],
                    'order_id' => $orderId,
                ]);
                return new Response('Order already paid', 200);
            }

            // Mark order as paid
            $order->markAsPaid('stripe', $session['id']);

            Log::info('Order marked as paid via webhook', [
                'session_id' => $session['id'],
                'order_id' => $orderId,
                'user_id' => $order->user_id,
            ]);

            // Process VIP purchase if order contains VIP products
            $vipPurchaseService = app(VipPurchaseService::class);

            if ($vipPurchaseService->orderContainsVipProducts($order)) {
                $vipProcessed = $vipPurchaseService->processVipPurchase($order);

                if ($vipProcessed) {
                    Log::info('VIP purchase processed successfully via webhook', [
                        'session_id' => $session['id'],
                        'order_id' => $orderId,
                        'user_id' => $order->user_id,
                    ]);
                } else {
                    Log::error('Failed to process VIP purchase via webhook', [
                        'session_id' => $session['id'],
                        'order_id' => $orderId,
                        'user_id' => $order->user_id,
                    ]);
                }
            }

            // Process wallet credit if order contains wallet credit products
            $this->processWalletCreditProducts($order, $session['id']);

            return new Response('Shop order payment handled', 200);
        } catch (\Exception $e) {
            Log::error('Error handling shop order payment webhook', [
                'session_id' => $session['id'],
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return new Response('Error processing webhook', 500);
        }
    }

    /**
     * Handle event ticket payment completion
     */
    private function handleEventTicketPayment(array $session)
    {
        try {
            $eventId = $session['metadata']['event_id'];
            $attendeeId = $session['metadata']['attendee_id'];
            $userId = $session['metadata']['user_id'];

            Log::info('Processing event ticket payment webhook', [
                'session_id' => $session['id'],
                'event_id' => $eventId,
                'attendee_id' => $attendeeId,
                'user_id' => $userId,
            ]);

            $attendee = EventAttendee::find($attendeeId);

            if (!$attendee) {
                Log::warning('Event attendee not found for payment webhook', [
                    'session_id' => $session['id'],
                    'attendee_id' => $attendeeId,
                ]);
                return new Response('Attendee not found', 404);
            }

            // Check if payment is already processed
            if ($attendee->payment_status === 'completed') {
                Log::info('Event ticket payment already processed', [
                    'session_id' => $session['id'],
                    'attendee_id' => $attendeeId,
                ]);
                return new Response('Payment already processed', 200);
            }

            // Get payment amount from session
            $amountPaid = $session['amount_total'] / 100; // Convert from cents

            // Mark payment as completed and generate ticket code
            $attendee->markPaymentCompleted($session['id'], 'stripe', $amountPaid);

            Log::info('Event ticket payment processed successfully', [
                'session_id' => $session['id'],
                'attendee_id' => $attendeeId,
                'event_id' => $eventId,
                'user_id' => $userId,
                'amount_paid' => $amountPaid,
                'ticket_code' => $attendee->ticket_code,
            ]);

            // TODO: Send confirmation email to user
            // TODO: Send notification email to event organizers

            return new Response('Event ticket payment handled', 200);
        } catch (\Exception $e) {
            Log::error('Error handling event ticket payment webhook', [
                'session_id' => $session['id'],
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return new Response('Error processing webhook', 500);
        }
    }

    /**
     * Calculate plan days based on interval
     */
    private function calculatePlanDays(SubscriptionPlan $plan): int
    {
        return match ($plan->interval) {
            'day' => $plan->interval_count,
            'week' => $plan->interval_count * 7,
            'month' => $plan->interval_count * 30,
            'year' => $plan->interval_count * 365,
            default => 30,
        };
    }

    /**
     * Process wallet credit products in an order
     */
    private function processWalletCreditProducts(Order $order, string $sessionId)
    {
        try {
            $user = $order->user;
            $wallet = $user->wallet;

            if (!$wallet) {
                Log::error('Wallet not found for user when processing wallet credit products', [
                    'session_id' => $sessionId,
                    'order_id' => $order->id,
                    'user_id' => $user->id,
                ]);
                return;
            }

            $totalCreditAmount = 0;
            $processedProducts = [];

            // Check each order item for wallet credit products
            foreach ($order->items as $item) {
                $product = $item->product;

                if ($product && $product->addsWalletCredit() && $product->hasValidWalletCreditConfig()) {
                    $creditPerUnit = $product->getWalletCreditAmount();
                    $totalCredit = $creditPerUnit * $item->quantity;
                    $totalCreditAmount += $totalCredit;

                    $processedProducts[] = [
                        'product_id' => $product->id,
                        'product_name' => $product->name,
                        'quantity' => $item->quantity,
                        'credit_per_unit' => $creditPerUnit,
                        'total_credit' => $totalCredit,
                    ];
                }
            }

            // Add total credit to wallet if any wallet credit products were found
            if ($totalCreditAmount > 0) {
                $transaction = $wallet->addFunds(
                    $totalCreditAmount,
                    'product_credit',
                    'Crédito de produtos - Pedido #' . $order->id,
                    $order->id,
                    Order::class
                );

                Log::info('Wallet credit from products processed successfully', [
                    'session_id' => $sessionId,
                    'order_id' => $order->id,
                    'user_id' => $user->id,
                    'total_credit_amount' => $totalCreditAmount,
                    'transaction_id' => $transaction->id,
                    'new_balance' => $wallet->balance,
                    'processed_products' => $processedProducts,
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Error processing wallet credit products', [
                'session_id' => $sessionId,
                'order_id' => $order->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * Handle wallet deposit payment completion
     */
    private function handleWalletDepositPayment(array $session)
    {
        try {
            $userId = $session['metadata']['user_id'] ?? null;

            if (!$userId) {
                Log::warning('User ID not found in wallet deposit webhook metadata', [
                    'session_id' => $session['id'],
                    'metadata' => $session['metadata'] ?? [],
                ]);
                return new Response('User ID not found', 400);
            }

            $user = User::find($userId);
            if (!$user) {
                Log::warning('User not found for wallet deposit webhook', [
                    'session_id' => $session['id'],
                    'user_id' => $userId,
                ]);
                return new Response('User not found', 404);
            }

            // Get payment amount from session (convert from cents to reais)
            $amount = $session['amount_total'] / 100;

            // Get or create user's wallet
            $wallet = $user->wallet;
            if (!$wallet) {
                Log::error('Wallet not found for user in deposit webhook', [
                    'session_id' => $session['id'],
                    'user_id' => $userId,
                ]);
                return new Response('Wallet not found', 404);
            }

            // Add funds to wallet
            $transaction = $wallet->addFunds(
                $amount,
                'deposit',
                'Depósito via Stripe - Sessão: ' . $session['id'],
                $session['id'],
                'stripe_session'
            );

            Log::info('Wallet deposit processed successfully via webhook', [
                'session_id' => $session['id'],
                'user_id' => $userId,
                'amount' => $amount,
                'transaction_id' => $transaction->id,
                'new_balance' => $wallet->balance,
            ]);

            return new Response('Wallet deposit handled', 200);
        } catch (\Exception $e) {
            Log::error('Error processing wallet deposit webhook', [
                'session_id' => $session['id'],
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return new Response('Error processing wallet deposit', 500);
        }
    }

    /**
     * Map Stripe subscription status to local status
     */
    private function mapStripeStatus(string $stripeStatus): string
    {
        return match ($stripeStatus) {
            'active' => VipSubscription::STATUS_ACTIVE,
            'trialing' => VipSubscription::STATUS_TRIALING,
            'canceled' => VipSubscription::STATUS_CANCELED,
            'incomplete' => VipSubscription::STATUS_PENDING,
            'incomplete_expired' => VipSubscription::STATUS_EXPIRED,
            'past_due' => VipSubscription::STATUS_PAST_DUE,
            'unpaid' => VipSubscription::STATUS_UNPAID,
            default => VipSubscription::STATUS_PENDING,
        };
    }
}
