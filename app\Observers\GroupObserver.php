<?php

namespace App\Observers;

use App\Models\Group;
use Illuminate\Support\Facades\Log;

class GroupObserver
{
    /**
     * Handle the Group "created" event.
     */
    public function created(Group $group): void
    {
        $this->syncGroupFiles($group);
    }

    /**
     * Handle the Group "updated" event.
     */
    public function updated(Group $group): void
    {
        $this->syncGroupFiles($group);
    }

    /**
     * Handle the Group "deleted" event.
     */
    public function deleted(Group $group): void
    {
        $this->cleanupGroupFiles($group);
    }

    /**
     * Sincroniza arquivos do grupo para public/storage em produção
     */
    private function syncGroupFiles(Group $group): void
    {
        // Apenas em produção
        if (!app()->environment('production')) {
            return;
        }

        try {
            // Sincronizar imagem do grupo
            if ($group->image) {
                $this->syncFile($group->image);
            }

            // Sincronizar imagem de capa
            if ($group->cover_image) {
                $this->syncFile($group->cover_image);
            }
        } catch (\Exception $e) {
            Log::error('Erro ao sincronizar arquivos do grupo', [
                'group_id' => $group->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Remove arquivos do grupo de public/storage
     */
    private function cleanupGroupFiles(Group $group): void
    {
        // Apenas em produção
        if (!app()->environment('production')) {
            return;
        }

        try {
            // Remover imagem do grupo
            if ($group->image) {
                $this->removeFile($group->image);
            }

            // Remover imagem de capa
            if ($group->cover_image) {
                $this->removeFile($group->cover_image);
            }
        } catch (\Exception $e) {
            Log::error('Erro ao limpar arquivos do grupo', [
                'group_id' => $group->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Sincroniza um arquivo específico
     */
    private function syncFile(string $path): void
    {
        $sourcePath = storage_path('app/public/' . $path);
        $publicPath = public_path('storage/' . $path);

        if (!file_exists($sourcePath)) {
            return;
        }

        // Criar diretório se não existir
        $publicDir = dirname($publicPath);
        if (!is_dir($publicDir)) {
            mkdir($publicDir, 0755, true);
        }

        // Copiar arquivo
        if (copy($sourcePath, $publicPath)) {
            Log::info("Arquivo de grupo sincronizado automaticamente: {$path}");
        } else {
            Log::error("Falha ao sincronizar arquivo de grupo: {$path}");
        }
    }

    /**
     * Remove um arquivo específico
     */
    private function removeFile(string $path): void
    {
        $publicPath = public_path('storage/' . $path);

        if (file_exists($publicPath)) {
            if (unlink($publicPath)) {
                Log::info("Arquivo de grupo removido de public/storage: {$path}");
            } else {
                Log::error("Falha ao remover arquivo de grupo: {$path}");
            }
        }
    }
}
