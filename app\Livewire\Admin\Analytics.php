<?php

namespace App\Livewire\Admin;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\UserActivity;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class Analytics extends Component
{
    use WithPagination;

    public $dateRange = '7'; // Últimos 7 dias por padrão
    public $activityType = 'all';
    public $selectedUser = '';
    public $selectedUrl = '';
    
    // Dados para os gráficos
    public $totalPageViews = 0;
    public $totalClicks = 0;
    public $totalUsers = 0;
    public $avgTimeOnPage = 0;
    
    public function mount()
    {
        // Verifica se o usuário é administrador
        if (!auth()->user()->isAdmin()) {
            abort(403, 'Acesso negado');
        }
        
        $this->loadStatistics();
    }

    public function updatedDateRange()
    {
        $this->loadStatistics();
        $this->resetPage();
    }

    public function updatedActivityType()
    {
        $this->resetPage();
    }

    public function updatedSelectedUser()
    {
        $this->resetPage();
    }

    public function updatedSelectedUrl()
    {
        $this->resetPage();
    }

    public function loadStatistics()
    {
        $startDate = $this->getStartDate();
        
        $query = UserActivity::whereBetween('created_at', [$startDate, now()]);
        
        // Estatísticas gerais
        $this->totalPageViews = (clone $query)->where('activity_type', 'page_view')->count();
        $this->totalClicks = (clone $query)->where('activity_type', 'click')->count();
        $this->totalUsers = (clone $query)->distinct('user_id')->count('user_id');
        
        // Tempo médio na página
        $avgTime = (clone $query)
            ->where('activity_type', 'page_view')
            ->whereNotNull('time_on_page')
            ->avg('time_on_page');
        
        $this->avgTimeOnPage = $avgTime ? round($avgTime) : 0;
    }

    public function getStartDate()
    {
        return match($this->dateRange) {
            '1' => now()->subDay(),
            '7' => now()->subDays(7),
            '30' => now()->subDays(30),
            '90' => now()->subDays(90),
            default => now()->subDays(7)
        };
    }

    public function getActivitiesProperty()
    {
        $query = UserActivity::with('user')
            ->whereBetween('created_at', [$this->getStartDate(), now()])
            ->orderBy('created_at', 'desc');

        // Filtros
        if ($this->activityType !== 'all') {
            $query->where('activity_type', $this->activityType);
        }

        if ($this->selectedUser) {
            $query->where('user_id', $this->selectedUser);
        }

        if ($this->selectedUrl) {
            $query->where('url', 'like', '%' . $this->selectedUrl . '%');
        }

        return $query->paginate(20);
    }

    public function getTopPagesProperty()
    {
        return UserActivity::select('url', 'page_title', DB::raw('COUNT(*) as views'))
            ->where('activity_type', 'page_view')
            ->whereBetween('created_at', [$this->getStartDate(), now()])
            ->whereNotNull('url')
            ->groupBy('url', 'page_title')
            ->orderBy('views', 'desc')
            ->limit(10)
            ->get();
    }

    public function getTopUsersProperty()
    {
        return UserActivity::select('user_id', DB::raw('COUNT(*) as activities'))
            ->with('user:id,name,username')
            ->whereBetween('created_at', [$this->getStartDate(), now()])
            ->whereNotNull('user_id')
            ->groupBy('user_id')
            ->orderBy('activities', 'desc')
            ->limit(10)
            ->get();
    }

    public function getActivityByHourProperty()
    {
        return UserActivity::select(
                DB::raw('HOUR(created_at) as hour'),
                DB::raw('COUNT(*) as count')
            )
            ->whereBetween('created_at', [$this->getStartDate(), now()])
            ->groupBy(DB::raw('HOUR(created_at)'))
            ->orderBy('hour')
            ->get()
            ->pluck('count', 'hour')
            ->toArray();
    }

    public function getDeviceStatsProperty()
    {
        return UserActivity::select('device_type', DB::raw('COUNT(*) as count'))
            ->whereBetween('created_at', [$this->getStartDate(), now()])
            ->whereNotNull('device_type')
            ->groupBy('device_type')
            ->get()
            ->pluck('count', 'device_type')
            ->toArray();
    }

    public function getUsersForFilter()
    {
        return User::select('id', 'name', 'username')
            ->whereHas('activities', function($query) {
                $query->whereBetween('created_at', [$this->getStartDate(), now()]);
            })
            ->orderBy('name')
            ->get();
    }

    public function exportData()
    {
        $activities = UserActivity::with('user')
            ->whereBetween('created_at', [$this->getStartDate(), now()])
            ->orderBy('created_at', 'desc')
            ->get();

        $filename = 'analytics_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($activities) {
            $file = fopen('php://output', 'w');

            // Cabeçalhos CSV
            fputcsv($file, [
                'Data/Hora',
                'Usuário',
                'Tipo de Atividade',
                'URL',
                'Título da Página',
                'Elemento',
                'Tempo na Página (s)',
                'Dispositivo',
                'IP'
            ]);

            // Dados
            foreach ($activities as $activity) {
                fputcsv($file, [
                    $activity->created_at->format('d/m/Y H:i:s'),
                    $activity->user ? $activity->user->name : 'Anônimo',
                    $activity->activity_type,
                    $activity->url,
                    $activity->page_title,
                    $activity->element_text,
                    $activity->time_on_page,
                    $activity->device_type,
                    $activity->ip_address
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Gera relatório de páginas mais populares
     */
    public function getPopularPagesReport()
    {
        return UserActivity::select(
                'url',
                'page_title',
                DB::raw('COUNT(*) as total_views'),
                DB::raw('COUNT(DISTINCT user_id) as unique_users'),
                DB::raw('AVG(time_on_page) as avg_time'),
                DB::raw('AVG(scroll_depth) as avg_scroll')
            )
            ->where('activity_type', 'page_view')
            ->whereBetween('created_at', [$this->getStartDate(), now()])
            ->whereNotNull('url')
            ->groupBy('url', 'page_title')
            ->orderBy('total_views', 'desc')
            ->get();
    }

    /**
     * Gera relatório de comportamento por usuário
     */
    public function getUserBehaviorReport()
    {
        return UserActivity::select(
                'user_id',
                DB::raw('COUNT(*) as total_activities'),
                DB::raw('COUNT(DISTINCT url) as pages_visited'),
                DB::raw('SUM(CASE WHEN activity_type = "click" THEN 1 ELSE 0 END) as total_clicks'),
                DB::raw('AVG(time_on_page) as avg_session_time'),
                DB::raw('MAX(created_at) as last_activity')
            )
            ->with('user:id,name,username,role')
            ->whereBetween('created_at', [$this->getStartDate(), now()])
            ->whereNotNull('user_id')
            ->groupBy('user_id')
            ->orderBy('total_activities', 'desc')
            ->get();
    }

    /**
     * Gera relatório de elementos mais clicados
     */
    public function getClickHeatmapReport()
    {
        return UserActivity::select(
                'element_type',
                'element_text',
                'url',
                'page_title',
                DB::raw('COUNT(*) as click_count')
            )
            ->where('activity_type', 'click')
            ->whereBetween('created_at', [$this->getStartDate(), now()])
            ->whereNotNull('element_text')
            ->groupBy('element_type', 'element_text', 'url', 'page_title')
            ->orderBy('click_count', 'desc')
            ->limit(50)
            ->get();
    }

    public function render()
    {
        return view('livewire.admin.analytics', [
            'activities' => $this->activities,
            'topPages' => $this->topPages,
            'topUsers' => $this->topUsers,
            'activityByHour' => $this->activityByHour,
            'deviceStats' => $this->deviceStats,
            'usersForFilter' => $this->getUsersForFilter(),
        ])->layout('components.layouts.app');
    }
}
