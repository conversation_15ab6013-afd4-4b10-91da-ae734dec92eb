<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Helpers\StorageHelper;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class DiagnoseStorageIssues extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'storage:diagnose {--save-report : Salvar relatório em arquivo}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Diagnostica problemas de storage e gera relatório detalhado';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Iniciando diagnóstico de storage...');
        $this->newLine();

        // Gerar relatório completo
        $report = StorageHelper::generateDebugReport();
        
        // Exibir informações básicas
        $this->displayBasicInfo($report);
        
        // Verificar problemas
        $this->checkIssues($report);
        
        // Testar URLs
        $this->testUrls($report);
        
        // Salvar relatório se solicitado
        if ($this->option('save-report')) {
            $this->saveReport($report);
        }
        
        $this->newLine();
        $this->info('✅ Diagnóstico concluído!');
        
        return 0;
    }

    private function displayBasicInfo($report)
    {
        $this->info('📋 Informações Básicas:');
        $this->line("  Ambiente: {$report['environment']}");
        $this->line("  APP_URL: {$report['app_url']}");
        $this->line("  Timestamp: {$report['timestamp']}");
        $this->newLine();
    }

    private function checkIssues($report)
    {
        $this->info('🔧 Verificando Configuração:');
        
        // Verificar configuração do disco
        $config = $report['storage_config'];
        $this->line("  Root: {$config['root']}");
        $this->line("  URL: {$config['url']}");
        $this->line("  Visibilidade: {$config['visibility']}");
        
        // Verificar problemas
        $issues = $report['storage_health'];
        if (empty($issues)) {
            $this->info('  ✅ Nenhum problema encontrado na configuração');
        } else {
            $this->error('  ❌ Problemas encontrados:');
            foreach ($issues as $issue) {
                $this->warn("    • {$issue}");
            }
        }
        
        $this->newLine();
    }

    private function testUrls($report)
    {
        $this->info('🔗 Testando URLs:');
        
        if (empty($report['sample_urls'])) {
            $this->warn('  Nenhuma imagem encontrada para teste');
            return;
        }
        
        foreach ($report['sample_urls'] as $sample) {
            $status = $sample['exists'] ? '✅' : '❌';
            $this->line("  {$status} {$sample['file']}");
            $this->line("      URL: {$sample['url']}");
        }
        
        $this->newLine();
    }

    private function saveReport($report)
    {
        $filename = 'storage-diagnosis-' . now()->format('Y-m-d-H-i-s') . '.json';
        $path = storage_path('logs/' . $filename);
        
        file_put_contents($path, json_encode($report, JSON_PRETTY_PRINT));
        
        $this->info("📄 Relatório salvo em: {$path}");
    }
}
