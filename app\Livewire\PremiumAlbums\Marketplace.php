<?php

namespace App\Livewire\PremiumAlbums;

use App\Models\Album;
use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Auth;

class Marketplace extends Component
{
    use WithPagination;

    public $search = '';
    public $minPrice = '';
    public $maxPrice = '';
    public $sortBy = 'created_at';
    public $sortDirection = 'desc';
    public $creator = '';
    
    public $showFilters = false;

    protected $queryString = [
        'search' => ['except' => ''],
        'minPrice' => ['except' => ''],
        'maxPrice' => ['except' => ''],
        'sortBy' => ['except' => 'created_at'],
        'sortDirection' => ['except' => 'desc'],
        'creator' => ['except' => ''],
    ];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingMinPrice()
    {
        $this->resetPage();
    }

    public function updatingMaxPrice()
    {
        $this->resetPage();
    }

    public function updatingCreator()
    {
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortBy === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $field;
            $this->sortDirection = 'asc';
        }
        $this->resetPage();
    }

    public function clearFilters()
    {
        $this->search = '';
        $this->minPrice = '';
        $this->maxPrice = '';
        $this->creator = '';
        $this->sortBy = 'created_at';
        $this->sortDirection = 'desc';
        $this->resetPage();
    }

    public function toggleFilters()
    {
        $this->showFilters = !$this->showFilters;
    }

    public function getAlbumsProperty()
    {
        $query = Album::availableForPurchase()
                     ->with(['user', 'medias' => function($q) {
                         $q->limit(3);
                     }])
                     ->withCount('reviews');

        // Filtro de busca
        if ($this->search) {
            $query->where(function($q) {
                $q->where('name', 'like', '%' . $this->search . '%')
                  ->orWhere('description', 'like', '%' . $this->search . '%')
                  ->orWhere('tags', 'like', '%' . $this->search . '%');
            });
        }

        // Filtro de preço
        if ($this->minPrice) {
            $query->where('price', '>=', $this->minPrice);
        }

        if ($this->maxPrice) {
            $query->where('price', '<=', $this->maxPrice);
        }

        // Filtro de criador
        if ($this->creator) {
            $query->whereHas('user', function($q) {
                $q->where('name', 'like', '%' . $this->creator . '%')
                  ->orWhere('username', 'like', '%' . $this->creator . '%');
            });
        }

        // Ordenação
        switch ($this->sortBy) {
            case 'price':
                $query->orderBy('price', $this->sortDirection);
                break;
            case 'rating':
                $query->orderBy('rating', $this->sortDirection);
                break;
            case 'sales':
                $query->orderBy('sales_count', $this->sortDirection);
                break;
            case 'name':
                $query->orderBy('name', $this->sortDirection);
                break;
            default:
                $query->orderBy('created_at', $this->sortDirection);
        }

        return $query->paginate(12);
    }

    public function getStatsProperty()
    {
        return [
            'total_albums' => Album::availableForPurchase()->count(),
            'total_creators' => User::whereHas('premiumAlbums', function($q) {
                $q->availableForPurchase();
            })->count(),
            'price_range' => [
                'min' => Album::availableForPurchase()->min('price') ?? 0,
                'max' => Album::availableForPurchase()->max('price') ?? 0,
            ]
        ];
    }

    public function render()
    {
        return view('livewire.premium-albums.marketplace', [
            'albums' => $this->albums,
            'stats' => $this->stats,
        ]);
    }
}
