<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class FixProductionUrls extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'production:fix-urls {--check : Apenas verificar sem corrigir}';

    /**
     * The description of the console command.
     *
     * @var string
     */
    protected $description = 'Corrige configurações de URL para produção no domínio www.swingcuritiba.com.br';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔧 Verificando configurações de URL para produção...');

        $issues = [];
        $fixes = [];

        // Verificar APP_URL
        $this->checkAppUrl($issues, $fixes);

        // Verificar ASSET_URL
        $this->checkAssetUrl($issues, $fixes);

        // Verificar configuração do filesystem
        $this->checkFilesystemConfig($issues, $fixes);

        // Verificar configuração do Vite
        $this->checkViteConfig($issues, $fixes);

        if (empty($issues)) {
            $this->info('✅ Todas as configurações de URL estão corretas!');
            return 0;
        }

        $this->warn('⚠️  Problemas encontrados:');
        foreach ($issues as $issue) {
            $this->line("  - {$issue}");
        }

        if ($this->option('check')) {
            $this->info('🔍 Modo verificação ativado. Nenhuma correção foi aplicada.');
            return 1;
        }

        if ($this->confirm('Deseja aplicar as correções automaticamente?')) {
            $this->applyFixes($fixes);

            // Limpar cache
            $this->info('🧹 Limpando cache...');
            Artisan::call('config:clear');
            Artisan::call('cache:clear');
            Artisan::call('view:clear');

            $this->info('✅ Correções aplicadas com sucesso!');
            $this->info('📝 Lembre-se de atualizar o arquivo .env no servidor de produção.');
        }

        return 0;
    }

    private function checkAppUrl(&$issues, &$fixes)
    {
        $currentUrl = config('app.url');
        $expectedUrl = 'https://www.swingcuritiba.com.br';

        if ($currentUrl !== $expectedUrl) {
            $issues[] = "APP_URL incorreta: {$currentUrl} (esperado: {$expectedUrl})";
            $fixes[] = "Configurar APP_URL={$expectedUrl}";
        } else {
            $this->info('✅ APP_URL: OK');
        }
    }

    private function checkAssetUrl(&$issues, &$fixes)
    {
        $currentAssetUrl = config('app.asset_url');
        $expectedAssetUrl = 'https://www.swingcuritiba.com.br';

        if ($currentAssetUrl !== $expectedAssetUrl) {
            $issues[] = "ASSET_URL incorreta: {$currentAssetUrl} (esperado: {$expectedAssetUrl})";
            $fixes[] = "Configurar ASSET_URL={$expectedAssetUrl}";
        } else {
            $this->info('✅ ASSET_URL: OK');
        }
    }

    private function checkFilesystemConfig(&$issues, &$fixes)
    {
        $publicDiskUrl = config('filesystems.disks.public.url');
        $expectedDiskUrl = 'https://www.swingcuritiba.com.br/storage';

        if ($publicDiskUrl !== $expectedDiskUrl) {
            $issues[] = "URL do disco público incorreta: {$publicDiskUrl} (esperado: {$expectedDiskUrl})";
            $fixes[] = "Configurar URL do disco público";
        } else {
            $this->info('✅ URL do disco público: OK');
        }
    }

    private function checkViteConfig(&$issues, &$fixes)
    {
        $viteConfigPath = base_path('vite.config.js');

        if (!file_exists($viteConfigPath)) {
            $issues[] = "Arquivo vite.config.js não encontrado";
            return;
        }

        $viteConfig = file_get_contents($viteConfigPath);

        if (!str_contains($viteConfig, 'https://www.swingcuritiba.com.br/build/')) {
            $issues[] = "Configuração do Vite não está otimizada para produção";
            $fixes[] = "Atualizar base URL no vite.config.js";
        } else {
            $this->info('✅ Configuração do Vite: OK');
        }
    }

    private function applyFixes($fixes)
    {
        $this->info('🔧 Aplicando correções...');

        foreach ($fixes as $fix) {
            $this->line("  - {$fix}");
        }

        // Forçar configurações corretas no ambiente atual
        if (app()->environment('production')) {
            $this->info('🔧 Aplicando configurações de produção...');

            // Forçar APP_URL
            config(['app.url' => 'https://www.swingcuritiba.com.br']);

            // Forçar ASSET_URL
            config(['app.asset_url' => 'https://www.swingcuritiba.com.br']);

            // Forçar URL do disco público
            config(['filesystems.disks.public.url' => 'https://www.swingcuritiba.com.br/storage']);

            $this->info('✅ Configurações aplicadas temporariamente');
        }

        // Criar arquivo .env.production se não existir
        $envProductionPath = base_path('.env.production');
        if (!file_exists($envProductionPath)) {
            $this->info('📝 Criando arquivo .env.production...');
            // O arquivo já foi criado anteriormente
        }

        $this->info('📋 Instruções para aplicar no servidor:');
        $this->line('1. Copie o arquivo .env.production para .env no servidor');
        $this->line('2. Execute: php artisan config:clear');
        $this->line('3. Execute: php artisan cache:clear');
        $this->line('4. Execute: npm run build');
        $this->line('5. Verifique se o link simbólico storage existe: php artisan storage:link');

        // Testar URLs após aplicar correções
        $this->info('🧪 Testando URLs após correções...');
        $this->line('APP_URL: ' . config('app.url'));
        $this->line('ASSET_URL: ' . config('app.asset_url'));
        $this->line('Storage URL: ' . config('filesystems.disks.public.url'));
    }
}
