<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nova Compra VIP - Pedido #{{ $order->id }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f4f4f4;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #fff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #E60073, #00FFF7);
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 10px 10px 0 0;
            margin: -30px -30px 30px -30px;
        }
        .alert {
            background-color: #E60073;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: bold;
        }
        .info-box {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .info-box h3 {
            margin-top: 0;
            color: #E60073;
        }
        .product-item {
            background-color: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
        }
        .product-name {
            font-weight: bold;
            color: #E60073;
            font-size: 16px;
        }
        .vip-badge {
            background-color: #FFE600;
            color: #333;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            display: inline-block;
            margin: 5px 0;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 12px;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background-color: #E60073;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 5px;
        }
        .btn:hover {
            background-color: #c0055f;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛍️ Nova Compra VIP Realizada</h1>
            <p>Pedido #{{ $order->id }}</p>
        </div>

        <div class="alert">
            ⚠️ ATENÇÃO: Um usuário adquiriu produtos VIP e foi automaticamente promovido!
        </div>

        <div class="info-box">
            <h3>👤 Informações do Cliente</h3>
            <p><strong>Nome:</strong> {{ $user->name }}</p>
            <p><strong>Email:</strong> {{ $user->email }}</p>
            <p><strong>Username:</strong> {{ $user->username }}</p>
            <p><strong>Role Atual:</strong> {{ $user->role }}</p>
            <p><strong>Data da Compra:</strong> {{ $order->created_at->format('d/m/Y H:i:s') }}</p>
        </div>

        <div class="info-box">
            <h3>📦 Detalhes do Pedido</h3>
            <p><strong>Número do Pedido:</strong> #{{ $order->id }}</p>
            <p><strong>Valor Total:</strong> R$ {{ number_format($order->total, 2, ',', '.') }}</p>
            <p><strong>Método de Pagamento:</strong> {{ $order->payment_method }}</p>
            <p><strong>Status:</strong> {{ $order->status }}</p>
        </div>

        <div class="info-box">
            <h3>⭐ Produtos VIP Adquiridos</h3>
            @foreach($vipProducts as $product)
                <div class="product-item">
                    <div class="product-name">{{ $product['name'] }}</div>
                    <div class="vip-badge">{{ $product['vip_duration_days'] }} dias de VIP</div>
                    <p><strong>Preço:</strong> R$ {{ number_format($product['price'], 2, ',', '.') }}</p>
                    @if(!empty($product['vip_benefits']))
                        <p><strong>Benefícios:</strong> {{ $product['vip_benefits'] }}</p>
                    @endif
                </div>
            @endforeach
            
            <div style="margin-top: 15px; padding: 10px; background-color: #E60073; color: white; border-radius: 5px; text-align: center;">
                <strong>Total de Dias VIP Concedidos: {{ $totalVipDays }} dias</strong>
            </div>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <a href="{{ route('admin.users') }}" class="btn">Ver Usuários</a>
            <a href="{{ route('admin.orders') }}" class="btn">Ver Pedidos</a>
        </div>

        <div class="footer">
            <p>Esta é uma notificação automática do sistema {{ config('app.name') }}</p>
            <p>Enviado para: {{ $adminRecipient->name }} ({{ $adminRecipient->email }})</p>
            <p>Data: {{ now()->format('d/m/Y H:i:s') }}</p>
        </div>
    </div>
</body>
</html>
