/* Animações para a Galeria de Mídia */

/* Animação de hover para cards de mídia */
.media-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.media-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Animação de zoom para imagens */
.media-image {
    transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.media-card:hover .media-image {
    transform: scale(1.05);
}

/* Animação de fade para overlays */
.media-overlay {
    transition: opacity 0.3s ease-in-out;
}

/* Animação de pulse para botões de ação */
.pulse-animation {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

/* Animação de slide para lightbox */
.lightbox-enter {
    animation: lightboxEnter 0.3s ease-out;
}

@keyframes lightboxEnter {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Animação de loading para thumbnails */
.loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Animação de bounce para botão flutuante */
.float-bounce {
    animation: floatBounce 3s ease-in-out infinite;
}

@keyframes floatBounce {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* Animação de gradient para headers */
.gradient-animation {
    background-size: 200% 200%;
    animation: gradientShift 4s ease infinite;
}

@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* Animação de typewriter para textos */
.typewriter {
    overflow: hidden;
    border-right: 2px solid;
    white-space: nowrap;
    animation: typewriter 3s steps(40, end), blink 0.75s step-end infinite;
}

@keyframes typewriter {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes blink {
    from, to {
        border-color: transparent;
    }
    50% {
        border-color: currentColor;
    }
}

/* Animação de slide up para cards */
.slide-up {
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Animação de rotate para ícones */
.rotate-on-hover {
    transition: transform 0.3s ease;
}

.rotate-on-hover:hover {
    transform: rotate(360deg);
}

/* Animação de glow para elementos destacados */
.glow-effect {
    box-shadow: 0 0 20px rgba(147, 51, 234, 0.3);
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from {
        box-shadow: 0 0 20px rgba(147, 51, 234, 0.3);
    }
    to {
        box-shadow: 0 0 30px rgba(147, 51, 234, 0.6);
    }
}

/* Responsividade para animações */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Animações específicas para mobile */
@media (max-width: 768px) {
    .media-card:hover {
        transform: none;
    }
    
    .media-card:active {
        transform: scale(0.98);
    }
}
