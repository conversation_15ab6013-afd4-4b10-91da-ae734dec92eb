<?php

namespace App\Observers;

use App\Models\UserCoverPhoto;
use Illuminate\Support\Facades\Log;

class UserCoverPhotoObserver
{
    /**
     * Handle the UserCoverPhoto "created" event.
     */
    public function created(UserCoverPhoto $userCoverPhoto): void
    {
        $this->syncUserCoverPhotoFiles($userCoverPhoto);
    }

    /**
     * Handle the UserCoverPhoto "updated" event.
     */
    public function updated(UserCoverPhoto $userCoverPhoto): void
    {
        $this->syncUserCoverPhotoFiles($userCoverPhoto);
    }

    /**
     * Handle the UserCoverPhoto "deleted" event.
     */
    public function deleted(UserCoverPhoto $userCoverPhoto): void
    {
        $this->cleanupUserCoverPhotoFiles($userCoverPhoto);
    }

    /**
     * Sincroniza arquivos da foto de capa do usuário para public/storage em produção
     */
    private function syncUserCoverPhotoFiles(UserCoverPhoto $userCoverPhoto): void
    {
        // Apenas em produção
        if (!app()->environment('production')) {
            return;
        }

        try {
            // Sincronizar foto original
            if ($userCoverPhoto->photo_path) {
                $this->syncFile($userCoverPhoto->photo_path);
            }

            // Sincronizar foto recortada
            if ($userCoverPhoto->cropped_photo_path) {
                $this->syncFile($userCoverPhoto->cropped_photo_path);
            }
        } catch (\Exception $e) {
            Log::error('Erro ao sincronizar foto de capa do usuário', [
                'user_cover_photo_id' => $userCoverPhoto->id,
                'user_id' => $userCoverPhoto->user_id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Remove arquivos da foto de capa do usuário de public/storage
     */
    private function cleanupUserCoverPhotoFiles(UserCoverPhoto $userCoverPhoto): void
    {
        // Apenas em produção
        if (!app()->environment('production')) {
            return;
        }

        try {
            // Remover foto original
            if ($userCoverPhoto->photo_path) {
                $this->removeFile($userCoverPhoto->photo_path);
            }

            // Remover foto recortada
            if ($userCoverPhoto->cropped_photo_path) {
                $this->removeFile($userCoverPhoto->cropped_photo_path);
            }
        } catch (\Exception $e) {
            Log::error('Erro ao limpar foto de capa do usuário', [
                'user_cover_photo_id' => $userCoverPhoto->id,
                'user_id' => $userCoverPhoto->user_id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Sincroniza um arquivo específico
     */
    private function syncFile(string $path): void
    {
        $sourcePath = storage_path('app/public/' . $path);
        $publicPath = public_path('storage/' . $path);

        if (!file_exists($sourcePath)) {
            return;
        }

        // Criar diretório se não existir
        $publicDir = dirname($publicPath);
        if (!is_dir($publicDir)) {
            mkdir($publicDir, 0755, true);
        }

        // Copiar arquivo
        if (copy($sourcePath, $publicPath)) {
            Log::info("Foto de capa de usuário sincronizada automaticamente: {$path}");
        } else {
            Log::error("Falha ao sincronizar foto de capa de usuário: {$path}");
        }
    }

    /**
     * Remove um arquivo específico
     */
    private function removeFile(string $path): void
    {
        $publicPath = public_path('storage/' . $path);

        if (file_exists($publicPath)) {
            if (unlink($publicPath)) {
                Log::info("Foto de capa de usuário removida de public/storage: {$path}");
            } else {
                Log::error("Falha ao remover foto de capa de usuário: {$path}");
            }
        }
    }
}
