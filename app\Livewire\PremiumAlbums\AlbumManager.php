<?php

namespace App\Livewire\PremiumAlbums;

use App\Models\Album;
use App\Services\PremiumAlbumService;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Auth;

class AlbumManager extends Component
{
    use WithPagination;

    public $search = '';
    public $status = '';
    public $sortBy = 'created_at';
    public $sortDirection = 'desc';
    
    public $showDeleteModal = false;
    public $albumToDelete = null;

    protected $queryString = [
        'search' => ['except' => ''],
        'status' => ['except' => ''],
        'sortBy' => ['except' => 'created_at'],
        'sortDirection' => ['except' => 'desc'],
    ];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingStatus()
    {
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortBy === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $field;
            $this->sortDirection = 'asc';
        }
    }

    public function confirmDelete($albumId)
    {
        $this->albumToDelete = Album::findOrFail($albumId);
        $this->showDeleteModal = true;
    }

    public function deleteAlbum()
    {
        if (!$this->albumToDelete) {
            return;
        }

        try {
            $albumService = new PremiumAlbumService();
            $result = $albumService->deletePremiumAlbum($this->albumToDelete, Auth::user());

            if ($result['success']) {
                session()->flash('success', $result['message']);
                $this->resetPage();
            } else {
                session()->flash('error', 'Erro ao excluir álbum.');
            }

        } catch (\Exception $e) {
            session()->flash('error', $e->getMessage());
        } finally {
            $this->showDeleteModal = false;
            $this->albumToDelete = null;
        }
    }

    public function cancelDelete()
    {
        $this->showDeleteModal = false;
        $this->albumToDelete = null;
    }

    public function getAlbumsProperty()
    {
        $query = Auth::user()->premiumAlbums()
                    ->withCount(['purchases', 'reviews', 'medias']);

        // Filtro de busca
        if ($this->search) {
            $query->where(function($q) {
                $q->where('name', 'like', '%' . $this->search . '%')
                  ->orWhere('description', 'like', '%' . $this->search . '%')
                  ->orWhere('tags', 'like', '%' . $this->search . '%');
            });
        }

        // Filtro de status
        if ($this->status) {
            $query->where('status', $this->status);
        }

        // Ordenação
        $query->orderBy($this->sortBy, $this->sortDirection);

        return $query->paginate(12);
    }

    public function getStatusBadgeClass($status)
    {
        return match($status) {
            'draft' => 'bg-gray-100 text-gray-800',
            'published' => 'bg-green-100 text-green-800',
            'suspended' => 'bg-red-100 text-red-800',
            default => 'bg-gray-100 text-gray-800',
        };
    }

    public function getStatusText($status)
    {
        return match($status) {
            'draft' => 'Rascunho',
            'published' => 'Publicado',
            'suspended' => 'Suspenso',
            default => 'Desconhecido',
        };
    }

    public function render()
    {
        return view('livewire.premium-albums.album-manager', [
            'albums' => $this->albums,
        ]);
    }
}
