<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\User;
use App\Models\UserPoint;
use Illuminate\Support\Facades\Storage;

class RankingLeaderboard extends Component
{
    public $topUsers;
    public $currentUserRank;
    public $totalUsers;
    public $period = 'all'; // all, monthly, weekly

    public function mount()
    {
        $this->loadRanking();
    }

    public function setPeriod($period)
    {
        $this->period = $period;
        $this->loadRanking();
    }

    public function loadRanking()
    {
        $query = User::with(['userPhotos' => function ($query) {
            $query->latest();
        }, 'userPoints'])
            ->whereHas('userPoints')
            ->orderByDesc(function ($query) {
                return $query->select('total_points')
                    ->from('user_points')
                    ->whereColumn('user_points.user_id', 'users.id')
                    ->limit(1);
            });

        // Aplicar filtro de período se necessário
        if ($this->period === 'monthly') {
            $query->whereHas('userPoints', function ($q) {
                $q->where('monthly_points', '>', 0);
            })->orderByDesc(function ($query) {
                return $query->select('monthly_points')
                    ->from('user_points')
                    ->whereColumn('user_points.user_id', 'users.id')
                    ->limit(1);
            });
        } elseif ($this->period === 'weekly') {
            $query->whereHas('userPoints', function ($q) {
                $q->where('weekly_points', '>', 0);
            })->orderByDesc(function ($query) {
                return $query->select('weekly_points')
                    ->from('user_points')
                    ->whereColumn('user_points.user_id', 'users.id')
                    ->limit(1);
            });
        }

        $this->topUsers = $query->take(20)->get();
        $this->totalUsers = User::whereHas('userPoints')->count();

        // Encontrar posição do usuário atual
        $this->findCurrentUserRank();

        // Adicionar avatares
        $this->topUsers->each(function ($user) {
            // Usar a foto atual (is_current), ou a primeira foto como fallback
            $currentOrFirstPhoto = $user->currentPhoto ?? $user->userPhotos->first();
            $user->avatar_url = $currentOrFirstPhoto ? Storage::url($currentOrFirstPhoto->photo_path) : asset('images/default-avatar.jpg');
        });
    }

    private function findCurrentUserRank()
    {
        if (!auth()->check()) {
            $this->currentUserRank = null;
            return;
        }

        $currentUserId = auth()->id();
        $currentUserPoints = UserPoint::where('user_id', $currentUserId)->first();

        if (!$currentUserPoints) {
            $this->currentUserRank = null;
            return;
        }

        $pointsField = match ($this->period) {
            'monthly' => 'monthly_points',
            'weekly' => 'weekly_points',
            default => 'total_points'
        };

        $currentPoints = $currentUserPoints->{$pointsField};

        $rank = UserPoint::where($pointsField, '>', $currentPoints)->count() + 1;

        $this->currentUserRank = [
            'position' => $rank,
            'points' => $currentPoints,
            'user' => auth()->user()
        ];
    }

    public function getPointsForPeriod($user)
    {
        // Since userPoints is now a hasOne relationship, it returns a single model or null
        if (!$user->userPoints) {
            return 0;
        }

        return match ($this->period) {
            'monthly' => $user->userPoints->monthly_points ?? 0,
            'weekly' => $user->userPoints->weekly_points ?? 0,
            default => $user->userPoints->total_points ?? 0
        };
    }

    public function render()
    {
        return view('livewire.ranking-leaderboard');
    }
}
