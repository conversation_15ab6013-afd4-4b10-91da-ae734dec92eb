<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Status do Pedido Atualizado</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f4f4f4;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #fff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #E60073, #00FFF7);
            color: white;
            padding: 30px;
            text-align: center;
            border-radius: 10px 10px 0 0;
            margin: -30px -30px 30px -30px;
        }
        .status-update {
            background-color: #FFE600;
            color: #333;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
            font-size: 18px;
            font-weight: bold;
        }
        .info-box {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }
        .info-box h3 {
            margin-top: 0;
            color: #E60073;
        }
        .status-badge {
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
            display: inline-block;
            margin: 10px 5px;
        }
        .status-pending { background-color: #ffc107; color: #333; }
        .status-processing { background-color: #17a2b8; color: white; }
        .status-completed { background-color: #28a745; color: white; }
        .status-shipped { background-color: #007bff; color: white; }
        .status-delivered { background-color: #28a745; color: white; }
        .status-cancelled { background-color: #dc3545; color: white; }
        .status-refunded { background-color: #6c757d; color: white; }
        .status-failed { background-color: #dc3545; color: white; }
        .order-items {
            background-color: #f8f9fa;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .item {
            border-bottom: 1px solid #dee2e6;
            padding: 10px 0;
        }
        .item:last-child {
            border-bottom: none;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 12px;
        }
        .btn {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(135deg, #E60073, #00FFF7);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            margin: 20px 10px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .status-timeline {
            background-color: #f0f8ff;
            border-left: 4px solid #00FFF7;
            padding: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📦 Status do Pedido Atualizado</h1>
            <p>Olá, {{ $user->name }}!</p>
        </div>

        <div class="status-update">
            🔄 O status do seu pedido foi atualizado!
        </div>

        <div class="info-box">
            <h3>📋 Informações do Pedido</h3>
            <p><strong>Número do Pedido:</strong> #{{ $order->id }}</p>
            <p><strong>Data do Pedido:</strong> {{ $order->created_at->format('d/m/Y H:i:s') }}</p>
            <p><strong>Valor Total:</strong> R$ {{ number_format($order->total, 2, ',', '.') }}</p>
        </div>

        <div class="status-timeline">
            <h3>📊 Mudança de Status</h3>
            <p>
                <strong>Status Anterior:</strong> 
                <span class="status-badge status-{{ $oldStatus }}">
                    @switch($oldStatus)
                        @case('pending') Pendente @break
                        @case('processing') Processando @break
                        @case('completed') Concluído @break
                        @case('shipped') Enviado @break
                        @case('delivered') Entregue @break
                        @case('cancelled') Cancelado @break
                        @case('refunded') Reembolsado @break
                        @case('failed') Falhou @break
                        @default {{ ucfirst($oldStatus) }}
                    @endswitch
                </span>
            </p>
            <p style="text-align: center; font-size: 24px; margin: 10px 0;">⬇️</p>
            <p>
                <strong>Status Atual:</strong> 
                <span class="status-badge status-{{ $newStatus }}">
                    @switch($newStatus)
                        @case('pending') Pendente @break
                        @case('processing') Processando @break
                        @case('completed') Concluído @break
                        @case('shipped') Enviado @break
                        @case('delivered') Entregue @break
                        @case('cancelled') Cancelado @break
                        @case('refunded') Reembolsado @break
                        @case('failed') Falhou @break
                        @default {{ ucfirst($newStatus) }}
                    @endswitch
                </span>
            </p>
        </div>

        @if($order->items && $order->items->count() > 0)
        <div class="info-box">
            <h3>🛍️ Itens do Pedido</h3>
            <div class="order-items">
                @foreach($order->items as $item)
                <div class="item">
                    <strong>{{ $item->product->name ?? 'Produto' }}</strong><br>
                    Quantidade: {{ $item->quantity }} | 
                    Preço: R$ {{ number_format($item->price, 2, ',', '.') }}
                </div>
                @endforeach
            </div>
        </div>
        @endif

        @switch($newStatus)
            @case('processing')
                <div class="info-box">
                    <h3>⚙️ Seu pedido está sendo processado</h3>
                    <p>Estamos preparando seus itens para envio. Em breve você receberá uma nova atualização.</p>
                </div>
                @break
            @case('shipped')
                <div class="info-box">
                    <h3>🚚 Seu pedido foi enviado</h3>
                    <p>Seu pedido está a caminho! 
                    @if($order->tracking_number)
                        <br><strong>Código de rastreamento:</strong> {{ $order->tracking_number }}
                    @endif
                    </p>
                </div>
                @break
            @case('delivered')
                <div class="info-box">
                    <h3>✅ Pedido entregue</h3>
                    <p>Seu pedido foi entregue com sucesso! Esperamos que você esteja satisfeito com sua compra.</p>
                </div>
                @break
            @case('cancelled')
                <div class="info-box">
                    <h3>❌ Pedido cancelado</h3>
                    <p>Seu pedido foi cancelado. Se você não solicitou este cancelamento, entre em contato conosco.</p>
                </div>
                @break
            @case('refunded')
                <div class="info-box">
                    <h3>💰 Reembolso processado</h3>
                    <p>O reembolso do seu pedido foi processado. O valor será creditado em sua conta em até 5 dias úteis.</p>
                </div>
                @break
        @endswitch

        <div style="text-align: center; margin: 30px 0;">
            <a href="{{ route('shop.user.orders') }}" class="btn">Ver Meus Pedidos</a>
            @if($newStatus === 'delivered')
                <a href="{{ route('shop.index') }}" class="btn">Continuar Comprando</a>
            @endif
        </div>

        <div class="footer">
            <p>Obrigado por escolher o {{ config('app.name') }}!</p>
            <p>Se tiver dúvidas, entre em contato conosco.</p>
            <p>Data: {{ now()->format('d/m/Y H:i:s') }}</p>
        </div>
    </div>
</body>
</html>
