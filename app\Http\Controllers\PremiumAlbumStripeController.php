<?php

namespace App\Http\Controllers;

use App\Models\AlbumPurchase;
use App\Services\AlbumPurchaseService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Stripe\Stripe;
use Stripe\Checkout\Session;
use Stripe\Webhook;

class PremiumAlbumStripeController extends Controller
{
    protected $purchaseService;

    public function __construct(AlbumPurchaseService $purchaseService)
    {
        $this->purchaseService = $purchaseService;
        Stripe::setApiKey(config('services.stripe.secret'));
    }

    /**
     * Criar sessão de checkout do Stripe.
     */
    public function createCheckoutSession(Request $request)
    {
        $request->validate([
            'purchase_id' => 'required|exists:album_purchases,id'
        ]);

        $user = Auth::user();
        $purchase = AlbumPurchase::findOrFail($request->purchase_id);

        // Verificar se a compra pertence ao usuário
        if ($purchase->buyer_id !== $user->id) {
            abort(403, 'Acesso negado.');
        }

        // Verificar se ainda está pendente
        if ($purchase->status !== AlbumPurchase::STATUS_PENDING) {
            return response()->json([
                'success' => false,
                'message' => 'Esta compra já foi processada.'
            ], 400);
        }

        try {
            $session = Session::create([
                'payment_method_types' => ['card'],
                'line_items' => [[
                    'price_data' => [
                        'currency' => 'brl',
                        'product_data' => [
                            'name' => $purchase->album->name,
                            'description' => $purchase->album->description,
                        ],
                        'unit_amount' => $purchase->amount * 100, // Stripe usa centavos
                    ],
                    'quantity' => 1,
                ]],
                'mode' => 'payment',
                'success_url' => route('premium-albums.stripe.success', ['session_id' => '{CHECKOUT_SESSION_ID}']),
                'cancel_url' => route('premium-albums.show', $purchase->album),
                'metadata' => [
                    'purchase_id' => $purchase->id,
                    'album_id' => $purchase->album_id,
                    'buyer_id' => $purchase->buyer_id,
                ],
                'customer_email' => $user->email,
            ]);

            // Salvar session ID na compra
            $purchase->update(['stripe_session_id' => $session->id]);

            return response()->json([
                'success' => true,
                'checkout_url' => $session->url
            ]);

        } catch (\Exception $e) {
            Log::error('Erro ao criar sessão Stripe', [
                'purchase_id' => $purchase->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Erro ao processar pagamento. Tente novamente.'
            ], 500);
        }
    }

    /**
     * Página de sucesso após pagamento.
     */
    public function success(Request $request)
    {
        $sessionId = $request->get('session_id');
        
        if (!$sessionId) {
            return redirect()->route('premium-albums.marketplace')
                           ->with('error', 'Sessão inválida.');
        }

        try {
            $session = Session::retrieve($sessionId);
            $purchase = AlbumPurchase::where('stripe_session_id', $sessionId)->first();

            if (!$purchase) {
                return redirect()->route('premium-albums.marketplace')
                               ->with('error', 'Compra não encontrada.');
            }

            // Se o pagamento foi bem-sucedido mas a compra ainda está pendente
            if ($session->payment_status === 'paid' && $purchase->status === AlbumPurchase::STATUS_PENDING) {
                $this->purchaseService->handleStripeWebhook($sessionId, $session->payment_intent);
            }

            return redirect()->route('premium-albums.show', $purchase->album)
                           ->with('success', 'Pagamento realizado com sucesso! Você agora tem acesso ao álbum.');

        } catch (\Exception $e) {
            Log::error('Erro na página de sucesso Stripe', [
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);

            return redirect()->route('premium-albums.marketplace')
                           ->with('error', 'Erro ao processar pagamento.');
        }
    }

    /**
     * Webhook do Stripe.
     */
    public function webhook(Request $request)
    {
        $payload = $request->getContent();
        $sigHeader = $request->header('Stripe-Signature');
        $endpointSecret = config('services.stripe.webhook_secret');

        try {
            $event = Webhook::constructEvent($payload, $sigHeader, $endpointSecret);
        } catch (\UnexpectedValueException $e) {
            Log::error('Payload inválido no webhook Stripe', ['error' => $e->getMessage()]);
            return response('Invalid payload', 400);
        } catch (\Stripe\Exception\SignatureVerificationException $e) {
            Log::error('Assinatura inválida no webhook Stripe', ['error' => $e->getMessage()]);
            return response('Invalid signature', 400);
        }

        // Processar evento
        try {
            switch ($event['type']) {
                case 'checkout.session.completed':
                    $this->handleCheckoutCompleted($event['data']['object']);
                    break;
                
                case 'payment_intent.succeeded':
                    $this->handlePaymentSucceeded($event['data']['object']);
                    break;
                
                case 'payment_intent.payment_failed':
                    $this->handlePaymentFailed($event['data']['object']);
                    break;
                
                default:
                    Log::info('Evento Stripe não tratado', ['type' => $event['type']]);
            }

            return response('Webhook handled', 200);

        } catch (\Exception $e) {
            Log::error('Erro ao processar webhook Stripe', [
                'event_type' => $event['type'],
                'error' => $e->getMessage()
            ]);

            return response('Webhook error', 500);
        }
    }

    /**
     * Processa checkout completado.
     */
    private function handleCheckoutCompleted($session)
    {
        $purchase = AlbumPurchase::where('stripe_session_id', $session['id'])->first();
        
        if (!$purchase) {
            Log::error('Compra não encontrada para sessão Stripe', ['session_id' => $session['id']]);
            return;
        }

        if ($purchase->status === AlbumPurchase::STATUS_PENDING) {
            $this->purchaseService->handleStripeWebhook($session['id'], $session['payment_intent']);
            
            Log::info('Compra de álbum completada via Stripe', [
                'purchase_id' => $purchase->id,
                'session_id' => $session['id']
            ]);
        }
    }

    /**
     * Processa pagamento bem-sucedido.
     */
    private function handlePaymentSucceeded($paymentIntent)
    {
        Log::info('Pagamento Stripe bem-sucedido', ['payment_intent' => $paymentIntent['id']]);
    }

    /**
     * Processa falha no pagamento.
     */
    private function handlePaymentFailed($paymentIntent)
    {
        $purchase = AlbumPurchase::where('stripe_payment_intent_id', $paymentIntent['id'])->first();
        
        if ($purchase) {
            $purchase->markAsFailed('Falha no pagamento Stripe');
            
            Log::warning('Pagamento de álbum falhou', [
                'purchase_id' => $purchase->id,
                'payment_intent' => $paymentIntent['id']
            ]);
        }
    }
}
