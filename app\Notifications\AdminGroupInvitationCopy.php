<?php

namespace App\Notifications;

use App\Models\GroupInvitation;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class AdminGroupInvitationCopy extends Notification
{
    use Queueable;

    protected $invitation;

    /**
     * Create a new notification instance.
     */
    public function __construct(GroupInvitation $invitation)
    {
        $this->invitation = $invitation;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via($notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable): MailMessage
    {
        $group = $this->invitation->group;
        $inviter = $this->invitation->inviter;
        $invitedUser = $this->invitation->user;

        return (new MailMessage)
            ->subject('[ADMIN COPY] Convite para Grupo - ' . $group->name)
            ->view('emails.admin-group-invitation-copy', [
                'invitation' => $this->invitation,
                'group' => $group,
                'inviter' => $inviter,
                'invitedUser' => $invitedUser,
                'adminRecipient' => $notifiable,
            ]);
    }
}
