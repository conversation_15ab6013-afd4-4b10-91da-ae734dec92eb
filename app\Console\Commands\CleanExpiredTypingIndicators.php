<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\TypingIndicator;

class CleanExpiredTypingIndicators extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'typing:clean';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Limpa indicadores de digitação expirados';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $deletedCount = TypingIndicator::cleanExpired();

        $this->info("Limpeza concluída. {$deletedCount} indicadores expirados foram removidos.");

        return Command::SUCCESS;
    }
}
