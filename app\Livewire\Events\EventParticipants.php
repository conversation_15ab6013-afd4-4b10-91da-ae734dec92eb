<?php

namespace App\Livewire\Events;

use App\Models\Event;
use App\Models\EventAttendee;
use Livewire\Component;
use Livewire\WithPagination;

class EventParticipants extends Component
{
    use WithPagination;

    public Event $event;
    public $search = '';
    public $perPage = 20;

    protected $queryString = [
        'search' => ['except' => ''],
    ];

    public function mount(Event $event)
    {
        $this->event = $event;
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function render()
    {
        $query = EventAttendee::where('event_id', $this->event->id)
            ->where('status', 'confirmed') // Apenas participantes confirmados
            ->where('payment_status', 'completed') // Apenas com pagamento confirmado
            ->with(['user' => function ($query) {
                // Selecionar apenas campos públicos do usuário
                $query->select('id', 'name', 'username', 'avatar');
            }]);

        // Aplicar busca se fornecida
        if ($this->search) {
            $query->whereHas('user', function ($q) {
                $q->where('name', 'like', '%' . $this->search . '%')
                  ->orWhere('username', 'like', '%' . $this->search . '%');
            });
        }

        // Ordenar por data de inscrição
        $query->orderBy('created_at', 'asc');

        $participants = $query->paginate($this->perPage);

        // Estatísticas públicas
        $stats = [
            'total_confirmed' => $this->event->attendees()
                ->where('status', 'confirmed')
                ->where('payment_status', 'completed')
                ->count(),
            'capacity' => $this->event->capacity,
            'spots_remaining' => $this->event->capacity ? 
                max(0, $this->event->capacity - $this->event->attendees()
                    ->where('status', 'confirmed')
                    ->where('payment_status', 'completed')
                    ->count()) : null,
        ];

        return view('livewire.events.event-participants', [
            'participants' => $participants,
            'stats' => $stats,
        ]);
    }
}
