// Importar Livewire e Alpine juntos (forma correta para Livewire 3)
import { Livewire, Alpine } from '../../vendor/livewire/livewire/dist/livewire.esm';

// Registrar plugins ou diretivas Alpine aqui se necessário
// Alpine.plugin(...)

// Inicializar Livewire (que automaticamente inicializa o Alpine)
Livewire.start();

// Importar o tooltip handler
import './tooltip-handler';

// Importar o live stream camera handler
import './live-stream-camera';

// Importar o live stream broadcast
import './live-stream-broadcast';

// Importar o mention hashtag handler
import './mention-hashtag-handler';

// Importar o file upload handler
import './file-upload-handler';

// Importar o activity tracker
import './activity-tracker';