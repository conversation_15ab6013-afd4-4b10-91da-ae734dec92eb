<div class="max-w-7xl mx-auto p-6">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-300 mb-2">Marketplace Premium</h1>
        <p class="text-gray-400">Descubra álbuns exclusivos de nossos criadores</p>
        
        <!-- Stats -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
            <div class="bg-zinc-800 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-purple-400">{{ $stats['total_albums'] }}</div>
                <div class="text-sm text-gray-400">Álbuns Disponíveis</div>
            </div>
            <div class="bg-zinc-800 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-cyan-400">{{ $stats['total_creators'] }}</div>
                <div class="text-sm text-gray-400">Criadores Ativos</div>
            </div>
            <div class="bg-zinc-800 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-green-400">R$ {{ number_format($stats['price_range']['min'], 2, ',', '.') }}</div>
                <div class="text-sm text-gray-400">Preço Mínimo</div>
            </div>
            <div class="bg-zinc-800 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-yellow-400">R$ {{ number_format($stats['price_range']['max'], 2, ',', '.') }}</div>
                <div class="text-sm text-gray-400">Preço Máximo</div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg mb-6">
        <div class="px-6 py-4">
            <!-- Main Search -->
            <div class="flex flex-col md:flex-row gap-4 mb-4">
                <div class="flex-1">
                    <flux:input 
                        wire:model.live.debounce.300ms="search" 
                        placeholder="Buscar álbuns, criadores ou tags..."
                        class="w-full"
                    >
                        <x-slot name="iconTrailing">
                            <flux:icon name="magnifying-glass" class="w-5 h-5" />
                        </x-slot>
                    </flux:input>
                </div>
                
                <flux:button 
                    wire:click="toggleFilters"
                    variant="outline"
                    class="md:w-auto"
                >
                    <flux:icon name="adjustments-horizontal" class="w-4 h-4 mr-2" />
                    Filtros
                </flux:button>
                
                @if($search || $minPrice || $maxPrice || $creator)
                <flux:button 
                    wire:click="clearFilters"
                    color="red"
                    variant="outline"
                >
                    Limpar
                </flux:button>
                @endif
            </div>

            <!-- Advanced Filters -->
            @if($showFilters)
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 pt-4 border-t border-gray-700">
                <div>
                    <flux:label for="minPrice">Preço Mínimo</flux:label>
                    <flux:input 
                        wire:model.live="minPrice" 
                        id="minPrice" 
                        type="number" 
                        step="0.01"
                        placeholder="R$ 0,00"
                        class="mt-1"
                    />
                </div>
                
                <div>
                    <flux:label for="maxPrice">Preço Máximo</flux:label>
                    <flux:input 
                        wire:model.live="maxPrice" 
                        id="maxPrice" 
                        type="number" 
                        step="0.01"
                        placeholder="R$ 999,00"
                        class="mt-1"
                    />
                </div>
                
                <div>
                    <flux:label for="creator">Criador</flux:label>
                    <flux:input 
                        wire:model.live="creator" 
                        id="creator" 
                        placeholder="Nome do criador..."
                        class="mt-1"
                    />
                </div>
                
                <div>
                    <flux:label for="sort">Ordenar por</flux:label>
                    <flux:select wire:model.live="sortBy" id="sort" class="mt-1">
                        <option value="created_at">Mais Recentes</option>
                        <option value="price">Preço</option>
                        <option value="rating">Avaliação</option>
                        <option value="sales">Mais Vendidos</option>
                        <option value="name">Nome</option>
                    </flux:select>
                </div>
            </div>
            @endif
        </div>
    </div>

    <!-- Albums Grid -->
    @if($albums->count() > 0)
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
        @foreach($albums as $album)
        <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg overflow-hidden hover:shadow-lg transition-shadow cursor-pointer group">
            <a href="{{ route('premium-albums.show', $album) }}" class="block">
                <!-- Album Cover -->
                <div class="relative h-48 bg-zinc-800">
                    @if($album->medias->count() > 0)
                        @php $firstMedia = $album->medias->first(); @endphp
                        @if($firstMedia->type === 'photo')
                            <img
                                src="{{ route('premium-albums.media', [$album, $firstMedia->id]) }}"
                                alt="{{ $album->name }}"
                                class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                            />
                        @else
                            <div class="w-full h-full flex items-center justify-center">
                                <flux:icon name="film" class="w-12 h-12 text-gray-400" />
                            </div>
                        @endif
                    @else
                        <div class="w-full h-full flex items-center justify-center">
                            <flux:icon name="photo" class="w-12 h-12 text-gray-400" />
                        </div>
                    @endif

                    <!-- Premium Badge -->
                    <div class="absolute top-2 left-2">
                        <span class="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-2 py-1 text-xs font-bold rounded-full">
                            PREMIUM
                        </span>
                    </div>

                    <!-- Media Count -->
                    <div class="absolute bottom-2 right-2">
                        <span class="bg-black bg-opacity-75 text-white px-2 py-1 text-xs rounded">
                            {{ $album->medias->count() }} {{ $album->medias->count() === 1 ? 'arquivo' : 'arquivos' }}
                        </span>
                    </div>

                    <!-- Preview Overlay -->
                    <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
                        <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <flux:icon name="eye" class="w-8 h-8 text-white" />
                        </div>
                    </div>
                </div>

                <div class="p-4">
                    <!-- Album Info -->
                    <h3 class="font-semibold text-gray-300 mb-2 truncate group-hover:text-purple-400 transition-colors">
                        {{ $album->name }}
                    </h3>
                    
                    <!-- Creator -->
                    <div class="flex items-center mb-3">
                        <img 
                            src="{{ $album->user->avatar_url ?? '/default-avatar.png' }}" 
                            alt="{{ $album->user->name }}"
                            class="w-6 h-6 rounded-full mr-2"
                        />
                        <span class="text-sm text-gray-400">{{ $album->user->name }}</span>
                    </div>

                    <!-- Stats -->
                    <div class="flex items-center justify-between text-sm text-gray-400 mb-3">
                        <div class="flex items-center">
                            @if($album->rating > 0)
                            <div class="flex items-center mr-3">
                                <flux:icon name="star" class="w-4 h-4 text-yellow-400 mr-1" />
                                <span>{{ number_format($album->rating, 1) }}</span>
                            </div>
                            @endif
                            
                            <div class="flex items-center">
                                <flux:icon name="shopping-cart" class="w-4 h-4 mr-1" />
                                <span>{{ $album->sales_count }}</span>
                            </div>
                        </div>
                        
                        <div class="text-right">
                            <div class="text-lg font-bold text-purple-400">
                                R$ {{ number_format($album->price, 2, ',', '.') }}
                            </div>
                        </div>
                    </div>

                    <!-- Tags -->
                    @if($album->tags)
                    <div class="flex flex-wrap gap-1">
                        @foreach(explode(',', $album->tags) as $tag)
                        <span class="bg-zinc-700 text-gray-300 px-2 py-1 text-xs rounded">
                            #{{ trim($tag) }}
                        </span>
                        @endforeach
                    </div>
                    @endif
                </div>
            </a>
        </div>
        @endforeach
    </div>

    <!-- Pagination -->
    {{ $albums->links() }}
    @else
    <!-- Empty State -->
    <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg text-center py-12">
        <flux:icon name="photo" class="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-300 mb-2">Nenhum álbum encontrado</h3>
        <p class="text-gray-400 mb-6">
            @if($search || $minPrice || $maxPrice || $creator)
                Tente ajustar os filtros de busca.
            @else
                Não há álbuns premium disponíveis no momento.
            @endif
        </p>
        @if($search || $minPrice || $maxPrice || $creator)
        <flux:button wire:click="clearFilters" color="purple">
            Limpar Filtros
        </flux:button>
        @endif
    </div>
    @endif
</div>
