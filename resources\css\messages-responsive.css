/* Messages Area Responsive Improvements */

/* Base styles for messages container */
.messages-container {
    height: calc(100vh - 8rem);
    min-height: 500px;
}

/* Conversations sidebar responsive */
.conversations-sidebar {
    transition: all 0.3s ease;
}

/* Mobile-first approach */
@media (max-width: 640px) {
    .messages-container {
        height: calc(100vh - 4rem);
        min-height: 500px;
    }

    /* Mobile layout: horizontal avatars + vertical chat */
    .messages-layout {
        flex-direction: column;
        gap: 0;
    }

    /* Conversations as horizontal scroll on mobile */
    .conversations-sidebar {
        width: 100%;
        height: 100px;
        border-radius: 0.5rem 0.5rem 0 0;
        flex-direction: row !important;
        overflow-x: auto !important;
        overflow-y: hidden !important;
        padding: 0.75rem 0.5rem;
        background: linear-gradient(135deg, rgba(139, 92, 246, 0.05) 0%, rgba(124, 58, 237, 0.05) 100%);
        border-bottom: 1px solid rgba(139, 92, 246, 0.2);
        display: flex !important;
    }

    /* Hide search on mobile horizontal mode */
    .conversations-sidebar .search-container {
        display: none;
    }

    /* Chat area takes most space on mobile */
    .chat-area {
        width: 100%;
        height: calc(100vh - 8rem);
        border-radius: 0 0 0.5rem 0.5rem;
        min-height: 400px;
    }

    /* Message bubbles smaller on mobile */
    .message-bubble {
        max-width: 85%;
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
    }

    /* Input area adjustments */
    .message-input-area {
        padding: 0.75rem;
        gap: 0.5rem;
    }

    .message-input {
        font-size: 16px;
        /* Prevent zoom on iOS */
        padding: 0.75rem;
    }

    /* Horizontal conversation items for mobile */
    .conversation-item {
        flex-direction: column;
        align-items: center;
        padding: 0.5rem;
        min-width: 70px;
        max-width: 70px;
        height: 85px;
        margin-right: 0.5rem;
        border-radius: 0.75rem;
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(10px);
        border: 2px solid transparent;
        transition: all 0.3s ease;
    }

    .conversation-item:hover,
    .conversation-item.active {
        border-color: #8B5CF6;
        background: rgba(139, 92, 246, 0.1);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
    }

    .conversation-avatar {
        width: 3rem;
        height: 3rem;
        border: 2px solid rgba(139, 92, 246, 0.3);
        margin-bottom: 0.25rem;
    }

    .conversation-item.active .conversation-avatar {
        border-color: #8B5CF6;
        box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
    }

    .conversation-content {
        margin-left: 0;
        text-align: center;
        width: 100%;
    }

    .conversation-name {
        font-size: 0.625rem;
        font-weight: 600;
        line-height: 1;
        max-width: 60px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    /* Hide preview and time on mobile horizontal */
    .conversation-preview,
    .conversation-time {
        display: none;
    }

    /* Search input */
    .search-input {
        font-size: 16px;
        /* Prevent zoom on iOS */
        padding: 0.5rem 0.75rem;
    }

    /* Status indicator */
    .status-indicator {
        width: 0.625rem;
        height: 0.625rem;
    }

    /* Unread badge for mobile horizontal */
    .unread-badge {
        width: 1rem;
        height: 1rem;
        font-size: 0.5rem;
        position: absolute;
        top: -2px;
        right: -2px;
        border: 1px solid white;
    }

    /* Horizontal scroll styling for mobile */
    .conversations-sidebar::-webkit-scrollbar {
        height: 4px;
    }

    .conversations-sidebar::-webkit-scrollbar-track {
        background: rgba(139, 92, 246, 0.1);
        border-radius: 2px;
    }

    .conversations-sidebar::-webkit-scrollbar-thumb {
        background: rgba(139, 92, 246, 0.4);
        border-radius: 2px;
    }

    .conversations-sidebar::-webkit-scrollbar-thumb:hover {
        background: rgba(139, 92, 246, 0.6);
    }

    /* Scroll indicators */
    .conversations-sidebar::before,
    .conversations-sidebar::after {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        width: 20px;
        pointer-events: none;
        z-index: 10;
    }

    .conversations-sidebar::before {
        left: 0;
        background: linear-gradient(to right, rgba(255, 255, 255, 0.8), transparent);
    }

    .conversations-sidebar::after {
        right: 0;
        background: linear-gradient(to left, rgba(255, 255, 255, 0.8), transparent);
    }

    /* Container for horizontal conversations */
    .conversations-list,
    .overflow-y-auto {
        display: flex !important;
        flex-direction: row !important;
        gap: 0.5rem !important;
        padding: 0 !important;
        min-width: max-content !important;
        overflow-x: auto !important;
        overflow-y: hidden !important;
        height: 100% !important;
    }

    /* Force horizontal layout for conversation container */
    .conversations-sidebar .overflow-y-auto {
        flex-grow: 0 !important;
        width: 100% !important;
    }
}

@media (min-width: 641px) and (max-width: 768px) {

    /* Tablet adjustments */
    .messages-container {
        height: calc(100vh - 7rem);
    }

    .conversations-sidebar {
        width: 40%;
    }

    .chat-area {
        width: 60%;
    }

    .message-bubble {
        max-width: 75%;
        padding: 0.625rem 0.875rem;
    }

    .conversation-preview {
        max-width: 180px;
    }
}

@media (min-width: 769px) and (max-width: 1024px) {

    /* Small desktop/large tablet */
    .conversations-sidebar {
        width: 35%;
    }

    .chat-area {
        width: 65%;
    }

    .message-bubble {
        max-width: 70%;
    }

    .conversation-preview {
        max-width: 200px;
    }
}

@media (min-width: 1025px) {

    /* Large desktop optimizations */
    .conversations-sidebar {
        width: 33.333333%;
        max-width: 400px;
    }

    .chat-area {
        width: 66.666667%;
    }

    .message-bubble {
        max-width: 65%;
        padding: 0.75rem 1rem;
    }

    .conversation-preview {
        max-width: 250px;
    }

    /* Enhanced hover effects on desktop */
    .conversation-item:hover {
        transform: translateX(2px);
        transition: transform 0.2s ease;
    }

    .message-input:focus {
        box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
    }
}

/* Touch-friendly improvements */
@media (hover: none) and (pointer: coarse) {

    /* Touch devices */
    .conversation-item {
        min-height: 3.5rem;
        padding: 1rem 0.75rem;
    }

    .message-input {
        min-height: 2.75rem;
        padding: 0.875rem 1rem;
    }

    .send-button {
        min-width: 2.75rem;
        min-height: 2.75rem;
    }

    /* Larger tap targets */
    .status-selector button {
        min-height: 2.5rem;
        padding: 0.5rem 0.75rem;
    }
}

/* Dark mode specific adjustments */
.dark .conversations-sidebar {
    background: rgb(39 39 42);
    border-color: rgb(63 63 70);
}

.dark .chat-area {
    background: rgb(39 39 42);
    border-color: rgb(63 63 70);
}

.dark .message-bubble.sent {
    background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
}

.dark .message-bubble.received {
    background: rgb(63 63 70);
    color: rgb(244 244 245);
}

/* Dark mode for mobile horizontal layout */
@media (max-width: 640px) {
    .dark .conversations-sidebar {
        background: linear-gradient(135deg, rgba(39, 39, 42, 0.95) 0%, rgba(63, 63, 70, 0.95) 100%);
        border-bottom-color: rgba(139, 92, 246, 0.3);
    }

    .dark .conversation-item {
        background: rgba(63, 63, 70, 0.8);
        border-color: rgba(139, 92, 246, 0.2);
    }

    .dark .conversation-item:hover,
    .dark .conversation-item.active {
        background: rgba(139, 92, 246, 0.2);
        border-color: #8B5CF6;
    }

    .dark .conversation-name {
        color: rgb(244 244 245);
    }

    .dark .conversations-sidebar::before {
        background: linear-gradient(to right, rgba(39, 39, 42, 0.8), transparent);
    }

    .dark .conversations-sidebar::after {
        background: linear-gradient(to left, rgba(39, 39, 42, 0.8), transparent);
    }

    .dark .conversations-sidebar::-webkit-scrollbar-track {
        background: rgba(139, 92, 246, 0.2);
    }

    .dark .conversations-sidebar::-webkit-scrollbar-thumb {
        background: rgba(139, 92, 246, 0.5);
    }
}

/* Scroll improvements */
.messages-scroll-area {
    scroll-behavior: smooth;
    scrollbar-width: thin;
    scrollbar-color: rgba(139, 92, 246, 0.3) transparent;
}

.messages-scroll-area::-webkit-scrollbar {
    width: 6px;
}

.messages-scroll-area::-webkit-scrollbar-track {
    background: transparent;
}

.messages-scroll-area::-webkit-scrollbar-thumb {
    background: rgba(139, 92, 246, 0.3);
    border-radius: 3px;
}

.messages-scroll-area::-webkit-scrollbar-thumb:hover {
    background: rgba(139, 92, 246, 0.5);
}

/* Loading states */
.conversation-skeleton {
    animation: pulse 1.5s ease-in-out infinite;
}

.message-skeleton {
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {

    .conversations-sidebar,
    .conversation-item,
    .message-bubble {
        transition: none;
    }

    .conversation-skeleton,
    .message-skeleton {
        animation: none;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .message-bubble.sent {
        background: #000;
        color: #fff;
        border: 2px solid #fff;
    }

    .message-bubble.received {
        background: #fff;
        color: #000;
        border: 2px solid #000;
    }

    .conversation-item:hover,
    .conversation-item.active {
        background: #000;
        color: #fff;
    }
}

/* Print styles */
@media print {
    .conversations-sidebar {
        display: none;
    }

    .chat-area {
        width: 100%;
    }

    .message-input-area {
        display: none;
    }

    .message-bubble {
        break-inside: avoid;
        page-break-inside: avoid;
    }
}