<?php
/**
 * Script para remover arquivos problemáticos do FTP
 * Execute acessando: https://www.swingcuritiba.com.br/remove-problematic-files.php
 * 
 * IMPORTANTE: Delete este arquivo após usar!
 */

// Verificar se está no ambiente correto
if (!file_exists('../artisan')) {
    die('❌ Erro: Execute no diretório public do Laravel');
}

// Mudar para o diretório raiz do Laravel
chdir('..');

// Verificar se é o ambiente de produção
if (!str_contains($_SERVER['HTTP_HOST'] ?? '', 'swingcuritiba.com.br')) {
    die('❌ Este script só pode ser executado na produção');
}

$filesToRemove = ;

$action = $_GET['action'] ?? 'preview';

echo "<h1>🗑️ Remoção de Arquivos Problemáticos</h1>\n";
echo "<p>Total de arquivos: " . count($filesToRemove) . "</p>\n";

if ($action === 'preview') {
    echo "<h2>📋 Arquivos que serão removidos:</h2>\n";
    echo "<ul>\n";
    
    $existingFiles = 0;
    foreach ($filesToRemove as $file) {
        $exists = file_exists($file);
        $status = $exists ? '✅ Existe' : '❌ Não existe';
        $color = $exists ? 'green' : 'red';
        
        echo "<li style='color: $color'>$status - $file</li>\n";
        
        if ($exists) {
            $existingFiles++;
        }
    }
    
    echo "</ul>\n";
    echo "<p><strong>Arquivos existentes: $existingFiles</strong></p>\n";
    
    if ($existingFiles > 0) {
        echo "<p><a href='?action=remove' style='background: red; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>🗑️ REMOVER ARQUIVOS</a></p>\n";
    }
    
    echo "<p><em>⚠️ Revise cuidadosamente antes de remover!</em></p>\n";
    
} elseif ($action === 'remove') {
    echo "<h2>🗑️ Removendo arquivos...</h2>\n";
    
    $removed = 0;
    $errors = 0;
    
    foreach ($filesToRemove as $file) {
        if (file_exists($file)) {
            if (unlink($file)) {
                echo "<p style='color: green'>✅ Removido: $file</p>\n";
                $removed++;
            } else {
                echo "<p style='color: red'>❌ Erro ao remover: $file</p>\n";
                $errors++;
            }
        } else {
            echo "<p style='color: gray'>⏭️ Não existe: $file</p>\n";
        }
        
        // Flush output para mostrar progresso
        if (ob_get_level()) {
            ob_flush();
        }
        flush();
    }
    
    echo "<h2>📊 Resultado:</h2>\n";
    echo "<p>✅ Removidos: $removed</p>\n";
    echo "<p>❌ Erros: $errors</p>\n";
    echo "<p>✅ <strong>Processo concluído!</strong></p>\n";
    echo "<p>⚠️ <strong>IMPORTANTE:</strong> Delete este script agora!</p>\n";
}

echo "<hr>\n";
echo "<p><small>Gerado em: " . date('Y-m-d H:i:s') . "</small></p>\n";
?>