<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class NetworkVisualization extends Component
{
    public User $user;
    public $networkData = [];
    public $showModal = false;
    public $selectedConnection = null;

    public function mount(User $user)
    {
        $this->user = $user;
        $this->loadNetworkData();
    }

    public function loadNetworkData()
    {
        if (!Auth::check()) {
            return;
        }

        $currentUser = Auth::user();

        // Para o próprio perfil, mostrar conexões diretas
        if ($currentUser->id === $this->user->id) {
            $this->networkData = $this->getUserNetworkData($this->user);
        } else {
            // Para outros perfis, mostrar conexões em comum
            $this->networkData = $this->getSharedNetworkData($currentUser, $this->user);
        }
    }

    protected function getUserNetworkData($user)
    {
        $followers = $user->followers()
            ->with('userPhotos')
            ->limit(12)
            ->get()
            ->map(function ($follower) {
                return [
                    'id' => $follower->id,
                    'name' => $follower->name,
                    'username' => $follower->username,
                    'avatar' => $this->getAvatar($follower),
                    'type' => 'follower',
                    'connection_strength' => $this->calculateConnectionStrength($follower),
                ];
            });

        $following = $user->following()
            ->with('userPhotos')
            ->limit(12)
            ->get()
            ->map(function ($following) {
                return [
                    'id' => $following->id,
                    'name' => $following->name,
                    'username' => $following->username,
                    'avatar' => $this->getAvatar($following),
                    'type' => 'following',
                    'connection_strength' => $this->calculateConnectionStrength($following),
                ];
            });

        return [
            'center' => [
                'id' => $user->id,
                'name' => $user->name,
                'username' => $user->username,
                'avatar' => $this->getAvatar($user),
                'type' => 'center'
            ],
            'connections' => $followers->merge($following)->unique('id')->values(),
            'stats' => [
                'followers_count' => $user->followers()->count(),
                'following_count' => $user->following()->count(),
                'mutual_connections' => 0,
            ]
        ];
    }

    protected function getSharedNetworkData($currentUser, $targetUser)
    {
        // Conexões mútuas
        $mutualFollowers = $currentUser->followers()
            ->whereIn('follower_id', $targetUser->followers()->pluck('follower_id'))
            ->with('userPhotos')
            ->limit(8)
            ->get();

        $mutualFollowing = $currentUser->following()
            ->whereIn('following_id', $targetUser->following()->pluck('following_id'))
            ->with('userPhotos')
            ->limit(8)
            ->get();

        $connections = $mutualFollowers->merge($mutualFollowing)
            ->unique('id')
            ->map(function ($connection) {
                return [
                    'id' => $connection->id,
                    'name' => $connection->name,
                    'username' => $connection->username,
                    'avatar' => $this->getAvatar($connection),
                    'type' => 'mutual',
                    'connection_strength' => $this->calculateConnectionStrength($connection),
                ];
            });

        return [
            'center' => [
                'id' => $targetUser->id,
                'name' => $targetUser->name,
                'username' => $targetUser->username,
                'avatar' => $this->getAvatar($targetUser),
                'type' => 'target'
            ],
            'current_user' => [
                'id' => $currentUser->id,
                'name' => $currentUser->name,
                'username' => $currentUser->username,
                'avatar' => $this->getAvatar($currentUser),
                'type' => 'current'
            ],
            'connections' => $connections->values(),
            'stats' => [
                'mutual_followers' => $mutualFollowers->count(),
                'mutual_following' => $mutualFollowing->count(),
                'total_mutual' => $connections->count(),
            ]
        ];
    }

    protected function calculateConnectionStrength($user)
    {
        $strength = 0;

        // Baseado em atividade recente
        if ($user->last_seen && $user->last_seen->diffInDays(now()) <= 7) {
            $strength += 30;
        }

        // Baseado em posts recentes
        $recentPosts = $user->posts()->where('created_at', '>=', now()->subDays(30))->count();
        $strength += min($recentPosts * 5, 30);

        // Baseado em interações
        $currentUser = Auth::user();
        if ($currentUser) {
            // Verifica se há likes mútuos
            $mutualLikes = $user->likes()
                ->whereHas('post', function ($query) use ($currentUser) {
                    $query->where('user_id', $currentUser->id);
                })
                ->count();
            $strength += min($mutualLikes * 10, 40);
        }

        return min($strength, 100);
    }

    public function showConnectionDetails($connectionId)
    {
        $this->selectedConnection = collect($this->networkData['connections'])
            ->firstWhere('id', $connectionId);
        $this->showModal = true;
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->selectedConnection = null;
    }

    public function followUser($userId)
    {
        $currentUser = Auth::user();
        $userToFollow = User::find($userId);

        if ($userToFollow && !$currentUser->isFollowing($userToFollow)) {
            $currentUser->following()->attach($userId);

            // Recarregar dados da rede
            $this->loadNetworkData();

            // Dispatch evento
            $this->dispatch('user-followed', userId: $userId);
        }
    }

    public function getAvatar($user)
    {
        if ($user->userPhotos && $user->userPhotos->first()) {
            return Storage::url($user->userPhotos->first()->photo_path);
        }
        return asset('images/default-avatar.jpg');
    }

    public function getConnectionColor($type)
    {
        return match ($type) {
            'follower' => 'border-blue-500',
            'following' => 'border-green-500',
            'mutual' => 'border-purple-500',
            'center' => 'border-yellow-500',
            'target' => 'border-red-500',
            'current' => 'border-indigo-500',
            default => 'border-gray-300'
        };
    }

    public function getConnectionLabel($type)
    {
        return match ($type) {
            'follower' => 'Seguidor',
            'following' => 'Seguindo',
            'mutual' => 'Conexão Mútua',
            'center' => 'Você',
            'target' => 'Perfil',
            'current' => 'Você',
            default => 'Conexão'
        };
    }

    public function render()
    {
        return view('livewire.network-visualization');
    }
}
