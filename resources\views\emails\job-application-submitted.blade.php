<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nova Candidatura - {{ config('app.name') }}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #374151;
            background-color: #f9fafb;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #E60073 0%, #00FFF7 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 1.875rem;
            font-weight: 700;
        }
        .vip-badge {
            background: linear-gradient(135deg, #FFE600 0%, #FFA500 100%);
            color: #000;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 700;
            text-transform: uppercase;
            display: inline-block;
            margin-left: 0.5rem;
        }
        .content {
            padding: 2rem;
        }
        .candidate-info {
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1.5rem 0;
        }
        .job-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1.5rem 0;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #e5e7eb;
        }
        .info-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        .info-label {
            font-weight: 600;
            color: #4b5563;
        }
        .info-value {
            color: #1f2937;
        }
        .action-button {
            display: inline-block;
            background: linear-gradient(135deg, #E60073 0%, #00FFF7 100%);
            color: white;
            text-decoration: none;
            padding: 0.875rem 2rem;
            border-radius: 6px;
            font-weight: 600;
            font-size: 1rem;
            margin: 1rem 0.5rem 1rem 0;
            transition: transform 0.2s;
        }
        .action-button:hover {
            transform: translateY(-1px);
        }
        .footer {
            background-color: #f9fafb;
            padding: 1.5rem;
            text-align: center;
            font-size: 0.875rem;
            color: #6b7280;
            border-top: 1px solid #e5e7eb;
        }
        .footer a {
            color: #E60073;
            text-decoration: none;
        }
        @media (max-width: 600px) {
            .container {
                margin: 0;
                border-radius: 0;
            }
            .content {
                padding: 1.5rem;
            }
            .header {
                padding: 1.5rem;
            }
            .info-row {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{ config('app.name') }}</h1>
            <p style="margin: 0.5rem 0 0 0; opacity: 0.9;">
                Nova Candidatura Recebida
                @if($application->is_vip_priority)
                    <span class="vip-badge">VIP</span>
                @endif
            </p>
        </div>
        
        <div class="content">
            <p style="font-size: 1.125rem; margin-bottom: 1.5rem; color: #1f2937;">
                Olá <strong>{{ $adminRecipient->name ?? 'Administrador' }}</strong>,
            </p>
            
            <p>
                Uma nova candidatura foi enviada para a vaga <strong>{{ $job->title }}</strong>.
                @if($application->is_vip_priority)
                    <strong style="color: #FFE600;">Esta é uma candidatura VIP e deve ter prioridade na análise.</strong>
                @endif
            </p>

            <div class="job-info">
                <h3 style="margin: 0 0 1rem 0; font-size: 1.25rem;">📋 Informações da Vaga</h3>
                <div class="info-row" style="border-color: rgba(255,255,255,0.2);">
                    <span class="info-label" style="color: rgba(255,255,255,0.9);">Título:</span>
                    <span class="info-value" style="color: white;">{{ $job->title }}</span>
                </div>
                <div class="info-row" style="border-color: rgba(255,255,255,0.2);">
                    <span class="info-label" style="color: rgba(255,255,255,0.9);">Categoria:</span>
                    <span class="info-value" style="color: white;">{{ $job->category->name }}</span>
                </div>
                <div class="info-row" style="border-color: rgba(255,255,255,0.2);">
                    <span class="info-label" style="color: rgba(255,255,255,0.9);">Localização:</span>
                    <span class="info-value" style="color: white;">{{ $job->location ?? 'Não especificado' }}</span>
                </div>
                <div class="info-row" style="border-color: rgba(255,255,255,0.2);">
                    <span class="info-label" style="color: rgba(255,255,255,0.9);">Modalidade:</span>
                    <span class="info-value" style="color: white;">{{ $job->work_mode }}</span>
                </div>
            </div>

            <div class="candidate-info">
                <h3 style="margin: 0 0 1rem 0; font-size: 1.25rem; color: #1f2937;">👤 Dados do Candidato</h3>
                <div class="info-row">
                    <span class="info-label">Nome:</span>
                    <span class="info-value">{{ $user->name }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Email:</span>
                    <span class="info-value">{{ $user->email }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Telefone:</span>
                    <span class="info-value">{{ $user->telefone ?? 'Não informado' }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Data da Candidatura:</span>
                    <span class="info-value">{{ $application->created_at->format('d/m/Y H:i') }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Status:</span>
                    <span class="info-value">{{ $application->status }}</span>
                </div>
                @if($application->is_vip_priority)
                <div class="info-row">
                    <span class="info-label">Prioridade:</span>
                    <span class="info-value" style="color: #FFE600; font-weight: 700;">VIP</span>
                </div>
                @endif
            </div>

            @if($application->cover_letter)
            <div style="background-color: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 1.5rem; margin: 1.5rem 0;">
                <h4 style="margin: 0 0 1rem 0; color: #1f2937;">💬 Carta de Apresentação</h4>
                <p style="margin: 0; white-space: pre-line;">{{ $application->cover_letter }}</p>
            </div>
            @endif

            <div style="text-align: center; margin: 2rem 0;">
                <a href="{{ route('admin.job-applications.show', $application->id) }}" class="action-button">
                    Ver Candidatura Completa
                </a>
                @if($application->resume_path)
                <a href="{{ route('job-applications.download-resume', $application->id) }}" class="action-button">
                    Download do Currículo
                </a>
                @endif
            </div>
        </div>
        
        <div class="footer">
            <p>
                Esta é uma notificação automática do sistema de candidaturas.
            </p>
            <p>
                © {{ date('Y') }} {{ config('app.name') }}. Todos os direitos reservados.<br>
                <a href="{{ config('app.url') }}">{{ config('app.url') }}</a>
            </p>
        </div>
    </div>
</body>
</html>
