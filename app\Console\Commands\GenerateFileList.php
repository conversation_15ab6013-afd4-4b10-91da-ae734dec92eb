<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Carbon\Carbon;

class GenerateFileList extends Command
{
    protected $signature = 'files:list {--output=file-list.txt} {--exclude-vendor} {--exclude-node-modules} {--exclude-storage}';
    protected $description = 'Gera uma lista de todos os arquivos do projeto com suas datas de modificação';

    public function handle()
    {
        $this->info('🔍 Gerando lista de arquivos...');
        
        $outputFile = $this->option('output');
        $excludeVendor = $this->option('exclude-vendor');
        $excludeNodeModules = $this->option('exclude-node-modules');
        $excludeStorage = $this->option('exclude-storage');
        
        $files = [];
        $basePath = base_path();
        
        // Diretórios a serem excluídos
        $excludeDirs = ['.git'];
        
        if ($excludeVendor) {
            $excludeDirs[] = 'vendor';
        }
        
        if ($excludeNodeModules) {
            $excludeDirs[] = 'node_modules';
        }
        
        if ($excludeStorage) {
            $excludeDirs[] = 'storage';
        }
        
        $this->scanDirectory($basePath, $files, $excludeDirs);
        
        // Ordenar por caminho
        ksort($files);
        
        // Gerar conteúdo do arquivo
        $content = "# Lista de Arquivos - " . Carbon::now()->format('Y-m-d H:i:s') . "\n";
        $content .= "# Commit atual: " . $this->getCurrentCommit() . "\n";
        $content .= "# Total de arquivos: " . count($files) . "\n\n";
        
        foreach ($files as $relativePath => $info) {
            $content .= sprintf(
                "%s | %s | %s\n",
                $info['modified'],
                $info['size'],
                $relativePath
            );
        }
        
        // Salvar arquivo
        File::put($outputFile, $content);
        
        $this->info("✅ Lista gerada: {$outputFile}");
        $this->info("📊 Total de arquivos: " . count($files));
        
        return 0;
    }
    
    private function scanDirectory($directory, &$files, $excludeDirs, $basePath = null)
    {
        if ($basePath === null) {
            $basePath = $directory;
        }
        
        $items = File::allFiles($directory);
        
        foreach ($items as $file) {
            $fullPath = $file->getRealPath();
            $relativePath = str_replace($basePath . DIRECTORY_SEPARATOR, '', $fullPath);
            $relativePath = str_replace('\\', '/', $relativePath);
            
            // Verificar se está em um diretório excluído
            $skip = false;
            foreach ($excludeDirs as $excludeDir) {
                if (str_starts_with($relativePath, $excludeDir . '/')) {
                    $skip = true;
                    break;
                }
            }
            
            if ($skip) {
                continue;
            }
            
            $files[$relativePath] = [
                'modified' => Carbon::createFromTimestamp($file->getMTime())->format('Y-m-d H:i:s'),
                'size' => $this->formatBytes($file->getSize()),
                'path' => $fullPath
            ];
        }
    }
    
    private function formatBytes($size)
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $unitIndex = 0;
        
        while ($size >= 1024 && $unitIndex < count($units) - 1) {
            $size /= 1024;
            $unitIndex++;
        }
        
        return round($size, 2) . ' ' . $units[$unitIndex];
    }
    
    private function getCurrentCommit()
    {
        try {
            $commit = trim(shell_exec('git rev-parse HEAD'));
            $shortCommit = substr($commit, 0, 7);
            $message = trim(shell_exec('git log -1 --pretty=%B'));
            return "{$shortCommit} - {$message}";
        } catch (\Exception $e) {
            return 'N/A';
        }
    }
}
