<div class="max-w-7xl mx-auto p-6">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-300 mb-2">Relatório de Ganhos</h1>
        <p class="text-gray-400">Acompanhe suas vendas e ganhos com álbuns premium</p>
    </div>

    <!-- Period Filter -->
    <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg class="mb-6"">
        <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.body">
            <div class="grid grid-cols-2 md:grid-cols-6 gap-4">
                <div class="col-span-2">
                    <flux:label for="period">Período</flux:label>
                    <flux:select wire:model.live="period" id="period" class="mt-1">
                        <option value="all">Todos os tempos</option>
                        <option value="today">Hoje</option>
                        <option value="week">Esta semana</option>
                        <option value="month">Este mês</option>
                        <option value="year">Este ano</option>
                        <option value="custom">Personalizado</option>
                    </flux:select>
                </div>

                @if($period === 'custom')
                <div class="col-span-2">
                    <flux:label for="startDate">Data Inicial</flux:label>
                    <flux:input 
                        wire:model.live="startDate" 
                        id="startDate" 
                        type="date"
                        class="mt-1"
                    />
                </div>

                <div class="col-span-2">
                    <flux:label for="endDate">Data Final</flux:label>
                    <flux:input 
                        wire:model.live="endDate" 
                        id="endDate" 
                        type="date"
                        class="mt-1"
                    />
                </div>
                @endif
            </div>
        </flux:card.body>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
        <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg">
            <div class="px-6 py-4 text-center">
                <div class="text-2xl font-bold text-green-400 mb-1">
                    R$ {{ number_format($stats['total_earnings'], 2, ',', '.') }}
                </div>
                <div class="text-sm text-gray-400">Ganhos Totais</div>
            </div>
        </div>

        <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg">
            <div class="px-6 py-4 text-center">
                <div class="text-2xl font-bold text-purple-400 mb-1">
                    {{ $stats['total_sales'] }}
                </div>
                <div class="text-sm text-gray-400">Vendas Totais</div>
            </flux:card.body>
        </div>

        <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg">
            <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.body class="text-center"">
                <div class="text-2xl font-bold text-yellow-400 mb-1">
                    R$ {{ number_format($stats['pending_earnings'], 2, ',', '.') }}
                </div>
                <div class="text-sm text-gray-400">Pendente</div>
            </flux:card.body>
        </div>

        <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg">
            <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.body class="text-center"">
                <div class="text-2xl font-bold text-blue-400 mb-1">
                    R$ {{ number_format($stats['paid_earnings'], 2, ',', '.') }}
                </div>
                <div class="text-sm text-gray-400">Pago</div>
            </flux:card.body>
        </div>

        <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg">
            <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.body class="text-center"">
                <div class="text-2xl font-bold text-cyan-400 mb-1">
                    R$ {{ number_format($stats['average_sale'], 2, ',', '.') }}
                </div>
                <div class="text-sm text-gray-400">Média por Venda</div>
            </flux:card.body>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Earnings Chart -->
        <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg">
            <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.header">
                <h3 class="text-lg font-semibold text-gray-300">Ganhos dos Últimos 30 Dias</h3>
            </div>
            <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.body">
                <div class="h-64 flex items-center justify-center">
                    <canvas id="earningsChart" width="400" height="200"></canvas>
                </div>
            </flux:card.body>
        </div>

        <!-- Top Albums -->
        <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg">
            <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.header">
                <h3 class="text-lg font-semibold text-gray-300">Álbuns Mais Vendidos</h3>
            </div>
            <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.body">
                @if($topAlbums->count() > 0)
                <div class="space-y-4">
                    @foreach($topAlbums as $album)
                    <div class="flex items-center justify-between p-3 bg-zinc-800 rounded-lg">
                        <div class="flex-1">
                            <h4 class="font-medium text-gray-300 truncate">{{ $album->name }}</h4>
                            <p class="text-sm text-gray-400">{{ $album->sales_count }} vendas</p>
                        </div>
                        <div class="text-right">
                            <p class="font-medium text-green-400">R$ {{ number_format($album->total_earnings, 2, ',', '.') }}</p>
                            <flux:button 
                                wire:click="showAlbumDetails({{ $album->id }})"
                                size="sm"
                                variant="outline"
                            >
                                Detalhes
                            </flux:button>
                        </div>
                    </div>
                    @endforeach
                </div>
                @else
                <div class="text-center py-8">
                    <flux:icon name="chart-bar" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p class="text-gray-400">Nenhuma venda ainda</p>
                </div>
                @endif
            </flux:card.body>
        </div>
    </div>

    <!-- Recent Sales -->
    <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg">
        <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.header">
            <h3 class="text-lg font-semibold text-gray-300">Vendas Recentes</h3>
        </div>
        <div class="bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg.body">
            @if($recentSales->count() > 0)
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead>
                        <tr class="border-b border-gray-700">
                            <th class="text-left py-3 px-4 text-gray-400">Data</th>
                            <th class="text-left py-3 px-4 text-gray-400">Álbum</th>
                            <th class="text-left py-3 px-4 text-gray-400">Comprador</th>
                            <th class="text-right py-3 px-4 text-gray-400">Valor</th>
                            <th class="text-right py-3 px-4 text-gray-400">Ganho</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($recentSales as $sale)
                        <tr class="border-b border-gray-800">
                            <td class="py-3 px-4 text-gray-300">
                                {{ $sale->purchased_at->format('d/m/Y H:i') }}
                            </td>
                            <td class="py-3 px-4">
                                <div class="font-medium text-gray-300">{{ $sale->album->name }}</div>
                            </td>
                            <td class="py-3 px-4 text-gray-300">
                                {{ $sale->buyer->name }}
                            </td>
                            <td class="py-3 px-4 text-right text-purple-400">
                                R$ {{ number_format($sale->amount, 2, ',', '.') }}
                            </td>
                            <td class="py-3 px-4 text-right text-green-400">
                                R$ {{ number_format($sale->seller_amount, 2, ',', '.') }}
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <div class="mt-4">
                {{ $recentSales->links() }}
            </div>
            @else
            <div class="text-center py-8">
                <flux:icon name="currency-dollar" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p class="text-gray-400">Nenhuma venda encontrada</p>
            </div>
            @endif
        </flux:card.body>
    </div>

    <!-- Album Details Modal -->
    @if($showDetails && $selectedAlbumDetails)
    <flux:modal wire:model="showDetails" size="lg">
        <flux:modal.header>
            <h2 class="text-lg font-semibold text-gray-300">
                Detalhes: {{ $selectedAlbumDetails['album']->name }}
            </h2>
        </flux:modal.header>
        
        <flux:modal.body>
            <!-- Stats -->
            <div class="grid grid-cols-2 gap-4 mb-6">
                <div class="text-center p-4 bg-zinc-800 rounded-lg">
                    <div class="text-xl font-bold text-green-400">
                        R$ {{ number_format($selectedAlbumDetails['total_earnings'], 2, ',', '.') }}
                    </div>
                    <div class="text-sm text-gray-400">Total de Ganhos</div>
                </div>
                <div class="text-center p-4 bg-zinc-800 rounded-lg">
                    <div class="text-xl font-bold text-purple-400">
                        {{ $selectedAlbumDetails['total_sales'] }}
                    </div>
                    <div class="text-sm text-gray-400">Total de Vendas</div>
                </div>
            </div>

            <!-- Sales List -->
            <h4 class="font-medium text-gray-300 mb-3">Histórico de Vendas</h4>
            <div class="max-h-64 overflow-y-auto">
                @foreach($selectedAlbumDetails['sales'] as $sale)
                <div class="flex justify-between items-center p-3 bg-zinc-800 rounded-lg mb-2">
                    <div>
                        <p class="text-gray-300">{{ $sale->buyer->name }}</p>
                        <p class="text-sm text-gray-400">{{ $sale->purchased_at->format('d/m/Y H:i') }}</p>
                    </div>
                    <div class="text-right">
                        <p class="text-green-400">R$ {{ number_format($sale->seller_amount, 2, ',', '.') }}</p>
                    </div>
                </div>
                @endforeach
            </div>
        </flux:modal.body>
        
        <flux:modal.footer>
            <flux:button wire:click="hideDetails" variant="outline">
                Fechar
            </flux:button>
        </flux:modal.footer>
    </flux:modal>
    @endif
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('earningsChart').getContext('2d');
    const chartData = @json($chartData);
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: chartData.map(item => item.date),
            datasets: [{
                label: 'Ganhos (R$)',
                data: chartData.map(item => item.earnings),
                borderColor: '#8B5CF6',
                backgroundColor: 'rgba(139, 92, 246, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: {
                        color: '#D1D5DB'
                    }
                }
            },
            scales: {
                x: {
                    ticks: {
                        color: '#9CA3AF'
                    },
                    grid: {
                        color: 'rgba(156, 163, 175, 0.1)'
                    }
                },
                y: {
                    ticks: {
                        color: '#9CA3AF',
                        callback: function(value) {
                            return 'R$ ' + value.toFixed(2);
                        }
                    },
                    grid: {
                        color: 'rgba(156, 163, 175, 0.1)'
                    }
                }
            }
        }
    });
});
</script>
@endpush
