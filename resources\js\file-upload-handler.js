// File Upload Handler for Livewire
document.addEventListener('DOMContentLoaded', function() {
    // Fix for Livewire file upload errors
    document.addEventListener('livewire:initialized', () => {
        // Handle upload errors
        Livewire.hook('upload:error', (message, component) => {
            console.error('Upload error:', message);
            
            // Show user-friendly error message
            if (typeof window.showToast === 'function') {
                window.showToast('Erro no upload: ' + message, 'error');
            }
        });

        // Handle upload finished
        Livewire.hook('upload:finished', (response, component) => {
            console.log('Upload finished successfully');
        });

        // Handle upload progress
        Livewire.hook('upload:progress', (progress, component) => {
            console.log('Upload progress:', progress + '%');
        });

        // Fix for null files error
        Livewire.hook('upload:start', (files, component) => {
            console.log('Upload started with files:', files);
            
            // Validate files exist
            if (!files || files.length === 0) {
                console.error('No files to upload');
                return false;
            }
            
            // Check if files are valid
            for (let file of files) {
                if (!file || !file.name) {
                    console.error('Invalid file detected:', file);
                    return false;
                }
            }
        });
    });

    // Additional file input validation
    document.addEventListener('change', function(e) {
        if (e.target.type === 'file' && e.target.hasAttribute('wire:model')) {
            const files = e.target.files;
            
            if (!files || files.length === 0) {
                console.warn('No files selected');
                return;
            }
            
            // Validate each file
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                
                if (!file) {
                    console.error('Invalid file at index', i);
                    e.target.value = ''; // Clear the input
                    return;
                }
                
                // Check file size (200MB limit)
                const maxSize = 200 * 1024 * 1024; // 200MB in bytes
                if (file.size > maxSize) {
                    alert('Arquivo muito grande. Tamanho máximo: 200MB');
                    e.target.value = '';
                    return;
                }
                
                console.log('File validated:', file.name, 'Size:', file.size);
            }
        }
    });
});

// Global function to show toast messages
window.showToast = function(message, type = 'info') {
    // Try to dispatch to toast component
    if (typeof Livewire !== 'undefined') {
        try {
            Livewire.dispatch('toast', {
                message: message,
                type: type
            });
        } catch (error) {
            console.error('Failed to show toast:', error);
            // Fallback to alert
            alert(message);
        }
    } else {
        alert(message);
    }
};
