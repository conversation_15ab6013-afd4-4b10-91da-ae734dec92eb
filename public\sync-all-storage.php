<?php

/**
 * Script para sincronizar TODOS os arquivos de storage (KingHost)
 * Execute acessando: https://www.swingcuritiba.com.br/sync-all-storage.php
 * 
 * IMPORTANTE: Delete este arquivo após usar!
 */

// Verificar se está no ambiente correto
if (!file_exists('../artisan')) {
    die('❌ Erro: Execute no diretório public do Laravel');
}

// Mudar para o diretório raiz do Laravel
chdir('..');

// Verificar se é o ambiente de produção
if (!str_contains($_SERVER['HTTP_HOST'] ?? '', 'swingcuritiba.com.br')) {
    die('❌ Este script só pode ser executado na produção');
}

echo "<h1>🔄 Sincronização COMPLETA de Storage</h1>";
echo "<pre>";

echo "🚀 Iniciando sincronização completa...\n";
echo "Timestamp: " . date('Y-m-d H:i:s') . "\n";
echo str_repeat("=", 50) . "\n";

$syncedFiles = 0;
$errors = 0;
$sourceBase = 'storage/app/public';
$targetBase = 'public/storage';

function syncDirectory($sourceDir, $targetDir, &$syncedFiles, &$errors, $level = 0)
{
    $indent = str_repeat("  ", $level);

    if (!is_dir($sourceDir)) {
        return;
    }

    // Criar diretório target se não existir
    if (!is_dir($targetDir)) {
        if (mkdir($targetDir, 0755, true)) {
            echo "{$indent}📁 Diretório criado: " . basename($targetDir) . "\n";
        } else {
            echo "{$indent}❌ Erro ao criar diretório: " . basename($targetDir) . "\n";
            $errors++;
            return;
        }
    }

    $items = scandir($sourceDir);

    foreach ($items as $item) {
        if ($item === '.' || $item === '..') {
            continue;
        }

        $sourcePath = $sourceDir . '/' . $item;
        $targetPath = $targetDir . '/' . $item;

        if (is_dir($sourcePath)) {
            echo "{$indent}📂 Processando diretório: {$item}\n";
            syncDirectory($sourcePath, $targetPath, $syncedFiles, $errors, $level + 1);
        } else {
            // Verificar se precisa sincronizar
            $needsSync = !file_exists($targetPath) ||
                filemtime($sourcePath) > filemtime($targetPath);

            if ($needsSync) {
                if (copy($sourcePath, $targetPath)) {
                    echo "{$indent}✅ {$item}\n";
                    $syncedFiles++;
                } else {
                    echo "{$indent}❌ Erro: {$item}\n";
                    $errors++;
                }
            } else {
                echo "{$indent}⏭️ {$item} (já atualizado)\n";
            }
        }
    }
}

// Verificar se o diretório source existe
if (!is_dir($sourceBase)) {
    echo "❌ Diretório source não encontrado: {$sourceBase}\n";
    echo "Criando estrutura básica...\n";

    $basicDirs = [
        'posts/images',
        'posts/videos',
        'avatars',
        'covers',
        'albums',
        'groups',
        'events',
        'products',
        'photos',
        'cover_photos'
    ];

    foreach ($basicDirs as $dir) {
        $fullDir = $sourceBase . '/' . $dir;
        if (!is_dir($fullDir)) {
            mkdir($fullDir, 0755, true);
            echo "✅ Criado: {$dir}\n";
        }
    }
}

echo "\n🔄 Sincronizando arquivos...\n";
echo str_repeat("-", 30) . "\n";

// Executar sincronização
syncDirectory($sourceBase, $targetBase, $syncedFiles, $errors);

echo str_repeat("-", 30) . "\n";

// Verificar alguns arquivos específicos
echo "\n🔍 Verificando arquivos de teste...\n";

$testDirs = [
    'posts/images' => 'Imagens de posts',
    'posts/videos' => 'Vídeos de posts',
    'avatars' => 'Avatars de usuários',
    'covers' => 'Capas de perfil',
    'groups' => 'Imagens de grupos',
    'albums' => 'Álbuns de fotos',
    'photos' => 'Fotos de perfil',
    'cover_photos' => 'Fotos de capa'
];

foreach ($testDirs as $dir => $desc) {
    $sourceDir = $sourceBase . '/' . $dir;
    $targetDir = $targetBase . '/' . $dir;

    echo "{$desc}: ";

    if (is_dir($sourceDir)) {
        $sourceFiles = count(array_diff(scandir($sourceDir), ['.', '..']));
        $targetFiles = is_dir($targetDir) ? count(array_diff(scandir($targetDir), ['.', '..'])) : 0;

        echo "Source: {$sourceFiles} | Target: {$targetFiles}";

        if ($sourceFiles === $targetFiles) {
            echo " ✅\n";
        } else {
            echo " ⚠️\n";
        }
    } else {
        echo "Não existe ⚠️\n";
    }
}

// Testar URLs
echo "\n🔗 Testando URLs...\n";
$appUrl = 'https://www.swingcuritiba.com.br';

// Procurar uma imagem para testar
$testImageFound = false;
$testDirs = ['posts/images', 'avatars', 'covers', 'groups', 'albums', 'photos', 'cover_photos'];

foreach ($testDirs as $dir) {
    $fullDir = $targetBase . '/' . $dir;
    if (is_dir($fullDir)) {
        $files = array_diff(scandir($fullDir), ['.', '..']);
        if (!empty($files)) {
            $testFile = reset($files);
            $testUrl = "{$appUrl}/storage/{$dir}/{$testFile}";
            echo "URL de teste: {$testUrl}\n";

            // Verificar se o arquivo existe
            if (file_exists($fullDir . '/' . $testFile)) {
                echo "✅ Arquivo existe e deve estar acessível\n";
            } else {
                echo "❌ Arquivo não encontrado\n";
            }

            $testImageFound = true;
            break;
        }
    }
}

if (!$testImageFound) {
    echo "⚠️ Nenhuma imagem encontrada para teste\n";
}

// Criar .htaccess se não existir
echo "\n⚙️ Verificando configuração Apache...\n";
$htaccessFile = $targetBase . '/.htaccess';
if (!file_exists($htaccessFile)) {
    $htaccessContent = '# Configuração para servir arquivos de storage
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteCond %{REQUEST_FILENAME} -f
    RewriteRule ^.*\.(jpg|jpeg|png|gif|bmp|webp|svg|mp4|mov|avi|webm)$ - [L]
</IfModule>

<IfModule mod_mime.c>
    AddType image/jpeg .jpg .jpeg
    AddType image/png .png
    AddType image/gif .gif
    AddType image/webp .webp
    AddType image/svg+xml .svg
    AddType video/mp4 .mp4
    AddType video/quicktime .mov
    AddType video/x-msvideo .avi
    AddType video/webm .webm
</IfModule>

<IfModule mod_headers.c>
    Header set Access-Control-Allow-Origin "*"
    <FilesMatch "\.(jpg|jpeg|png|gif|bmp|webp|svg)$">
        Header set Cache-Control "public, max-age=2592000"
    </FilesMatch>
</IfModule>';

    if (file_put_contents($htaccessFile, $htaccessContent)) {
        echo "✅ Arquivo .htaccess criado\n";
    } else {
        echo "❌ Falha ao criar .htaccess\n";
        $errors++;
    }
} else {
    echo "✅ Arquivo .htaccess já existe\n";
}

// Resumo final
echo "\n" . str_repeat("=", 50) . "\n";
echo "📊 RESUMO DA SINCRONIZAÇÃO\n";
echo str_repeat("=", 50) . "\n";
echo "Arquivos sincronizados: {$syncedFiles}\n";
echo "Erros encontrados: {$errors}\n";

if ($errors === 0) {
    echo "\n🎉 Sincronização concluída com SUCESSO!\n";
    echo "\n✅ Próximos passos:\n";
    echo "1. Teste uma URL de imagem diretamente no navegador\n";
    echo "2. Verifique se as imagens aparecem nos posts\n";
    echo "3. Novas imagens serão sincronizadas automaticamente\n";
    echo "4. DELETE ESTE ARQUIVO por segurança\n";
} else {
    echo "\n⚠️ Sincronização concluída com alguns erros.\n";
    echo "Verifique as permissões dos diretórios.\n";
}

echo "\n💡 IMPORTANTE:\n";
echo "• Agora as novas imagens serão sincronizadas automaticamente\n";
echo "• O sistema foi configurado para funcionar sem symlink\n";
echo "• Delete este arquivo após usar!\n";

echo "\n⚠️ IMPORTANTE: Delete este arquivo após usar!\n";
echo "</pre>";

// Adicionar botão para deletar o próprio arquivo
echo '<hr>';
echo '<form method="post" style="margin: 20px 0;">';
echo '<input type="hidden" name="delete_self" value="1">';
echo '<button type="submit" style="background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">🗑️ Deletar este arquivo (Recomendado)</button>';
echo '</form>';

// Processar deleção do arquivo
if (isset($_POST['delete_self'])) {
    if (unlink('public/' . basename(__FILE__))) {
        echo '<p style="color: green;">✅ Arquivo deletado com sucesso!</p>';
        echo '<script>setTimeout(() => window.location.href = "/", 2000);</script>';
    } else {
        echo '<p style="color: red;">❌ Erro ao deletar arquivo. Delete manualmente.</p>';
    }
}
