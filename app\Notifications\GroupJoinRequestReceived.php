<?php

namespace App\Notifications;

use App\Models\Group;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class GroupJoinRequestReceived extends Notification
{
    use Queueable;

    protected $group;
    protected $requestingUser;

    /**
     * Create a new notification instance.
     */
    public function __construct(Group $group, User $requestingUser)
    {
        $this->group = $group;
        $this->requestingUser = $requestingUser;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via($notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Nova Solicitação de Entrada - ' . $this->group->name)
            ->view('emails.group-join-request-received', [
                'group' => $this->group,
                'requestingUser' => $this->requestingUser,
                'admin' => $notifiable,
            ]);
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray($notifiable): array
    {
        return [
            'group_id' => $this->group->id,
            'group_name' => $this->group->name,
            'requesting_user_id' => $this->requestingUser->id,
            'requesting_user_name' => $this->requestingUser->name,
            'message' => $this->requestingUser->name . ' solicitou entrada no grupo ' . $this->group->name,
        ];
    }
}
