<x-layouts.app :title="__('Contos')">

    <div class="container mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-4 sm:py-6">
        <!-- Mensagem de sucesso -->
        @if (session()->has('message'))
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4 sm:mb-6">
                {{ session('message') }}
            </div>
        @endif

        <!-- Header com título e botão -->
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
            <div>
                <h1 class="text-2xl sm:text-3xl font-bold text-gray-300">Contos</h1>
                <p class="text-gray-400 text-sm sm:text-base mt-1">Descubra e compartilhe histórias incríveis</p>
            </div>

            <!-- <PERSON><PERSON><PERSON> para abrir o modal -->
            <button
                x-data
                x-on:click="$dispatch('open-modal')"
                class="bg-purple-600 text-white px-4 sm:px-6 py-2 sm:py-3 rounded-lg shadow-md hover:bg-purple-700 hover:shadow-lg transition duration-300 ease-in-out text-sm sm:text-base font-medium w-full sm:w-auto">
                {{ __('Criar Conto') }}
            </button>
        </div>

        <!-- Lista de contos -->
        <livewire:list-contos />
    </div>

    <!-- Modal -->
    <div
        x-data="{show : false}"
        x-show="show"
        x-on:open-modal.window="show = true"
        x-on:close-modal.window="show = false"
        x-on:keydown.escape.window="show = false"
        x-on:click.away="show = false"
        class="fixed inset-0 flex items-center justify-center bg-zinc-800 bg-opacity-75 z-50 p-4">
        <div class="bg-zinc-900 rounded-lg shadow-lg p-4 sm:p-6 w-full max-w-lg sm:max-w-2xl max-h-[90vh] overflow-y-auto">
            <livewire:create-conto />
        </div>
    </div>

</x-layouts.app>
