<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Laravel\Cashier\Billable;
use App\Models\Conto;
use App\Models\City;
use App\Models\State;
use App\Models\Like;
use App\Models\Level;
use App\Models\UserLevel;
use App\Models\UserPoint;
use App\Models\Notification;
use App\Models\FollowRequest;
// use App\Models\LookingForOption;
// use App\Models\PreferenceOption;
use App\Models\UserPhoto;
use App\Models\UserCoverPhoto;
use App\Models\Post;
use App\Models\Payment;
use App\Models\Hobby;
use App\Models\Procura;
use App\Models\Product;
use App\Models\Group;
use App\Models\GroupInvitation;
use App\Models\Event;
use App\Models\Wallet;
use App\Models\WalletTransaction;
use App\Models\Conversation;
use App\Models\NightBoardPlan;
use App\Models\NightBoardSupport;
use App\Models\Album;
use App\Models\AlbumMedia;
use App\Models\Achievement;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use App\Notifications\CustomVerifyEmail;

class User extends Authenticatable implements MustVerifyEmail
{

    use HasFactory, Notifiable, Billable;


    protected $fillable = [
        'name',
        'username',
        'email',
        'password',
        'city_id',
        'state_id',
        'latitude',
        'longitude',
        'sexo',
        'aniversario',
        'privado',
        'bio',
        'role', // Adicionado role
        'active', // Adicionado active
        'last_seen',
        'status',
        'daily_access_start', // Controle de tempo diário
        'daily_minutes_used' // Minutos usados hoje
    ];

    // Relação com posts
    public function posts()
    {
        return $this->hasMany(Post::class);
    }

    // Relação com likes
    public function likes()
    {
        return $this->hasMany(Like::class);
    }

    public function likedPosts()
    {
        return $this->belongsToMany(Post::class, 'post_user_likes')->withTimestamps();
    }

    public function contos()
    {
        return $this->hasMany(Conto::class);
    }


    // Método para atualizar o nível do usuário com base nos pontos
    public function updateLevel()
    {
        $points = $this->userPoints ? $this->userPoints->total_points : 0;
        $levelData = Level::where('min_points', '<=', $points)
            ->orderBy('level', 'desc')
            ->first();

        if ($levelData && $this->level !== $levelData->level) {
            $this->level = $levelData->level;
            $this->save();
        }
    }

    // Acessor para obter o objeto Level do usuário
    public function getLevelAttribute()
    {
        $points = $this->userPoints ? $this->userPoints->total_points : 0;
        return Level::where('min_points', '<=', $points)
            ->orderBy('level', 'desc')
            ->first();
    }

    // Relacionamento com "looking_for" via pivot - Temporarily commented out until model is created
    // public function lookingFor() {
    //     return $this->belongsToMany(LookingForOption::class, 'user_looking_for', 'user_id', 'looking_for_option_id');
    // }

    // Relacionamento com "preferences" via pivot - Temporarily commented out until model is created
    // public function preferences() {
    //     return $this->belongsToMany(PreferenceOption::class, 'user_preferences', 'user_id', 'preference_option_id');
    // }

    // Relacionamento com fotos
    public function photos()
    {
        return $this->hasMany(UserPhoto::class);
    }

    public function userPhotos()
    {
        return $this->hasMany(UserPhoto::class);
    }

    public function userPhoto()
    {
        return $this->hasOne(UserPhoto::class);
    }

    public function userCoverPhotos()
    {
        return $this->hasMany(UserCoverPhoto::class);
    }

    /**
     * Álbuns do usuário
     */
    public function albums()
    {
        return $this->hasMany(Album::class)->orderBy('sort_order');
    }

    /**
     * Mídias do usuário (fotos e vídeos)
     */
    public function albumMedias()
    {
        return $this->hasMany(AlbumMedia::class);
    }

    /**
     * Álbum padrão do usuário
     */
    public function defaultAlbum()
    {
        return $this->hasOne(Album::class)->where('is_default', true);
    }

    /**
     * Usuários que seguem este usuário
     */
    public function followers()
    {
        return $this->belongsToMany(User::class, 'user_followers', 'following_id', 'follower_id')
            ->withTimestamps();
    }

    /**
     * Usuários que este usuário segue
     */
    public function following()
    {
        return $this->belongsToMany(User::class, 'user_followers', 'follower_id', 'following_id')
            ->withTimestamps();
    }

    /**
     * Check if the user is following another user
     */
    public function isFollowing(User $user)
    {
        return $this->following()->where('following_id', $user->id)->exists();
    }

    /**
     * Relacionamento com notificações
     */
    public function notifications()
    {
        return $this->hasMany(Notification::class);
    }

    /**
     * Relacionamento com solicitações de seguir
     */
    public function followRequests()
    {
        return $this->hasMany(FollowRequest::class, 'receiver_id');
    }

    public function sentFollowRequests()
    {
        return $this->hasMany(FollowRequest::class, 'sender_id');
    }

    public function hasPendingFollowRequestFrom(User $user)
    {
        return $this->followRequests()
            ->where('sender_id', $user->id)
            ->where('status', 'pending')
            ->exists();
    }

    public function city()
    {
        return $this->belongsTo(City::class);
    }

    public function state()
    {
        return $this->belongsTo(State::class);
    }

    /**
     * Increment ranking points
     */
    public function incrementRankingPoints($points)
    {
        $this->ranking_points += $points;
        $this->save();
    }

    /**
     * Relacionamento com pontos do usuário (registro atual)
     */
    public function userPoints()
    {
        return $this->hasOne(UserPoint::class);
    }

    /**
     * Relacionamento com todos os logs de pontos do usuário
     */
    public function userPointsLogs()
    {
        return $this->hasMany(UserPointLog::class);
    }

    public function hobbies()
    {
        return $this->belongsToMany(Hobby::class, 'hobby_user');
    }

    public function procuras()
    {
        return $this->belongsToMany(Procura::class, 'procura_user');
    }

    /**
     * Produtos na wishlist do usuário (many-to-many).
     */
    public function wishlistedProducts()
    {
        return $this->belongsToMany(Product::class, 'wishlists')->withTimestamps();
    }

    /**
     * Mensagens enviadas pelo usuário
     */
    public function sentMessages()
    {
        return $this->hasMany(Message::class, 'sender_id');
    }

    /**
     * Mensagens recebidas pelo usuário
     */
    public function receivedMessages()
    {
        return $this->hasMany(Message::class, 'receiver_id');
    }

    /**
     * Retorna o número de mensagens não lidas
     */
    public function unreadMessagesCount()
    {
        return $this->receivedMessages()->whereNull('read_at')->count();
    }

    /**
     * Conversas do usuário
     */
    public function conversations()
    {
        return $this->belongsToMany(Conversation::class, 'conversation_user')
            ->withTimestamps();
    }

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'aniversario' => 'date',
            'privado' => 'boolean',
            'role' => 'string', // Adicionado cast para role
            'active' => 'boolean', // Adicionado cast para active
            'last_seen' => 'datetime',
            'status' => 'string',
            'daily_access_start' => 'datetime', // Controle de tempo diário
            'daily_minutes_used' => 'integer', // Minutos usados hoje
        ];
    }

    /**
     * Set the aniversario attribute.
     * Convert empty strings to null to avoid database errors.
     */
    public function setAniversarioAttribute($value)
    {
        $this->attributes['aniversario'] = $value === '' ? null : $value;
    }

    /**
     * Set the sexo attribute.
     * Convert empty strings to null to avoid database errors.
     */
    public function setSexoAttribute($value)
    {
        $this->attributes['sexo'] = $value === '' ? null : $value;
    }

    /**
     * Accessor inteligente de status do usuário
     * Determina o status real do usuário com base em seu status definido e última atividade
     */
    public function getPresenceStatusAttribute()
    {
        // Se o status for definido manualmente como "dnd" (não perturbe), respeite isso
        if ($this->status === 'dnd') {
            return 'dnd';
        }

        // Se o status for definido manualmente como "away", respeite isso
        if ($this->status === 'away') {
            return 'away';
        }

        // Se o status for definido como "offline", respeite isso
        if ($this->status === 'offline') {
            return 'offline';
        }

        // Se o usuário estiver online mas não tiver atividade recente, considere-o offline
        if (!$this->last_seen) {
            return 'offline';
        }

        // Verifica se o usuário esteve ativo nos últimos 5 minutos
        if ($this->last_seen->diffInMinutes(now()) < 5) {
            return 'online';
        }

        // Se o usuário esteve ativo nos últimos 15 minutos, considere-o away
        if ($this->last_seen->diffInMinutes(now()) < 15) {
            return 'away';
        }

        // Caso contrário, considere-o offline
        return 'offline';
    }

    /**
     * Verifica se o usuário está em modo "não perturbe"
     */
    public function isDoNotDisturb()
    {
        return $this->status === 'dnd';
    }

    /**
     * Relacionamento com estatísticas de tempo online
     */
    public function onlineStats()
    {
        return $this->hasMany(UserOnlineStat::class);
    }

    /**
     * Relacionamento com atividades do usuário
     */
    public function activities()
    {
        return $this->hasMany(UserActivity::class);
    }

    /**
     * Obter estatísticas de tempo online para hoje
     */
    public function getTodayOnlineStatsAttribute()
    {
        return UserOnlineStat::getOrCreateForToday($this->id);
    }

    /**
     * Obter estatísticas de tempo online para a semana atual
     */
    public function getCurrentWeekOnlineStatsAttribute()
    {
        return UserOnlineStat::getCurrentWeekStats($this->id);
    }

    /**
     * Obter estatísticas de tempo online para o mês atual
     */
    public function getCurrentMonthOnlineStatsAttribute()
    {
        return UserOnlineStat::getCurrentMonthStats($this->id);
    }

    /**
     * Verifica se o usuário possui determinado papel
     */
    public function hasRole($role)
    {
        return $this->role === $role;
    }

    /**
     * Grupos que o usuário criou
     */
    public function createdGroups()
    {
        return $this->hasMany(Group::class, 'creator_id');
    }

    /**
     * Grupos dos quais o usuário é membro
     */
    public function groups()
    {
        return $this->belongsToMany(Group::class, 'group_user')
            ->withPivot('role', 'is_approved', 'joined_at')
            ->withTimestamps();
    }

    /**
     * Grupos dos quais o usuário é administrador
     */
    public function adminGroups()
    {
        return $this->belongsToMany(Group::class, 'group_user')
            ->wherePivot('role', 'admin')
            ->withPivot('joined_at')
            ->withTimestamps();
    }

    /**
     * Grupos dos quais o usuário é moderador
     */
    public function moderatedGroups()
    {
        return $this->belongsToMany(Group::class, 'group_user')
            ->wherePivot('role', 'moderator')
            ->withPivot('joined_at')
            ->withTimestamps();
    }

    /**
     * Convites de grupo recebidos pelo usuário
     */
    public function groupInvitations()
    {
        return $this->hasMany(GroupInvitation::class, 'user_id');
    }

    /**
     * Convites de grupo enviados pelo usuário
     */
    public function sentGroupInvitations()
    {
        return $this->hasMany(GroupInvitation::class, 'invited_by');
    }

    /**
     * Verifica se o usuário é membro de um grupo
     */
    public function isMemberOf(Group $group)
    {
        return $this->groups()
            ->where('group_id', $group->id)
            ->where('is_approved', true)
            ->exists();
    }

    /**
     * Verifica se o usuário é administrador de um grupo
     */
    public function isAdminOf(Group $group)
    {
        return $this->adminGroups()
            ->where('group_id', $group->id)
            ->exists();
    }

    /**
     * Verifica se o usuário é moderador de um grupo
     */
    public function isModeratorOf(Group $group)
    {
        return $this->moderatedGroups()
            ->where('group_id', $group->id)
            ->exists();
    }

    /**
     * Verifica se o usuário pode gerenciar um grupo
     */
    public function canManageGroup(Group $group)
    {
        return $this->isAdminOf($group) || $this->isModeratorOf($group);
    }

    /**
     * Eventos criados pelo usuário
     */
    public function createdEvents()
    {
        return $this->hasMany(Event::class, 'created_by');
    }

    /**
     * Eventos que o usuário está participando
     */
    public function attendingEvents()
    {
        return $this->belongsToMany(Event::class, 'event_attendees')
            ->withPivot('status', 'ticket_code', 'payment_status', 'payment_method', 'payment_id', 'amount_paid', 'paid_at', 'checked_in_at')
            ->withTimestamps();
    }

    /**
     * Verifica se o usuário é administrador
     */
    public function isAdmin()
    {
        return $this->role === 'admin';
    }

    /**
     * Verifica se o usuário está ativo
     */
    public function isActive()
    {
        return $this->active;
    }

    /**
     * Scope para filtrar apenas usuários ativos
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    /**
     * Scope para filtrar apenas usuários inativos
     */
    public function scopeInactive($query)
    {
        return $query->where('active', false);
    }

    /**
     * Matches que o usuário iniciou (deu like ou pass)
     */
    public function initiatedMatches()
    {
        return $this->hasMany(UserMatch::class, 'user_id');
    }

    /**
     * Matches em que o usuário é o alvo
     */
    public function receivedMatches()
    {
        return $this->hasMany(UserMatch::class, 'target_user_id');
    }

    /**
     * Usuários que deram match com este usuário
     */
    public function matchedUsers()
    {
        return $this->belongsToMany(User::class, 'user_matches', 'user_id', 'target_user_id')
            ->wherePivot('is_matched', true)
            ->withPivot('matched_at')
            ->withTimestamps();
    }

    /**
     * Verifica se o usuário deu match com outro usuário
     */
    public function hasMatchWith(User $user)
    {
        return $this->initiatedMatches()
            ->where('target_user_id', $user->id)
            ->where('is_matched', true)
            ->exists();
    }

    /**
     * Get the user's initials
     */
    public function initials()
    {
        return Str::of($this->name)
            ->explode(' ')
            ->map(fn(string $name) => Str::of($name)->substr(0, 1))
            ->implode('');
    }

    /**
     * Pedidos do usuário
     */
    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Get the user's wallet
     */
    public function wallet()
    {
        return $this->hasOne(Wallet::class);
    }

    /**
     * Get the user's wallet transactions
     */
    public function walletTransactions()
    {
        return $this->hasMany(WalletTransaction::class);
    }

    /**
     * Get or create the user's wallet
     */
    public function getWalletAttribute()
    {
        try {
            // Verificar se a tabela de wallets existe
            if (!\Schema::hasTable('wallets')) {
                logger()->warning('Tabela de wallets não existe. Não foi possível obter wallet para o usuário: ' . $this->id);
                return null;
            }

            // First try to find the existing wallet
            $wallet = $this->wallet()->first();

            if ($wallet) {
                // Existing wallet found, return it
                return $wallet;
            } else {
                // No wallet found, create a new one with default values
                logger()->info('Creating new wallet for user', [
                    'user_id' => $this->id,
                    'initial_balance' => 0.00
                ]);

                return $this->wallet()->create([
                    'balance' => 0.00,
                    'active' => true,
                ]);
            }
        } catch (\Exception $e) {
            logger()->error('Erro ao obter/criar wallet: ' . $e->getMessage(), [
                'user_id' => $this->id,
                'exception' => $e
            ]);
            return null;
        }
    }

    /**
     * Get the user's wallet balance formatted
     */
    public function getFormattedWalletBalanceAttribute()
    {
        $wallet = $this->wallet;
        if ($wallet) {
            return 'R$ ' . number_format($wallet->balance, 2, ',', '.');
        }
        return 'R$ 0,00';
    }

    /**
     * Assinaturas VIP do usuário
     */
    public function vipSubscriptions()
    {
        return $this->hasMany(VipSubscription::class);
    }

    /**
     * Assinatura VIP ativa atual
     */
    public function activeVipSubscription()
    {
        return $this->hasOne(VipSubscription::class)
            ->active()
            ->latest('activated_at');
    }

    /**
     * Verifica se o usuário tem assinatura VIP ativa
     */
    public function hasActiveVipSubscription()
    {
        return $this->activeVipSubscription()->exists();
    }

    /**
     * Verifica se o usuário está em período de trial
     */
    public function onVipTrial()
    {
        $subscription = $this->activeVipSubscription;
        return $subscription && $subscription->onTrial();
    }

    /**
     * Retorna os dias restantes da assinatura VIP
     */
    public function getVipRemainingDays()
    {
        $subscription = $this->activeVipSubscription;
        return $subscription ? $subscription->getRemainingDays() : 0;
    }

    /**
     * Retorna os dias restantes do trial VIP
     */
    public function getVipTrialRemainingDays()
    {
        $subscription = $this->activeVipSubscription;
        return $subscription ? $subscription->getRemainingTrialDays() : 0;
    }

    /**
     * Visitas que este usuário fez a outros perfis
     */
    public function profileVisitsMade(): HasMany
    {
        return $this->hasMany(ProfileVisit::class, 'visitor_id');
    }

    /**
     * Visitas que outros usuários fizeram ao perfil deste usuário
     */
    public function profileVisitsReceived(): HasMany
    {
        return $this->hasMany(ProfileVisit::class, 'visited_id');
    }

    /**
     * Registra uma visita ao perfil de outro usuário
     */
    public function visitProfile(User $user): void
    {
        // Não registrar visitas ao próprio perfil
        if ($this->id === $user->id) {
            return;
        }

        // Registrar a visita
        $this->profileVisitsMade()->create([
            'visited_id' => $user->id,
            'visited_at' => now(),
        ]);
    }

    /**
     * Send the email verification notification.
     */
    public function sendEmailVerificationNotification()
    {
        $this->notify(new CustomVerifyEmail);
    }

    /**
     * Planos de noite criados pelo usuário
     */
    public function nightBoardPlans()
    {
        return $this->hasMany(NightBoardPlan::class);
    }

    /**
     * Patrocínios feitos pelo usuário
     */
    public function nightBoardSupports()
    {
        return $this->hasMany(NightBoardSupport::class);
    }

    /**
     * Tickets de suporte do usuário
     */
    public function supportTickets()
    {
        return $this->hasMany(SupportTicket::class);
    }

    /**
     * Tickets de suporte atribuídos ao usuário (para admins)
     */
    public function assignedTickets()
    {
        return $this->hasMany(SupportTicket::class, 'assigned_to');
    }

    // Relacionamento com conquistas
    public function achievements()
    {
        return $this->belongsToMany(Achievement::class, 'user_achievements')->withTimestamps();
    }

    // Boot method para registrar observers
    protected static function boot()
    {
        parent::boot();

        // REMOVIDO: Observer para o evento pivotAttached no relacionamento followers (lógica movida para UserObserver)
        // static::pivotAttached(function ($relation, $pivotIds) {
        //     // Verificar se o relacionamento é 'followers' e se está anexando (seguindo)
        //     if ($relation === 'followers') {
        //         // $pivotIds contém o ID do follower (quem começou a seguir)
        //         // $this refere-se ao modelo User que está sendo seguido

        //         $followedUser = $this; // O usuário que ganhou um seguidor
        //         $followersCount = $followedUser->followers()->count(); // Contagem ATUAL de seguidores

        //         // Buscar a conquista pelo nome (ajuste o nome conforme criado no admin)
        //         $influencerAchievement = \App\Models\Achievement::where('name', 'Influenciador Inicial')->first();

        //         // TODO: O limiar de seguidores para a conquista deve ser buscado do banco de dados ou configurado.
        //         // Por enquanto, usaremos um valor fixo (ex: 50 seguidores)
        //         $followersThreshold = 50; // Exemplo de limiar de seguidores

        //         if ($influencerAchievement && $followersCount >= $followersThreshold) {
        //             // Verificar se o usuário seguido já tem a conquista
        //             if (!$followedUser->achievements->contains($influencerAchievement->id)) {
        //                 // Conceder a conquista
        //                 $followedUser->achievements()->attach($influencerAchievement->id);

        //                 // Adicionar os pontos da conquista
        //                 \App\Models\UserPoint::addPoints(
        //                     $followedUser->id,
        //                     'achievement',
        //                     $influencerAchievement->points,
        //                     "Ganhou a conquista \"" . $influencerAchievement->name . "\"",
        //                     $influencerAchievement->id,
        //                     \App\Models\Achievement::class
        //                 );

        //                 // Opcional: disparar evento para notificar o usuário no frontend
        //                 // $followedUser->notify(new AchievementUnlocked($influencerAchievement));
        //             }
        //         }
        //     }
        // });
    }

    /**
     * Get the user's current photo (where is_current is true).
     */
    public function currentPhoto(): HasOne
    {
        return $this->hasOne(UserPhoto::class)->where('is_current', true);
    }

    /**
     * Labels do usuário
     */
    public function labels()
    {
        return $this->hasMany(UserLabel::class);
    }

    /**
     * Labels ativas do usuário
     */
    public function activeLabels()
    {
        return $this->hasMany(UserLabel::class)->active();
    }

    /**
     * Verifica se o usuário tem uma label específica
     */
    public function hasLabel(string $labelName): bool
    {
        return $this->activeLabels()->where('name', $labelName)->exists();
    }
}
