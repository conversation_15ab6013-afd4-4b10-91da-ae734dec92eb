<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PremiumAlbumAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @param  string  $action
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next, $action = null)
    {
        $user = Auth::user();

        if (!$user) {
            return redirect()->route('login')->with('error', 'Você precisa estar logado para acessar esta funcionalidade.');
        }

        switch ($action) {
            case 'create':
                return $this->checkCreatePermission($user, $request, $next);
            
            case 'purchase':
                return $this->checkPurchasePermission($user, $request, $next);
            
            case 'moderate':
                return $this->checkModeratePermission($user, $request, $next);
            
            case 'admin':
                return $this->checkAdminPermission($user, $request, $next);
            
            default:
                return $next($request);
        }
    }

    /**
     * Verifica permissão para criar álbuns premium.
     */
    private function checkCreatePermission($user, $request, $next)
    {
        if ($user->role === 'visitante') {
            return redirect()->back()->with('error', 'Apenas usuários VIP podem criar álbuns premium. Faça upgrade para VIP!');
        }

        return $next($request);
    }

    /**
     * Verifica permissão para comprar álbuns.
     */
    private function checkPurchasePermission($user, $request, $next)
    {
        if ($user->role === 'visitante') {
            return redirect()->back()->with('error', 'Apenas usuários VIP podem comprar álbuns premium. Faça upgrade para VIP!');
        }

        return $next($request);
    }

    /**
     * Verifica permissão para moderar conteúdo.
     */
    private function checkModeratePermission($user, $request, $next)
    {
        if ($user->role !== 'administrador') {
            abort(403, 'Acesso negado. Apenas administradores podem moderar conteúdo.');
        }

        return $next($request);
    }

    /**
     * Verifica permissão para funcionalidades administrativas.
     */
    private function checkAdminPermission($user, $request, $next)
    {
        if ($user->role !== 'administrador') {
            abort(403, 'Acesso negado. Apenas administradores podem acessar esta área.');
        }

        return $next($request);
    }
}
