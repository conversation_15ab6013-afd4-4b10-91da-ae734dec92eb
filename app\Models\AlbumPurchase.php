<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AlbumPurchase extends Model
{
    use HasFactory;

    protected $fillable = [
        'album_id',
        'buyer_id',
        'seller_id',
        'amount',
        'commission',
        'seller_amount',
        'payment_method',
        'stripe_session_id',
        'stripe_payment_intent_id',
        'status',
        'purchased_at',
        'notes',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'commission' => 'decimal:2',
        'seller_amount' => 'decimal:2',
        'purchased_at' => 'datetime',
    ];

    /**
     * Status possíveis para uma compra.
     */
    const STATUS_PENDING = 'pending';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED = 'failed';
    const STATUS_REFUNDED = 'refunded';

    /**
     * Álbum comprado.
     */
    public function album()
    {
        return $this->belongsTo(Album::class);
    }

    /**
     * Usuário comprador.
     */
    public function buyer()
    {
        return $this->belongsTo(User::class, 'buyer_id');
    }

    /**
     * Usuário vendedor.
     */
    public function seller()
    {
        return $this->belongsTo(User::class, 'seller_id');
    }

    /**
     * Ganhos relacionados a esta compra.
     */
    public function earnings()
    {
        return $this->hasMany(AlbumEarning::class, 'purchase_id');
    }

    /**
     * Scope para compras completadas.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', self::STATUS_COMPLETED);
    }

    /**
     * Scope para compras pendentes.
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * Scope para compras por método de pagamento.
     */
    public function scopeByPaymentMethod($query, $method)
    {
        return $query->where('payment_method', $method);
    }

    /**
     * Verifica se a compra foi completada.
     */
    public function isCompleted()
    {
        return $this->status === self::STATUS_COMPLETED;
    }

    /**
     * Verifica se a compra está pendente.
     */
    public function isPending()
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Marca a compra como completada.
     */
    public function markAsCompleted()
    {
        $this->update([
            'status' => self::STATUS_COMPLETED,
            'purchased_at' => now(),
        ]);
    }

    /**
     * Marca a compra como falhada.
     */
    public function markAsFailed($reason = null)
    {
        $this->update([
            'status' => self::STATUS_FAILED,
            'notes' => $reason,
        ]);
    }
}
