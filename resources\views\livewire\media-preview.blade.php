<div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md p-6">
    <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <flux:icon.photo class="w-5 h-5 mr-2 text-purple-500" />
            Fotos e Vídeos
            @if($totalCount > 0)
                <span class="ml-2 text-sm text-gray-600 dark:text-gray-400">({{ $totalCount }})</span>
            @endif
        </h3>
        
        @if($totalCount > 9)
            <a href="#media-gallery" class="text-purple-600 hover:text-purple-700 dark:text-purple-400 dark:hover:text-purple-300 text-sm font-medium">
                Ver todas
            </a>
        @endif
    </div>

    @if($recentMedias->count() > 0)
        <div class="grid grid-cols-3 gap-2">
            @foreach($recentMedias as $index => $media)
                <div class="aspect-square bg-gray-200 dark:bg-zinc-600 rounded-lg overflow-hidden hover:shadow-lg transition-shadow cursor-pointer group relative"
                     onclick="document.getElementById('media-gallery').scrollIntoView({ behavior: 'smooth' })">
                    @if($media->type === 'photo')
                        <img src="{{ $media->thumbnail_url }}" 
                             alt="{{ $media->title }}"
                             class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                    @else
                        <div class="w-full h-full bg-gray-800 flex items-center justify-center relative">
                            <img src="{{ $media->thumbnail_url }}" 
                                 alt="{{ $media->title }}"
                                 class="w-full h-full object-cover opacity-70">
                            <div class="absolute inset-0 flex items-center justify-center">
                                <flux:icon.play class="w-6 h-6 text-white drop-shadow-lg" />
                            </div>
                        </div>
                    @endif
                    
                    {{-- Overlay para mostrar mais fotos --}}
                    @if($index === 8 && $totalCount > 9)
                        <div class="absolute inset-0 bg-black bg-opacity-60 flex items-center justify-center">
                            <div class="text-white text-center">
                                <div class="text-xl font-bold">+{{ $totalCount - 9 }}</div>
                                <div class="text-xs">mais</div>
                            </div>
                        </div>
                    @endif
                </div>
            @endforeach
        </div>

        {{-- Estatísticas Rápidas --}}
        <div class="mt-4 flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
            <div class="flex items-center space-x-4">
                <span class="flex items-center">
                    <flux:icon.photo class="w-4 h-4 mr-1" />
                    {{ $recentMedias->where('type', 'photo')->count() }} fotos
                </span>
                <span class="flex items-center">
                    <flux:icon.video-camera class="w-4 h-4 mr-1" />
                    {{ $recentMedias->where('type', 'video')->count() }} vídeos
                </span>
            </div>
            
            @if($user->albums()->count() > 0)
                <span class="flex items-center">
                    <flux:icon.folder class="w-4 h-4 mr-1" />
                    {{ $user->albums()->count() }} álbuns
                </span>
            @endif
        </div>
    @else
        <div class="text-center py-8">
            <flux:icon.photo class="w-12 h-12 text-gray-400 mx-auto mb-3" />
            <h4 class="font-medium text-gray-900 dark:text-white mb-1">
                Nenhuma mídia
            </h4>
            <p class="text-sm text-gray-600 dark:text-gray-400">
                {{ Auth::check() && Auth::id() === $user->id ? 'Adicione suas primeiras fotos e vídeos.' : 'Este usuário ainda não possui mídias públicas.' }}
            </p>
        </div>
    @endif
</div>
