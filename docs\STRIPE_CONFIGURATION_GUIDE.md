# Guia de Configuração Stripe - Loja e Assinaturas

## Status Atual da Configuração

### ✅ **O que já está configurado:**

1. **Stripe Keys**: Configuradas no `.env` com chaves LIVE
2. **<PERSON>vel Cashier**: Instalado e configurado
3. **Webhook**: Configurado com secret
4. **Moeda**: BRL (Real Brasileiro) configurado
5. **Checkout da Loja**: Funcionando com produtos dinâmicos
6. **Sistema VIP**: Integrado com processamento automático

### 🔧 **Como o Sistema Atual Funciona:**

#### **1. Produtos da Loja (Funcionamento Atual)**
- **Criação Dinâmica**: Produtos são criados dinamicamente no Stripe durante o checkout
- **Sem Produtos Pré-criados**: Não precisam ser criados previamente no dashboard Stripe
- **Processamento**: Usa `price_data` para criar preços na hora

<augment_code_snippet path="app/Livewire/Shop/Checkout.php" mode="EXCERPT">
````php
$line_items[] = [
    'price_data' => [
        'currency' => 'brl',
        'product_data' => [
            'name' => $item->product->name,
            'images' => [$item->product->getImageUrl() ?? 'https://placehold.co/200'],
        ],
        'unit_amount' => (int)($item->price * 100),
    ],
    'quantity' => $item->quantity,
];
````
</augment_code_snippet>

#### **2. Produtos VIP (Sistema Implementado)**
- **Detecção Automática**: Sistema detecta produtos com `grants_vip_access = true`
- **Processamento Duplo**: Controller + Webhook para garantir processamento
- **Ativação VIP**: Automática após pagamento confirmado

#### **3. Fluxo de Pagamento Atual**
```
Produto Adicionado → Carrinho → Checkout → Stripe Session → Pagamento → Webhook → Processamento VIP
```

## 📋 **Para Assinaturas Recorrentes (Se Necessário)**

### **Opção 1: Sistema Atual (Recomendado)**
O sistema atual já funciona perfeitamente para "assinaturas" VIP:
- Usuário compra produto VIP (pagamento único)
- Sistema ativa VIP por período determinado
- Usuário pode renovar comprando novamente

### **Opção 2: Assinaturas Stripe Nativas**
Se quiser assinaturas recorrentes automáticas:

#### **1. Criar Produtos no Stripe Dashboard**
```bash
# Exemplo de produtos que precisariam ser criados:
- VIP Mensal (R$ 29,90/mês)
- VIP Trimestral (R$ 69,90/3 meses)  
- VIP Anual (R$ 199,90/ano)
```

#### **2. Configurar Preços Recorrentes**
- Acessar [Stripe Dashboard](https://dashboard.stripe.com/products)
- Criar produtos com preços recorrentes
- Copiar `price_id` para configuração

#### **3. Modificar Checkout para Assinaturas**
```php
// Em vez de 'mode' => 'payment'
'mode' => 'subscription',
'line_items' => [[
    'price' => 'price_1234567890', // Price ID do Stripe
    'quantity' => 1,
]]
```

## 🛍️ **Garantindo que Produtos Usem Stripe**

### **Sistema Atual (Já Implementado)**

#### **1. Métodos de Pagamento Disponíveis**
- **Cartão de Crédito**: Sempre usa Stripe ✅
- **PIX/Boleto**: Usa Stripe (se configurado) ou gateway nacional
- **Carteira**: Usa saldo interno do usuário

#### **2. Forçar Stripe para Todos os Produtos**

<augment_code_snippet path="app/Livewire/Shop/Checkout.php" mode="EXCERPT">
````php
public function placeOrder()
{
    // Para forçar apenas Stripe, modificar esta lógica:
    if ($this->paymentMethod === 'credit_card') {
        return $this->redirectToStripeCheckout($order);
    }
    
    // Para forçar TODOS os pagamentos via Stripe:
    // return $this->redirectToStripeCheckout($order);
}
````
</augment_code_snippet>

#### **3. Configuração de Métodos de Pagamento**

Para garantir que apenas Stripe seja usado:

```php
// No componente Checkout, modificar:
protected function rules()
{
    return [
        'paymentMethod' => 'required|in:credit_card', // Apenas cartão
        'notes' => 'nullable|string|max:500',
    ];
}
```

## 🔧 **Configurações Recomendadas**

### **1. Para Produtos Únicos (Atual)**
✅ **Manter sistema atual** - funciona perfeitamente
- Produtos criados dinamicamente
- Processamento VIP automático
- Flexibilidade total

### **2. Para Assinaturas Recorrentes**
Se quiser implementar:

#### **Passo 1: Criar Produtos no Stripe**
1. Acessar [Stripe Dashboard](https://dashboard.stripe.com/products)
2. Criar produto "VIP Mensal"
3. Adicionar preço recorrente R$ 29,90/mês
4. Copiar `price_id`

#### **Passo 2: Configurar no Laravel**
```php
// config/stripe-products.php
return [
    'vip_monthly' => 'price_1234567890',
    'vip_quarterly' => 'price_0987654321',
    'vip_yearly' => 'price_1122334455',
];
```

#### **Passo 3: Modificar VipPurchaseService**
```php
// Para produtos VIP com assinatura recorrente
if ($product->vip_plan_type === 'subscription') {
    // Usar price_id do Stripe
    $priceId = config('stripe-products.' . $product->slug);
    // Criar checkout session com mode: 'subscription'
}
```

## 🎯 **Recomendação Final**

### **Para sua situação atual:**

1. **✅ Manter sistema atual** para produtos VIP
   - Funciona perfeitamente
   - Flexível e confiável
   - Já implementado e testado

2. **✅ Todos os produtos já usam Stripe** quando:
   - Usuário escolhe "Cartão de Crédito"
   - Sistema redireciona automaticamente para Stripe

3. **🔧 Se quiser forçar apenas Stripe:**
   - Remover opções PIX/Boleto/Carteira do checkout
   - Manter apenas "credit_card" como opção

4. **🔧 Se quiser assinaturas recorrentes:**
   - Criar produtos específicos no Stripe Dashboard
   - Modificar checkout para mode: 'subscription'
   - Implementar cancelamento de assinaturas

## 📊 **Status dos Sistemas**

| Sistema | Status | Stripe | Observações |
|---------|--------|--------|-------------|
| Produtos Loja | ✅ Funcionando | ✅ Sim | Criação dinâmica |
| Produtos VIP | ✅ Funcionando | ✅ Sim | Processamento automático |
| Carteira | ✅ Funcionando | ❌ Não | Saldo interno |
| PIX/Boleto | ⚠️ Configurável | ⚠️ Opcional | Pode usar Stripe ou outro |

**Conclusão**: Seu sistema já está configurado corretamente para usar Stripe com produtos dinâmicos. Não é necessário criar produtos previamente no Stripe Dashboard para o funcionamento atual.

---

**Data**: 31/07/2025  
**Status**: Sistema Funcionando ✅
