<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_activities', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('cascade');
            $table->string('session_id')->nullable(); // Para tracking de usuários não logados
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            
            // Dados da atividade
            $table->string('activity_type'); // 'page_view', 'click', 'form_submit', 'download', etc.
            $table->string('url')->nullable();
            $table->string('route_name')->nullable();
            $table->string('method', 10)->default('GET'); // GET, POST, PUT, DELETE
            $table->json('parameters')->nullable(); // Parâmetros da requisição
            
            // Dados específicos do evento
            $table->string('element_type')->nullable(); // 'button', 'link', 'form', etc.
            $table->string('element_id')->nullable();
            $table->string('element_class')->nullable();
            $table->text('element_text')->nullable();
            $table->string('page_title')->nullable();
            
            // Dados de performance e comportamento
            $table->integer('time_on_page')->nullable(); // Tempo em segundos
            $table->integer('scroll_depth')->nullable(); // Porcentagem de scroll
            $table->string('referrer')->nullable();
            $table->json('viewport_size')->nullable(); // {width: 1920, height: 1080}
            $table->string('device_type')->nullable(); // 'desktop', 'mobile', 'tablet'
            
            // Dados contextuais
            $table->json('additional_data')->nullable(); // Dados extras específicos
            $table->timestamps();
            
            // Índices para performance
            $table->index(['user_id', 'created_at']);
            $table->index(['activity_type', 'created_at']);
            $table->index(['url', 'created_at']);
            $table->index(['session_id', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_activities');
    }
};
