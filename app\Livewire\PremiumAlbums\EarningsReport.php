<?php

namespace App\Livewire\PremiumAlbums;

use App\Models\AlbumEarning;
use App\Models\AlbumPurchase;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class EarningsReport extends Component
{
    use WithPagination;

    public $period = 'all'; // all, today, week, month, year
    public $startDate = '';
    public $endDate = '';
    
    public $showDetails = false;
    public $selectedAlbumId = null;

    protected $queryString = [
        'period' => ['except' => 'all'],
        'startDate' => ['except' => ''],
        'endDate' => ['except' => ''],
    ];

    public function mount()
    {
        $this->endDate = now()->format('Y-m-d');
        $this->startDate = now()->subDays(30)->format('Y-m-d');
    }

    public function updatedPeriod()
    {
        $this->resetPage();
        
        switch ($this->period) {
            case 'today':
                $this->startDate = now()->format('Y-m-d');
                $this->endDate = now()->format('Y-m-d');
                break;
            case 'week':
                $this->startDate = now()->startOfWeek()->format('Y-m-d');
                $this->endDate = now()->endOfWeek()->format('Y-m-d');
                break;
            case 'month':
                $this->startDate = now()->startOfMonth()->format('Y-m-d');
                $this->endDate = now()->endOfMonth()->format('Y-m-d');
                break;
            case 'year':
                $this->startDate = now()->startOfYear()->format('Y-m-d');
                $this->endDate = now()->endOfYear()->format('Y-m-d');
                break;
        }
    }

    public function showAlbumDetails($albumId)
    {
        $this->selectedAlbumId = $albumId;
        $this->showDetails = true;
    }

    public function hideDetails()
    {
        $this->showDetails = false;
        $this->selectedAlbumId = null;
    }

    public function getStatsProperty()
    {
        $user = Auth::user();
        $query = $user->albumEarnings();

        // Aplicar filtro de período
        if ($this->period !== 'all') {
            $query->whereBetween('created_at', [
                Carbon::parse($this->startDate)->startOfDay(),
                Carbon::parse($this->endDate)->endOfDay()
            ]);
        }

        return [
            'total_earnings' => $query->sum('net_amount'),
            'total_sales' => $query->count(),
            'pending_earnings' => $query->where('status', 'pending')->sum('net_amount'),
            'paid_earnings' => $query->where('status', 'paid')->sum('net_amount'),
            'average_sale' => $query->avg('net_amount') ?? 0,
        ];
    }

    public function getTopAlbumsProperty()
    {
        $user = Auth::user();
        
        return $user->premiumAlbums()
                   ->select('albums.*')
                   ->selectRaw('COUNT(album_purchases.id) as sales_count')
                   ->selectRaw('SUM(album_earnings.net_amount) as total_earnings')
                   ->leftJoin('album_purchases', 'albums.id', '=', 'album_purchases.album_id')
                   ->leftJoin('album_earnings', 'albums.id', '=', 'album_earnings.album_id')
                   ->where('album_purchases.status', 'completed')
                   ->groupBy('albums.id')
                   ->orderByDesc('total_earnings')
                   ->limit(5)
                   ->get();
    }

    public function getRecentSalesProperty()
    {
        $user = Auth::user();
        $query = $user->albumSales()
                     ->with(['album', 'buyer'])
                     ->where('status', 'completed')
                     ->orderBy('purchased_at', 'desc');

        // Aplicar filtro de período
        if ($this->period !== 'all') {
            $query->whereBetween('purchased_at', [
                Carbon::parse($this->startDate)->startOfDay(),
                Carbon::parse($this->endDate)->endOfDay()
            ]);
        }

        return $query->paginate(10);
    }

    public function getChartDataProperty()
    {
        $user = Auth::user();
        $days = 30;
        
        $data = [];
        for ($i = $days - 1; $i >= 0; $i--) {
            $date = now()->subDays($i);
            
            $earnings = $user->albumEarnings()
                           ->whereDate('created_at', $date)
                           ->sum('net_amount');
            
            $data[] = [
                'date' => $date->format('d/m'),
                'earnings' => (float) $earnings,
            ];
        }
        
        return $data;
    }

    public function getSelectedAlbumDetailsProperty()
    {
        if (!$this->selectedAlbumId) {
            return null;
        }

        $album = Auth::user()->premiumAlbums()->findOrFail($this->selectedAlbumId);
        
        $sales = $album->purchases()
                      ->with('buyer')
                      ->where('status', 'completed')
                      ->orderBy('purchased_at', 'desc')
                      ->get();

        $earnings = $album->earnings()
                         ->orderBy('created_at', 'desc')
                         ->get();

        return [
            'album' => $album,
            'sales' => $sales,
            'earnings' => $earnings,
            'total_earnings' => $earnings->sum('net_amount'),
            'total_sales' => $sales->count(),
        ];
    }

    public function render()
    {
        return view('livewire.premium-albums.earnings-report', [
            'stats' => $this->stats,
            'topAlbums' => $this->topAlbums,
            'recentSales' => $this->recentSales,
            'chartData' => $this->chartData,
            'selectedAlbumDetails' => $this->selectedAlbumDetails,
        ]);
    }
}
