<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class AnalyzeProblematicFiles extends Command
{
    protected $signature = 'files:analyze-problems {differences-file=differences.txt} {--interactive}';
    protected $description = 'Analisa arquivos problemáticos e sugere ações';

    private $criticalFiles = [];
    private $layoutFiles = [];
    private $configFiles = [];
    private $assetFiles = [];
    private $otherFiles = [];

    public function handle()
    {
        $differencesFile = $this->argument('differences-file');
        $interactive = $this->option('interactive');
        
        if (!File::exists($differencesFile)) {
            $this->error("❌ Arquivo não encontrado: {$differencesFile}");
            return 1;
        }
        
        $this->info('🔍 Analisando arquivos problemáticos...');
        
        $onlyFtpFiles = $this->parseOnlyFtpFiles($differencesFile);
        
        if (empty($onlyFtpFiles)) {
            $this->info('✅ Nenhum arquivo problemático encontrado!');
            return 0;
        }
        
        $this->categorizeFiles($onlyFtpFiles);
        $this->displayAnalysis();
        
        if ($interactive) {
            $this->interactiveMode();
        } else {
            $this->generateRemovalScript();
        }
        
        return 0;
    }
    
    private function parseOnlyFtpFiles($filePath)
    {
        $content = File::get($filePath);
        $lines = explode("\n", $content);
        $files = [];
        $inOnlyFtpSection = false;
        
        foreach ($lines as $line) {
            $line = trim($line);
            
            if (str_contains($line, 'Arquivos que existem APENAS no FTP')) {
                $inOnlyFtpSection = true;
                continue;
            }
            
            if ($inOnlyFtpSection && str_starts_with($line, '##')) {
                break;
            }
            
            if ($inOnlyFtpSection && str_starts_with($line, '- ')) {
                $filePath = substr($line, 2);
                $filePath = explode(' (', $filePath)[0]; // Remove info de data/tamanho
                $files[] = $filePath;
            }
        }
        
        return $files;
    }
    
    private function categorizeFiles($files)
    {
        foreach ($files as $file) {
            $category = $this->categorizeFile($file);
            
            switch ($category) {
                case 'critical':
                    $this->criticalFiles[] = $file;
                    break;
                case 'layout':
                    $this->layoutFiles[] = $file;
                    break;
                case 'config':
                    $this->configFiles[] = $file;
                    break;
                case 'asset':
                    $this->assetFiles[] = $file;
                    break;
                default:
                    $this->otherFiles[] = $file;
            }
        }
    }
    
    private function categorizeFile($file)
    {
        // Arquivos críticos do sistema
        if (preg_match('/\.(env|htaccess)$/', $file) || 
            str_contains($file, 'config/') ||
            str_contains($file, 'bootstrap/') ||
            $file === 'composer.json' ||
            $file === 'artisan') {
            return 'critical';
        }
        
        // Arquivos de layout/views
        if (str_contains($file, 'resources/views/') ||
            str_contains($file, 'resources/css/') ||
            str_contains($file, 'resources/js/') ||
            str_contains($file, 'public/css/') ||
            str_contains($file, 'public/js/')) {
            return 'layout';
        }
        
        // Arquivos de configuração
        if (str_contains($file, 'config/') ||
            preg_match('/\.(json|yaml|yml|xml)$/', $file)) {
            return 'config';
        }
        
        // Assets compilados
        if (str_contains($file, 'public/build/') ||
            str_contains($file, 'public/storage/') ||
            preg_match('/\.(css|js|map)$/', $file)) {
            return 'asset';
        }
        
        return 'other';
    }
    
    private function displayAnalysis()
    {
        $this->info("\n📊 ANÁLISE DE ARQUIVOS PROBLEMÁTICOS");
        $this->line("═══════════════════════════════════════");
        
        if (!empty($this->criticalFiles)) {
            $this->error("🚨 CRÍTICOS (" . count($this->criticalFiles) . " arquivos)");
            $this->warn("   Estes arquivos podem quebrar o sistema completamente!");
            foreach ($this->criticalFiles as $file) {
                $this->line("   - {$file}");
            }
            $this->line("");
        }
        
        if (!empty($this->layoutFiles)) {
            $this->warn("🎨 LAYOUT/VIEWS (" . count($this->layoutFiles) . " arquivos)");
            $this->line("   Estes arquivos podem estar causando problemas visuais:");
            foreach (array_slice($this->layoutFiles, 0, 10) as $file) {
                $this->line("   - {$file}");
            }
            if (count($this->layoutFiles) > 10) {
                $this->line("   ... e mais " . (count($this->layoutFiles) - 10) . " arquivos");
            }
            $this->line("");
        }
        
        if (!empty($this->assetFiles)) {
            $this->info("📦 ASSETS (" . count($this->assetFiles) . " arquivos)");
            $this->line("   Assets compilados que podem estar desatualizados:");
            foreach (array_slice($this->assetFiles, 0, 5) as $file) {
                $this->line("   - {$file}");
            }
            if (count($this->assetFiles) > 5) {
                $this->line("   ... e mais " . (count($this->assetFiles) - 5) . " arquivos");
            }
            $this->line("");
        }
        
        if (!empty($this->configFiles)) {
            $this->warn("⚙️ CONFIGURAÇÃO (" . count($this->configFiles) . " arquivos)");
            foreach ($this->configFiles as $file) {
                $this->line("   - {$file}");
            }
            $this->line("");
        }
        
        if (!empty($this->otherFiles)) {
            $this->line("📄 OUTROS (" . count($this->otherFiles) . " arquivos)");
            foreach (array_slice($this->otherFiles, 0, 5) as $file) {
                $this->line("   - {$file}");
            }
            if (count($this->otherFiles) > 5) {
                $this->line("   ... e mais " . (count($this->otherFiles) - 5) . " arquivos");
            }
        }
    }
    
    private function interactiveMode()
    {
        $this->info("\n🤔 MODO INTERATIVO");
        $this->line("═══════════════════");
        
        $toRemove = [];
        
        // Perguntar sobre cada categoria
        if (!empty($this->criticalFiles)) {
            $this->error("\n🚨 Arquivos CRÍTICOS encontrados!");
            $this->warn("CUIDADO: Remover estes arquivos pode quebrar o sistema!");
            if ($this->confirm('Deseja incluir arquivos críticos na remoção?', false)) {
                $toRemove = array_merge($toRemove, $this->criticalFiles);
            }
        }
        
        if (!empty($this->layoutFiles)) {
            $this->warn("\n🎨 Arquivos de LAYOUT encontrados!");
            if ($this->confirm('Deseja remover arquivos de layout/views?', true)) {
                $toRemove = array_merge($toRemove, $this->layoutFiles);
            }
        }
        
        if (!empty($this->assetFiles)) {
            $this->info("\n📦 Assets compilados encontrados!");
            if ($this->confirm('Deseja remover assets antigos?', true)) {
                $toRemove = array_merge($toRemove, $this->assetFiles);
            }
        }
        
        if (!empty($this->configFiles)) {
            $this->warn("\n⚙️ Arquivos de configuração encontrados!");
            if ($this->confirm('Deseja remover arquivos de configuração?', false)) {
                $toRemove = array_merge($toRemove, $this->configFiles);
            }
        }
        
        if (!empty($this->otherFiles)) {
            $this->line("\n📄 Outros arquivos encontrados!");
            if ($this->confirm('Deseja remover outros arquivos?', true)) {
                $toRemove = array_merge($toRemove, $this->otherFiles);
            }
        }
        
        if (!empty($toRemove)) {
            $this->generateRemovalScript($toRemove);
        } else {
            $this->info("✅ Nenhum arquivo selecionado para remoção.");
        }
    }
    
    private function generateRemovalScript($filesToRemove = null)
    {
        if ($filesToRemove === null) {
            $filesToRemove = array_merge(
                $this->layoutFiles,
                $this->assetFiles,
                $this->otherFiles
                // Não incluir críticos e config por padrão
            );
        }
        
        if (empty($filesToRemove)) {
            $this->info("✅ Nenhum arquivo para remover.");
            return;
        }
        
        $scriptContent = $this->generatePhpRemovalScript($filesToRemove);
        
        File::put('public/remove-problematic-files.php', $scriptContent);
        
        $this->info("\n✅ Script de remoção gerado!");
        $this->line("📄 Arquivo: public/remove-problematic-files.php");
        $this->line("🌐 URL: https://www.swingcuritiba.com.br/remove-problematic-files.php");
        $this->line("📊 Arquivos a remover: " . count($filesToRemove));
        
        $this->warn("\n⚠️ INSTRUÇÕES:");
        $this->line("1. Faça upload do script para o FTP");
        $this->line("2. Acesse a URL no navegador");
        $this->line("3. Revise a lista antes de confirmar");
        $this->line("4. Delete o script após usar!");
    }
    
    private function generatePhpRemovalScript($files)
    {
        $filesJson = json_encode($files, JSON_PRETTY_PRINT);
        
        return "<?php
/**
 * Script para remover arquivos problemáticos do FTP
 * Execute acessando: https://www.swingcuritiba.com.br/remove-problematic-files.php
 * 
 * IMPORTANTE: Delete este arquivo após usar!
 */

// Verificar se está no ambiente correto
if (!file_exists('../artisan')) {
    die('❌ Erro: Execute no diretório public do Laravel');
}

// Mudar para o diretório raiz do Laravel
chdir('..');

// Verificar se é o ambiente de produção
if (!str_contains(\$_SERVER['HTTP_HOST'] ?? '', 'swingcuritiba.com.br')) {
    die('❌ Este script só pode ser executado na produção');
}

\$filesToRemove = {$filesJson};

\$action = \$_GET['action'] ?? 'preview';

echo \"<h1>🗑️ Remoção de Arquivos Problemáticos</h1>\\n\";
echo \"<p>Total de arquivos: \" . count(\$filesToRemove) . \"</p>\\n\";

if (\$action === 'preview') {
    echo \"<h2>📋 Arquivos que serão removidos:</h2>\\n\";
    echo \"<ul>\\n\";
    
    \$existingFiles = 0;
    foreach (\$filesToRemove as \$file) {
        \$exists = file_exists(\$file);
        \$status = \$exists ? '✅ Existe' : '❌ Não existe';
        \$color = \$exists ? 'green' : 'red';
        
        echo \"<li style='color: \$color'>\$status - \$file</li>\\n\";
        
        if (\$exists) {
            \$existingFiles++;
        }
    }
    
    echo \"</ul>\\n\";
    echo \"<p><strong>Arquivos existentes: \$existingFiles</strong></p>\\n\";
    
    if (\$existingFiles > 0) {
        echo \"<p><a href='?action=remove' style='background: red; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>🗑️ REMOVER ARQUIVOS</a></p>\\n\";
    }
    
    echo \"<p><em>⚠️ Revise cuidadosamente antes de remover!</em></p>\\n\";
    
} elseif (\$action === 'remove') {
    echo \"<h2>🗑️ Removendo arquivos...</h2>\\n\";
    
    \$removed = 0;
    \$errors = 0;
    
    foreach (\$filesToRemove as \$file) {
        if (file_exists(\$file)) {
            if (unlink(\$file)) {
                echo \"<p style='color: green'>✅ Removido: \$file</p>\\n\";
                \$removed++;
            } else {
                echo \"<p style='color: red'>❌ Erro ao remover: \$file</p>\\n\";
                \$errors++;
            }
        } else {
            echo \"<p style='color: gray'>⏭️ Não existe: \$file</p>\\n\";
        }
        
        // Flush output para mostrar progresso
        if (ob_get_level()) {
            ob_flush();
        }
        flush();
    }
    
    echo \"<h2>📊 Resultado:</h2>\\n\";
    echo \"<p>✅ Removidos: \$removed</p>\\n\";
    echo \"<p>❌ Erros: \$errors</p>\\n\";
    echo \"<p>✅ <strong>Processo concluído!</strong></p>\\n\";
    echo \"<p>⚠️ <strong>IMPORTANTE:</strong> Delete este script agora!</p>\\n\";
}

echo \"<hr>\\n\";
echo \"<p><small>Gerado em: \" . date('Y-m-d H:i:s') . \"</small></p>\\n\";
?>";
    }
}
