<?php

namespace App\Console\Commands;

use App\Models\Group;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class TestSecretGroup extends Command
{
    protected $signature = 'test:secret-group';
    protected $description = 'Testa a criação e funcionalidade de grupos secretos';

    public function handle()
    {
        $this->info('Iniciando teste de grupos secretos...');

        // Criar um usuário de teste
        $user = User::create([
            'name' => 'Usuário Teste',
            'username' => 'usuario_teste_' . now()->timestamp,
            'email' => 'teste' . now()->timestamp . '@exemplo.com',
            'password' => bcrypt('password'),
            'role' => 'vip',
            'active' => true,
        ]);

        $this->info("Usuário criado: {$user->name} (ID: {$user->id})");

        // Criar um grupo secreto
        $groupData = [
            'name' => 'Grupo Secreto Teste ' . now()->format('H:i:s'),
            'slug' => Str::slug('Grupo Secreto Teste ' . now()->format('H:i:s')),
            'description' => 'Este é um grupo secreto para testes',
            'privacy' => 'secret',
            'creator_id' => $user->id,
            'posts_require_approval' => false,
        ];

        $group = Group::create($groupData);

        if ($group) {
            $this->info("Grupo secreto criado: {$group->name} (ID: {$group->id})");
            $this->info("Privacy: {$group->privacy}");
            $this->info("Slug: {$group->slug}");

            // Verificar se o criador foi adicionado como membro
            $membership = $group->members()->where('user_id', $user->id)->first();
            if ($membership) {
                $this->info("✓ Criador foi adicionado como membro com role: {$membership->pivot->role}");
            } else {
                $this->error("✗ Criador NÃO foi adicionado como membro");
            }

            // Verificar métodos do modelo
            if ($group->isMember($user)) {
                $this->info("✓ Método isMember() funciona corretamente");
            } else {
                $this->error("✗ Método isMember() não funciona");
            }

            if ($group->isAdmin($user)) {
                $this->info("✓ Método isAdmin() funciona corretamente");
            } else {
                $this->error("✗ Método isAdmin() não funciona");
            }

            // Criar outro usuário para testar acesso
            $otherUser = User::create([
                'name' => 'Outro Usuário',
                'username' => 'outro_usuario_' . now()->timestamp,
                'email' => 'outro' . now()->timestamp . '@exemplo.com',
                'password' => bcrypt('password'),
                'role' => 'vip',
                'active' => true,
            ]);

            $this->info("Segundo usuário criado: {$otherUser->name} (ID: {$otherUser->id})");

            // Verificar se o outro usuário NÃO é membro
            if (!$group->isMember($otherUser)) {
                $this->info("✓ Outro usuário não é membro (correto)");
            } else {
                $this->error("✗ Outro usuário é membro (incorreto)");
            }

            // Testar listagem de grupos
            $this->info("\nTestando listagem de grupos...");

            // Grupos visíveis para o criador
            $visibleForCreator = Group::where(function ($q) use ($user) {
                $q->where('privacy', '!=', 'secret')
                    ->orWhereHas('members', function ($q2) use ($user) {
                        $q2->where('user_id', $user->id)
                            ->where('is_approved', true);
                    });
            })->count();

            $this->info("Grupos visíveis para o criador: {$visibleForCreator}");

            // Grupos visíveis para o outro usuário
            $visibleForOther = Group::where(function ($q) use ($otherUser) {
                $q->where('privacy', '!=', 'secret')
                    ->orWhereHas('members', function ($q2) use ($otherUser) {
                        $q2->where('user_id', $otherUser->id)
                            ->where('is_approved', true);
                    });
            })->count();

            $this->info("Grupos visíveis para o outro usuário: {$visibleForOther}");

            if ($visibleForCreator > $visibleForOther) {
                $this->info("✓ Criador vê mais grupos que o não-membro (correto)");
            } else {
                $this->error("✗ Problema na visibilidade dos grupos");
            }
        } else {
            $this->error("Falha ao criar o grupo secreto");
            return 1;
        }

        $this->info("\nTeste concluído com sucesso!");
        return 0;
    }
}
