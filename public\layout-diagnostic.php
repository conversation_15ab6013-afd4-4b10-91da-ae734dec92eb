<?php
/**
 * Script de diagnóstico do layout
 * Execute acessando: https://www.swingcuritiba.com.br/layout-diagnostic.php
 * 
 * IMPORTANTE: Delete este arquivo após usar!
 */

// Verificar se está no ambiente correto
if (!file_exists('../artisan')) {
    die('❌ Erro: Execute no diretório public do Laravel');
}

// Mudar para o diretório raiz do Laravel
chdir('..');

// Verificar se é o ambiente de produção
if (!str_contains($_SERVER['HTTP_HOST'] ?? '', 'swingcuritiba.com.br')) {
    die('❌ Este script só pode ser executado na produção');
}

echo "<h1>🔍 Diagnóstico do Layout</h1>\n";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
.ok { background: #d4edda; border-color: #c3e6cb; }
.warning { background: #fff3cd; border-color: #ffeaa7; }
.error { background: #f8d7da; border-color: #f5c6cb; }
.code { background: #f8f9fa; padding: 10px; border-radius: 3px; font-family: monospace; }
</style>\n";

// 1. Verificar arquivos de build
echo "<div class='section'>\n";
echo "<h2>📦 Assets Compilados</h2>\n";

$buildDir = 'public/build';
$manifestFile = $buildDir . '/manifest.json';

if (file_exists($manifestFile)) {
    echo "<p class='ok'>✅ Manifest.json existe</p>\n";
    
    $manifest = json_decode(file_get_contents($manifestFile), true);
    echo "<div class='code'>\n";
    echo "<strong>Manifest.json:</strong><br>\n";
    foreach ($manifest as $key => $value) {
        $file = $buildDir . '/' . $value['file'];
        $exists = file_exists($file) ? '✅' : '❌';
        $size = file_exists($file) ? formatBytes(filesize($file)) : 'N/A';
        echo "{$exists} {$key} → {$value['file']} ({$size})<br>\n";
    }
    echo "</div>\n";
} else {
    echo "<p class='error'>❌ Manifest.json não encontrado</p>\n";
}

// 2. Verificar configurações do Laravel
echo "</div><div class='section'>\n";
echo "<h2>⚙️ Configurações do Laravel</h2>\n";

$configs = [
    'APP_URL' => env('APP_URL'),
    'ASSET_URL' => env('ASSET_URL'),
    'APP_ENV' => env('APP_ENV'),
    'APP_DEBUG' => env('APP_DEBUG') ? 'true' : 'false',
];

foreach ($configs as $key => $value) {
    $class = $value ? 'ok' : 'warning';
    echo "<p class='{$class}'>{$key}: {$value}</p>\n";
}

// 3. Verificar URLs dos assets
echo "</div><div class='section'>\n";
echo "<h2>🔗 URLs dos Assets</h2>\n";

if (file_exists($manifestFile)) {
    $manifest = json_decode(file_get_contents($manifestFile), true);
    
    foreach ($manifest as $key => $value) {
        $assetUrl = asset('build/' . $value['file']);
        echo "<p><strong>{$key}:</strong><br>\n";
        echo "<a href='{$assetUrl}' target='_blank'>{$assetUrl}</a></p>\n";
    }
}

// 4. Verificar CSS específicos
echo "</div><div class='section'>\n";
echo "<h2>🎨 CSS Específicos</h2>\n";

$cssFiles = [
    'resources/css/mobile.css',
    'resources/css/app.css',
    'resources/css/text-colors.css',
];

foreach ($cssFiles as $file) {
    if (file_exists($file)) {
        $size = formatBytes(filesize($file));
        $modified = date('Y-m-d H:i:s', filemtime($file));
        echo "<p class='ok'>✅ {$file} ({$size}) - {$modified}</p>\n";
    } else {
        echo "<p class='error'>❌ {$file} não encontrado</p>\n";
    }
}

// 5. Verificar Livewire
echo "</div><div class='section'>\n";
echo "<h2>⚡ Livewire</h2>\n";

$livewireConfig = config('livewire');
echo "<div class='code'>\n";
echo "<strong>Configurações do Livewire:</strong><br>\n";
echo "View Path: " . ($livewireConfig['view_path'] ?? 'N/A') . "<br>\n";
echo "Asset URL: " . ($livewireConfig['asset_url'] ?? 'N/A') . "<br>\n";
echo "Update Mode: " . ($livewireConfig['navigate']['show_progress_bar'] ?? 'N/A') . "<br>\n";
echo "</div>\n";

// 6. Verificar Flux UI
echo "</div><div class='section'>\n";
echo "<h2>🌊 Flux UI</h2>\n";

$fluxCss = 'vendor/livewire/flux/dist/flux.css';
if (file_exists($fluxCss)) {
    $size = formatBytes(filesize($fluxCss));
    echo "<p class='ok'>✅ Flux CSS encontrado ({$size})</p>\n";
} else {
    echo "<p class='error'>❌ Flux CSS não encontrado</p>\n";
}

// 7. Teste de componentes
echo "</div><div class='section'>\n";
echo "<h2>🧪 Teste de Componentes</h2>\n";

echo "<div style='padding: 20px; background: #f8f9fa; border-radius: 5px;'>\n";
echo "<h3>Teste Visual dos Componentes:</h3>\n";

// Simular alguns componentes básicos
echo "<div style='display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px; margin: 10px 0;'>\n";
echo "<div style='padding: 10px; background: #007bff; color: white; border-radius: 5px; text-align: center;'>Botão 1</div>\n";
echo "<div style='padding: 10px; background: #28a745; color: white; border-radius: 5px; text-align: center;'>Botão 2</div>\n";
echo "</div>\n";

echo "<div style='padding: 15px; background: #343a40; color: white; border-radius: 5px; margin: 10px 0;'>\n";
echo "Card de exemplo com fundo escuro\n";
echo "</div>\n";

echo "</div>\n";

// 8. Recomendações
echo "</div><div class='section'>\n";
echo "<h2>💡 Recomendações</h2>\n";

echo "<ol>\n";
echo "<li><strong>Limpar cache:</strong> php artisan optimize:clear</li>\n";
echo "<li><strong>Recompilar assets:</strong> npm run build</li>\n";
echo "<li><strong>Verificar permissões:</strong> chmod 755 public/build/</li>\n";
echo "<li><strong>Testar URLs:</strong> Clique nos links dos assets acima</li>\n";
echo "<li><strong>Verificar console:</strong> F12 → Console para erros JS/CSS</li>\n";
echo "</ol>\n";

echo "</div>\n";

echo "<hr>\n";
echo "<p><small>Diagnóstico gerado em: " . date('Y-m-d H:i:s') . "</small></p>\n";
echo "<p><strong>⚠️ IMPORTANTE:</strong> Delete este arquivo após usar!</p>\n";

function formatBytes($size) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $unitIndex = 0;
    
    while ($size >= 1024 && $unitIndex < count($units) - 1) {
        $size /= 1024;
        $unitIndex++;
    }
    
    return round($size, 2) . ' ' . $units[$unitIndex];
}
?>
