<div class="container mx-auto p-6">
    <!-- Header -->
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-300 mb-2">Lives</h1>
            <p class="text-gray-400">Descubra transmissões ao vivo da comunidade</p>
        </div>
        
        <div class="mt-4 md:mt-0">
            <a href="{{ route('live-streams.broadcast') }}" class="inline-flex items-center">
                <flux:button variant="primary" size="base">
                    <x-flux::icon name="video-camera" class="w-5 h-5 mr-2" />
                    Iniciar Live
                </flux:button>
            </a>
        </div>
    </div>

    <!-- Filtros e Busca -->
    <div class="bg-zinc-800 rounded-lg p-6 mb-8">
        <div class="flex flex-col md:flex-row gap-4">
            <!-- Filtros -->
            <div class="flex gap-2 flex-wrap">
                <flux:button 
                    wire:click="setFilter('live')" 
                    variant="{{ $filter === 'live' ? 'primary' : 'ghost' }}"
                    size="sm"
                >
                    <div class="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
                    Ao Vivo
                </flux:button>
                <flux:button 
                    wire:click="setFilter('waiting')" 
                    variant="{{ $filter === 'waiting' ? 'primary' : 'ghost' }}"
                    size="sm"
                >
                    <div class="w-2 h-2 bg-yellow-500 rounded-full mr-2"></div>
                    Aguardando
                </flux:button>
                <flux:button 
                    wire:click="setFilter('ended')" 
                    variant="{{ $filter === 'ended' ? 'primary' : 'ghost' }}"
                    size="sm"
                >
                    <div class="w-2 h-2 bg-gray-500 rounded-full mr-2"></div>
                    Encerradas
                </flux:button>
                <flux:button 
                    wire:click="setFilter('all')" 
                    variant="{{ $filter === 'all' ? 'primary' : 'ghost' }}"
                    size="sm"
                >
                    Todas
                </flux:button>
            </div>

            <!-- Busca -->
            <div class="flex-1 md:max-w-md">
                <flux:input 
                    wire:model.live.debounce.300ms="search"
                    placeholder="Buscar lives..."
                    class="w-full"
                >
                    <x-slot name="iconTrailing">
                        <x-flux::icon name="magnifying-glass" class="w-5 h-5" />
                    </x-slot>
                </flux:input>
            </div>
        </div>
    </div>

    <!-- Grid de Lives -->
    @if($liveStreams->count() > 0)
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
            @foreach($liveStreams as $liveStream)
                <div class="bg-zinc-800 rounded-lg overflow-hidden hover:bg-zinc-700 transition-colors">
                    <!-- Thumbnail -->
                    <div class="relative aspect-video bg-black">
                        @if($liveStream->user->userPhotos->first())
                            <img src="{{ Storage::url($liveStream->user->userPhotos->first()->photo_path) }}" 
                                 class="w-full h-full object-cover opacity-50">
                        @else
                            <div class="w-full h-full bg-gradient-to-br from-purple-900 to-pink-900 flex items-center justify-center">
                                <x-flux::icon name="video-camera" class="w-12 h-12 text-white opacity-50" />
                            </div>
                        @endif
                        
                        <!-- Status Badge -->
                        <div class="absolute top-3 left-3">
                            @if($liveStream->isLive())
                                <div class="bg-red-500 text-white px-2 py-1 rounded text-xs font-bold flex items-center">
                                    <div class="w-2 h-2 bg-white rounded-full mr-1 animate-pulse"></div>
                                    AO VIVO
                                </div>
                            @elseif($liveStream->isWaiting())
                                <div class="bg-yellow-500 text-black px-2 py-1 rounded text-xs font-bold">
                                    AGUARDANDO
                                </div>
                            @else
                                <div class="bg-gray-500 text-white px-2 py-1 rounded text-xs font-bold">
                                    ENCERRADA
                                </div>
                            @endif
                        </div>

                        <!-- Viewers Count -->
                        @if($liveStream->isLive() && $liveStream->viewers_count > 0)
                            <div class="absolute top-3 right-3 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-xs flex items-center">
                                <x-flux::icon name="eye" class="w-3 h-3 mr-1" />
                                {{ $liveStream->viewers_count }}
                            </div>
                        @endif

                        <!-- Play Button Overlay -->
                        <div class="absolute inset-0 flex items-center justify-center">
                            <div class="bg-black bg-opacity-50 rounded-full p-4">
                                <x-flux::icon name="play" class="w-8 h-8 text-white" />
                            </div>
                        </div>
                    </div>

                    <!-- Content -->
                    <div class="p-4">
                        <!-- User Info -->
                        <div class="flex items-center gap-3 mb-3">
                            <img src="{{ $liveStream->user->userPhotos->first() ? Storage::url($liveStream->user->userPhotos->first()->photo_path) : asset('images/default-avatar.jpg') }}"
                                 class="w-8 h-8 rounded-full">
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-300 truncate">{{ $liveStream->user->name }}</p>
                                <p class="text-xs text-gray-500">{{ $liveStream->created_at->diffForHumans() }}</p>
                            </div>
                        </div>

                        <!-- Title -->
                        <h3 class="font-bold text-gray-300 mb-2 line-clamp-2">{{ $liveStream->title }}</h3>

                        <!-- Description -->
                        @if($liveStream->description)
                            <p class="text-sm text-gray-400 mb-3 line-clamp-2">{{ $liveStream->description }}</p>
                        @endif

                        <!-- Stats -->
                        <div class="flex items-center justify-between text-xs text-gray-500">
                            <div class="flex items-center gap-4">
                                @if($liveStream->total_donations > 0)
                                    <div class="flex items-center gap-1">
                                        <x-flux::icon name="currency-dollar" class="w-3 h-3" />
                                        R$ {{ number_format($liveStream->total_donations, 0) }}
                                    </div>
                                @endif
                            </div>
                            
                            <!-- Action Button -->
                            <a href="{{ route('live-streams.view', $liveStream) }}" class="text-purple-400 hover:text-purple-300">
                                @if($liveStream->isLive())
                                    Assistir
                                @elseif($liveStream->isWaiting())
                                    Aguardar
                                @else
                                    Ver Replay
                                @endif
                            </a>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Pagination -->
        <div class="flex justify-center">
            {{ $liveStreams->links() }}
        </div>
    @else
        <!-- Empty State -->
        <div class="text-center py-12">
            <x-flux::icon name="video-camera-slash" class="w-16 h-16 mx-auto mb-4 text-gray-500" />
            <h3 class="text-xl font-bold text-gray-300 mb-2">
                @if($filter === 'live')
                    Nenhuma live ao vivo no momento
                @elseif($filter === 'waiting')
                    Nenhuma live aguardando início
                @elseif($filter === 'ended')
                    Nenhuma live encerrada encontrada
                @else
                    Nenhuma live encontrada
                @endif
            </h3>
            <p class="text-gray-400 mb-6">
                @if($search)
                    Tente buscar por outros termos ou
                @endif
                Seja o primeiro a criar uma live!
            </p>
            <a href="{{ route('live-streams.broadcast') }}">
                <flux:button variant="primary">
                    <x-flux::icon name="video-camera" class="w-5 h-5 mr-2" />
                    Iniciar Live
                </flux:button>
            </a>
        </div>
    @endif
</div>
