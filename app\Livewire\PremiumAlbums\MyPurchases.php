<?php

namespace App\Livewire\PremiumAlbums;

use App\Models\AlbumPurchase;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Auth;

class MyPurchases extends Component
{
    use WithPagination;

    public $search = '';
    public $sortBy = 'purchased_at';
    public $sortDirection = 'desc';
    public $filterBy = 'all'; // all, recent, favorites

    protected $queryString = [
        'search' => ['except' => ''],
        'sortBy' => ['except' => 'purchased_at'],
        'sortDirection' => ['except' => 'desc'],
        'filterBy' => ['except' => 'all'],
    ];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingFilterBy()
    {
        $this->resetPage();
    }

    public function sortBy($field)
    {
        if ($this->sortBy === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $field;
            $this->sortDirection = 'asc';
        }
        $this->resetPage();
    }

    public function getPurchasesProperty()
    {
        $query = Auth::user()->albumPurchases()
                           ->with(['album.user', 'album.medias'])
                           ->where('status', 'completed');

        // Filtro de busca
        if ($this->search) {
            $query->whereHas('album', function($q) {
                $q->where('name', 'like', '%' . $this->search . '%')
                  ->orWhere('description', 'like', '%' . $this->search . '%')
                  ->orWhere('tags', 'like', '%' . $this->search . '%');
            });
        }

        // Filtros especiais
        switch ($this->filterBy) {
            case 'recent':
                $query->where('purchased_at', '>=', now()->subDays(30));
                break;
            case 'favorites':
                // Implementar sistema de favoritos se necessário
                break;
        }

        // Ordenação
        switch ($this->sortBy) {
            case 'album_name':
                $query->join('albums', 'album_purchases.album_id', '=', 'albums.id')
                      ->orderBy('albums.name', $this->sortDirection)
                      ->select('album_purchases.*');
                break;
            case 'amount':
                $query->orderBy('amount', $this->sortDirection);
                break;
            case 'creator':
                $query->join('albums', 'album_purchases.album_id', '=', 'albums.id')
                      ->join('users', 'albums.user_id', '=', 'users.id')
                      ->orderBy('users.name', $this->sortDirection)
                      ->select('album_purchases.*');
                break;
            default:
                $query->orderBy('purchased_at', $this->sortDirection);
        }

        return $query->paginate(12);
    }

    public function getStatsProperty()
    {
        $user = Auth::user();
        
        return [
            'total_purchases' => $user->albumPurchases()->completed()->count(),
            'total_spent' => $user->albumPurchases()->completed()->sum('amount'),
            'recent_purchases' => $user->albumPurchases()
                                     ->completed()
                                     ->where('purchased_at', '>=', now()->subDays(30))
                                     ->count(),
            'favorite_creator' => $user->albumPurchases()
                                     ->completed()
                                     ->join('albums', 'album_purchases.album_id', '=', 'albums.id')
                                     ->join('users', 'albums.user_id', '=', 'users.id')
                                     ->select('users.name', \DB::raw('COUNT(*) as purchase_count'))
                                     ->groupBy('users.id', 'users.name')
                                     ->orderByDesc('purchase_count')
                                     ->first()?->name ?? 'Nenhum',
        ];
    }

    public function render()
    {
        return view('livewire.premium-albums.my-purchases', [
            'purchases' => $this->purchases,
            'stats' => $this->stats,
        ]);
    }
}
