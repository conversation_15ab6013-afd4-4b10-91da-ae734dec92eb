<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ingresso Confirmado - {{ $event->name }}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #E60073;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #E60073;
            margin-bottom: 10px;
        }
        .title {
            color: #333;
            font-size: 28px;
            margin: 0;
        }
        .subtitle {
            color: #666;
            font-size: 16px;
            margin: 10px 0 0 0;
        }
        .event-info {
            background: linear-gradient(135deg, #E60073, #00FFF7);
            color: white;
            padding: 25px;
            border-radius: 10px;
            margin: 25px 0;
            text-align: center;
        }
        .event-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        .event-details {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            gap: 15px;
        }
        .detail-item {
            text-align: center;
            min-width: 120px;
        }
        .detail-label {
            font-size: 12px;
            opacity: 0.9;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .detail-value {
            font-size: 16px;
            font-weight: bold;
            margin-top: 5px;
        }
        .ticket-info {
            background: #f8f9fa;
            border: 2px dashed #E60073;
            border-radius: 10px;
            padding: 20px;
            margin: 25px 0;
            text-align: center;
        }
        .ticket-code {
            font-family: 'Courier New', monospace;
            font-size: 24px;
            font-weight: bold;
            color: #E60073;
            background: white;
            padding: 10px 20px;
            border-radius: 5px;
            display: inline-block;
            margin: 10px 0;
            letter-spacing: 2px;
        }
        .instructions {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 25px 0;
        }
        .instructions h3 {
            color: #1976d2;
            margin-top: 0;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 14px;
        }
        .button {
            display: inline-block;
            background: linear-gradient(135deg, #E60073, #00FFF7);
            color: white;
            padding: 12px 25px;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            margin: 15px 0;
            transition: transform 0.2s;
        }
        .button:hover {
            transform: translateY(-2px);
        }
        @media (max-width: 600px) {
            .event-details {
                flex-direction: column;
                align-items: center;
            }
            .detail-item {
                margin-bottom: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🎉 Swing Curitiba</div>
            <h1 class="title">Ingresso Confirmado!</h1>
            <p class="subtitle">Seu pagamento foi processado com sucesso</p>
        </div>

        <p>Olá <strong>{{ $user->name }}</strong>,</p>

        <p>Parabéns! Seu ingresso para o evento foi confirmado. Estamos ansiosos para vê-lo(a) lá!</p>

        <div class="event-info">
            <div class="event-name">{{ $event->name }}</div>
            <div class="event-details">
                <div class="detail-item">
                    <div class="detail-label">📅 Data</div>
                    <div class="detail-value">{{ $event->formatted_date }}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">🕐 Horário</div>
                    <div class="detail-value">{{ $event->formatted_start_time }}</div>
                </div>
                @if($event->location)
                <div class="detail-item">
                    <div class="detail-label">📍 Local</div>
                    <div class="detail-value">{{ $event->location }}</div>
                </div>
                @endif
                <div class="detail-item">
                    <div class="detail-label">💰 Valor Pago</div>
                    <div class="detail-value">R$ {{ number_format($attendee->amount_paid, 2, ',', '.') }}</div>
                </div>
            </div>
        </div>

        <div class="ticket-info">
            <h3>🎫 Seu Código do Ingresso</h3>
            <div class="ticket-code">{{ $attendee->ticket_code }}</div>
            <p><strong>Importante:</strong> Guarde este código! Você precisará dele para fazer check-in no evento.</p>
        </div>

        <div class="instructions">
            <h3>📋 Instruções Importantes</h3>
            <ul>
                <li><strong>Chegue com antecedência:</strong> Recomendamos chegar 15-30 minutos antes do horário de início</li>
                <li><strong>Traga seu código:</strong> Apresente o código do ingresso na entrada para fazer check-in</li>
                <li><strong>Documento:</strong> Tenha um documento com foto em mãos</li>
                @if($event->description)
                <li><strong>Informações adicionais:</strong> {{ strip_tags($event->description) }}</li>
                @endif
            </ul>
        </div>

        <div style="text-align: center;">
            <a href="{{ url('/eventos/' . $event->slug) }}" class="button">
                Ver Detalhes do Evento
            </a>
        </div>

        <p>Se você tiver alguma dúvida ou precisar de ajuda, não hesite em entrar em contato conosco.</p>

        <p>Nos vemos no evento! 🎉</p>

        <div class="footer">
            <p><strong>Swing Curitiba</strong><br>
            A melhor plataforma de eventos de swing da cidade<br>
            <a href="{{ url('/') }}">www.swingcuritiba.com.br</a></p>
            
            <p style="font-size: 12px; color: #999; margin-top: 15px;">
                Este é um email automático. Por favor, não responda a esta mensagem.<br>
                Para suporte, entre em contato através do nosso site.
            </p>
        </div>
    </div>
</body>
</html>
