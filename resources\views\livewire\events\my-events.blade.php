<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white dark:bg-zinc-800 shadow-md rounded-lg p-6">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                    Meus Eventos
                </h1>
                <p class="text-gray-600 dark:text-gray-300 mt-1">
                    G<PERSON><PERSON><PERSON> suas inscrições e acompanhe seus eventos
                </p>
            </div>
            <div class="flex gap-3">
                <flux:button href="{{ route('events.index') }}" variant="outline">
                    <flux:icon name="plus" class="h-4 w-4 mr-2" />
                    Buscar Eventos
                </flux:button>
            </div>
        </div>
    </div>

    <!-- Estatísticas -->
    <div class="grid grid-cols-2 lg:grid-cols-4 gap-4">
        <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md p-4">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                    <flux:icon name="calendar-days" class="h-6 w-6 text-blue-600 dark:text-blue-400" />
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total</p>
                    <p class="text-lg font-semibold text-gray-900 dark:text-white">{{ $stats['total'] }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md p-4">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                    <flux:icon name="clock" class="h-6 w-6 text-green-600 dark:text-green-400" />
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Próximos</p>
                    <p class="text-lg font-semibold text-gray-900 dark:text-white">{{ $stats['upcoming'] }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md p-4">
            <div class="flex items-center">
                <div class="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                    <flux:icon name="check-circle" class="h-6 w-6 text-purple-600 dark:text-purple-400" />
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Confirmados</p>
                    <p class="text-lg font-semibold text-gray-900 dark:text-white">{{ $stats['confirmed'] }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md p-4">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                    <flux:icon name="star" class="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Participei</p>
                    <p class="text-lg font-semibold text-gray-900 dark:text-white">{{ $stats['attended'] }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtros e Busca -->
    <div class="bg-white dark:bg-zinc-800 shadow-md rounded-lg p-6">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <!-- Filtros -->
            <div class="flex flex-wrap gap-2">
                <flux:button 
                    wire:click="setFilter('all')" 
                    variant="{{ $filter === 'all' ? 'primary' : 'outline' }}"
                    size="sm"
                >
                    Todos
                </flux:button>
                <flux:button 
                    wire:click="setFilter('upcoming')" 
                    variant="{{ $filter === 'upcoming' ? 'primary' : 'outline' }}"
                    size="sm"
                >
                    Próximos
                </flux:button>
                <flux:button 
                    wire:click="setFilter('confirmed')" 
                    variant="{{ $filter === 'confirmed' ? 'primary' : 'outline' }}"
                    size="sm"
                >
                    Confirmados
                </flux:button>
                <flux:button 
                    wire:click="setFilter('pending')" 
                    variant="{{ $filter === 'pending' ? 'primary' : 'outline' }}"
                    size="sm"
                >
                    Pendentes
                </flux:button>
                <flux:button 
                    wire:click="setFilter('past')" 
                    variant="{{ $filter === 'past' ? 'primary' : 'outline' }}"
                    size="sm"
                >
                    Passados
                </flux:button>
            </div>

            <!-- Busca -->
            <div class="w-full lg:w-80">
                <flux:input 
                    wire:model.live.debounce.300ms="search" 
                    placeholder="Buscar eventos..." 
                    class="w-full"
                >
                    <x-slot name="iconTrailing">
                        <flux:icon name="magnifying-glass" class="h-4 w-4" />
                    </x-slot>
                </flux:input>
            </div>
        </div>
    </div>

    <!-- Lista de Eventos -->
    <div class="space-y-4">
        @forelse($attendees as $attendee)
            @php
                $event = $attendee->event;
                $isPast = $event->date < now();
                $isUpcoming = $event->date >= now();
            @endphp
            
            <div class="bg-white dark:bg-zinc-800 shadow-md rounded-lg overflow-hidden">
                <div class="flex flex-col lg:flex-row">
                    <!-- Imagem do evento -->
                    <div class="lg:w-48 h-48 lg:h-auto">
                        <img 
                            src="{{ $event->image_url }}" 
                            alt="{{ $event->name }}" 
                            class="w-full h-full object-cover"
                        >
                    </div>

                    <!-- Conteúdo -->
                    <div class="flex-1 p-6">
                        <div class="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4">
                            <div class="flex-1">
                                <!-- Título e badges -->
                                <div class="flex flex-wrap items-center gap-2 mb-2">
                                    <h3 class="text-xl font-bold text-gray-900 dark:text-white">
                                        {{ $event->name }}
                                    </h3>
                                    
                                    <!-- Status badges -->
                                    @if($attendee->status === 'confirmed' && $attendee->payment_status === 'completed')
                                        <span class="px-2 py-1 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 text-xs font-medium rounded-full">
                                            Confirmado
                                        </span>
                                    @elseif($attendee->status === 'attended')
                                        <span class="px-2 py-1 bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200 text-xs font-medium rounded-full">
                                            Participei
                                        </span>
                                    @elseif($attendee->payment_status === 'pending')
                                        <span class="px-2 py-1 bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 text-xs font-medium rounded-full">
                                            Pagamento Pendente
                                        </span>
                                    @elseif($attendee->status === 'cancelled')
                                        <span class="px-2 py-1 bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 text-xs font-medium rounded-full">
                                            Cancelado
                                        </span>
                                    @else
                                        <span class="px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 text-xs font-medium rounded-full">
                                            Inscrito
                                        </span>
                                    @endif

                                    @if($isPast)
                                        <span class="px-2 py-1 bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200 text-xs font-medium rounded-full">
                                            Evento Passado
                                        </span>
                                    @endif
                                </div>

                                <!-- Informações do evento -->
                                <div class="space-y-2 text-sm text-gray-600 dark:text-gray-300">
                                    <div class="flex items-center">
                                        <flux:icon name="calendar-days" class="h-4 w-4 mr-2" />
                                        {{ $event->formatted_date }} às {{ $event->formatted_start_time }}
                                    </div>
                                    @if($event->location)
                                        <div class="flex items-center">
                                            <flux:icon name="map-pin" class="h-4 w-4 mr-2" />
                                            {{ $event->location }}
                                        </div>
                                    @endif
                                    <div class="flex items-center">
                                        <flux:icon name="currency-dollar" class="h-4 w-4 mr-2" />
                                        {{ $event->formatted_price }}
                                    </div>
                                    @if($attendee->ticket_code)
                                        <div class="flex items-center">
                                            <flux:icon name="ticket" class="h-4 w-4 mr-2" />
                                            Código: <span class="font-mono ml-1">{{ $attendee->ticket_code }}</span>
                                        </div>
                                    @endif
                                </div>

                                <!-- Data de inscrição -->
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-3">
                                    Inscrito em {{ $attendee->created_at->format('d/m/Y H:i') }}
                                </p>
                            </div>

                            <!-- Ações -->
                            <div class="flex flex-col gap-2 lg:w-40">
                                <flux:button href="{{ route('events.show', $event->slug) }}" variant="outline" size="sm">
                                    <flux:icon name="eye" class="h-4 w-4 mr-2" />
                                    Ver Evento
                                </flux:button>

                                @if($attendee->status !== 'cancelled' && $attendee->status !== 'attended' && $isUpcoming)
                                    <flux:button 
                                        wire:click="cancelRegistration({{ $attendee->id }})" 
                                        variant="danger" 
                                        size="sm"
                                        wire:confirm="Tem certeza que deseja cancelar sua inscrição?"
                                    >
                                        <flux:icon name="x-mark" class="h-4 w-4 mr-2" />
                                        Cancelar
                                    </flux:button>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @empty
            <div class="bg-white dark:bg-zinc-800 shadow-md rounded-lg p-8 text-center">
                <flux:icon name="calendar-days" class="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    @if($search)
                        Nenhum evento encontrado
                    @else
                        Você ainda não se inscreveu em nenhum evento
                    @endif
                </h3>
                <p class="text-gray-500 dark:text-gray-400 mb-4">
                    @if($search)
                        Tente ajustar sua busca ou filtros.
                    @else
                        Explore nossos eventos e faça sua primeira inscrição!
                    @endif
                </p>
                <flux:button href="{{ route('events.index') }}">
                    <flux:icon name="plus" class="h-4 w-4 mr-2" />
                    Buscar Eventos
                </flux:button>
            </div>
        @endforelse
    </div>

    <!-- Paginação -->
    @if($attendees->hasPages())
        <div class="bg-white dark:bg-zinc-800 shadow-md rounded-lg p-4">
            {{ $attendees->links() }}
        </div>
    @endif

    <!-- Flash Messages -->
    @if (session()->has('success'))
        <div class="fixed bottom-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50">
            {{ session('success') }}
        </div>
    @endif

    @if (session()->has('error'))
        <div class="fixed bottom-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50">
            {{ session('error') }}
        </div>
    @endif
</div>
