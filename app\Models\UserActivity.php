<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Request;

class UserActivity extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'session_id',
        'ip_address',
        'user_agent',
        'activity_type',
        'url',
        'route_name',
        'method',
        'parameters',
        'element_type',
        'element_id',
        'element_class',
        'element_text',
        'page_title',
        'time_on_page',
        'scroll_depth',
        'referrer',
        'viewport_size',
        'device_type',
        'additional_data',
    ];

    protected $casts = [
        'parameters' => 'array',
        'viewport_size' => 'array',
        'additional_data' => 'array',
    ];

    /**
     * Relacionamento com o usuário
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Registra uma atividade de página visitada
     */
    public static function logPageView(string $url, ?string $routeName = null, array $additionalData = []): void
    {
        self::create([
            'user_id' => Auth::id(),
            'session_id' => session()->getId(),
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
            'activity_type' => 'page_view',
            'url' => $url,
            'route_name' => $routeName,
            'method' => Request::method(),
            'parameters' => Request::all(),
            'page_title' => $additionalData['page_title'] ?? null,
            'referrer' => Request::header('referer'),
            'device_type' => self::detectDeviceType(Request::userAgent()),
            'additional_data' => $additionalData,
        ]);
    }

    /**
     * Registra um clique em elemento
     */
    public static function logClick(array $data): void
    {
        self::create([
            'user_id' => Auth::id(),
            'session_id' => session()->getId(),
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
            'activity_type' => 'click',
            'url' => $data['url'] ?? Request::url(),
            'route_name' => $data['route_name'] ?? null,
            'element_type' => $data['element_type'] ?? null,
            'element_id' => $data['element_id'] ?? null,
            'element_class' => $data['element_class'] ?? null,
            'element_text' => $data['element_text'] ?? null,
            'page_title' => $data['page_title'] ?? null,
            'viewport_size' => $data['viewport_size'] ?? null,
            'additional_data' => $data['additional_data'] ?? [],
        ]);
    }

    /**
     * Registra evento customizado
     */
    public static function logEvent(string $eventType, array $data = []): void
    {
        self::create([
            'user_id' => Auth::id(),
            'session_id' => session()->getId(),
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
            'activity_type' => $eventType,
            'url' => Request::url(),
            'route_name' => Request::route()?->getName(),
            'method' => Request::method(),
            'additional_data' => $data,
        ]);
    }

    /**
     * Detecta o tipo de dispositivo baseado no User Agent
     */
    private static function detectDeviceType(?string $userAgent): string
    {
        if (!$userAgent) return 'unknown';

        $userAgent = strtolower($userAgent);

        if (strpos($userAgent, 'mobile') !== false || 
            strpos($userAgent, 'android') !== false || 
            strpos($userAgent, 'iphone') !== false) {
            return 'mobile';
        }

        if (strpos($userAgent, 'tablet') !== false || 
            strpos($userAgent, 'ipad') !== false) {
            return 'tablet';
        }

        return 'desktop';
    }

    /**
     * Scope para filtrar por tipo de atividade
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('activity_type', $type);
    }

    /**
     * Scope para filtrar por usuário
     */
    public function scopeForUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope para filtrar por período
     */
    public function scopeBetweenDates($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Scope para atividades de hoje
     */
    public function scopeToday($query)
    {
        return $query->whereDate('created_at', today());
    }

    /**
     * Scope para atividades desta semana
     */
    public function scopeThisWeek($query)
    {
        return $query->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()]);
    }

    /**
     * Scope para atividades deste mês
     */
    public function scopeThisMonth($query)
    {
        return $query->whereMonth('created_at', now()->month)
                    ->whereYear('created_at', now()->year);
    }
}
