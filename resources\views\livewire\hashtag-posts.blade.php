<div>
    @if($posts->count() > 0)
        <div class="space-y-6">
            @foreach($posts as $post)
                <div class="bg-zinc-800 border border-zinc-700 rounded-lg p-6">
                    <!-- Header do Post -->
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center space-x-3">
                            <img src="{{ !empty($post->user->userPhotos->first()) ? asset('storage/' . $post->user->userPhotos->first()->photo_path) : asset('images/default-avatar.jpg') }}"
                                 class="w-10 h-10 rounded-full object-cover">
                            <div>
                                <h4 class="font-semibold text-gray-300">{{ $post->user->name }}</h4>
                                <p class="text-sm text-gray-500">
                                    <a href="/{{ $post->user->username }}" class="hover:underline">@{{ $post->user->username }}</a>
                                </p>
                            </div>
                        </div>
                        <span class="text-sm text-gray-500">{{ $post->created_at->diffForHumans() }}</span>
                    </div>

                    <!-- Conteúdo do Post -->
                    @if($post->image)
                        <img src="{{ asset('storage/' . $post->image) }}" class="w-full rounded-lg mb-4">
                    @endif

                    @if($post->video)
                        <video controls class="w-full rounded-lg mb-4" preload="metadata">
                            <source src="{{ asset('storage/' . $post->video) }}" type="video/mp4">
                            Seu navegador não suporta o elemento de vídeo.
                        </video>
                    @endif

                    <div class="text-gray-300 mb-4">
                        {!! \App\Services\ContentProcessor::processContent($post->content) !!}
                    </div>

                    <!-- Hashtags do Post -->
                    @if($post->hashtags->count() > 0)
                        <div class="flex flex-wrap gap-2 mb-4">
                            @foreach($post->hashtags as $tag)
                                <a href="{{ route('hashtag.show', $tag->slug) }}" 
                                   class="text-cyan-400 hover:text-cyan-300 text-sm">
                                    #{{ $tag->name }}
                                </a>
                            @endforeach
                        </div>
                    @endif

                    <!-- Ações do Post -->
                    <div class="flex items-center space-x-4 text-gray-400">
                        <span class="flex items-center">
                            <x-flux::icon name="heart" class="w-5 h-5 mr-1" />
                            {{ $post->likedByUsers->count() }}
                        </span>
                        <span class="flex items-center">
                            <x-flux::icon name="chat-bubble-left" class="w-5 h-5 mr-1" />
                            {{ $post->comments->count() }}
                        </span>
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Botão Carregar Mais -->
        @if($posts->count() >= $limit)
            <div class="mt-8 text-center">
                <flux:button wire:click="loadMore" variant="ghost">
                    Carregar mais posts
                </flux:button>
            </div>
        @endif
    @else
        <div class="bg-zinc-800 border border-zinc-700 rounded-lg p-8 text-center">
            <x-flux::icon name="hashtag" class="w-16 h-16 text-gray-600 mx-auto mb-4" />
            <h3 class="text-xl font-semibold text-gray-300 mb-2">Nenhum post encontrado</h3>
            <p class="text-gray-500">Seja o primeiro a postar com #{{ $hashtag->name }}!</p>
        </div>
    @endif
</div>
