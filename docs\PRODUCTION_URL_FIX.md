# Correção de URLs Erradas em Produção

## Problema
URLs incorretas em produção no domínio `www.swingcuritiba.com.br`, causando problemas com:
- Assets (CSS, JS) não carregando
- Imagens não aparecendo
- Links quebrados
- Redirecionamentos incorretos

## Causas Comuns
1. **APP_URL incorreta** no arquivo `.env`
2. **ASSET_URL não configurada** ou incorreta
3. **Configuração do Vite** não otimizada para produção
4. **URL do disco público** incorreta no filesystem
5. **Cache de configuração** desatualizado

## Solução Rápida

### 1. Via Script Automático
```bash
# No servidor de produção
chmod +x deploy-production.sh
./deploy-production.sh
```

### 2. Via Comando Artisan
```bash
# Verificar problemas
php artisan production:fix-urls --check

# Aplicar correções
php artisan production:fix-urls
```

## Solução Manual

### 1. Corrigir arquivo .env
Certifique-se que o arquivo `.env` na produção contém:

```env
APP_URL=https://www.swingcuritiba.com.br
ASSET_URL=https://www.swingcuritiba.com.br
APP_ENV=production
APP_DEBUG=false
FILESYSTEM_DISK=public
SESSION_DOMAIN=.swingcuritiba.com.br
```

### 2. Limpar e recriar cache
```bash
php artisan config:clear
php artisan cache:clear
php artisan view:clear
php artisan route:clear

# Recriar cache otimizado
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

### 3. Verificar link simbólico do storage
```bash
# Verificar se existe
ls -la public/storage

# Criar se não existir
php artisan storage:link

# Verificar permissões
chmod 755 public/storage
chmod -R 755 storage/app/public
```

### 4. Build dos assets
```bash
npm install --production
npm run build
```

## Verificação

### 1. Testar URLs
```bash
# Verificar configurações
php artisan tinker
>>> config('app.url')
>>> config('app.asset_url')
>>> config('filesystems.disks.public.url')
```

### 2. Testar no navegador
- Acesse: `https://www.swingcuritiba.com.br`
- Verifique se CSS/JS carregam
- Teste upload e exibição de imagens
- Verifique console do navegador para erros

### 3. Testar URLs de storage
```bash
# Exemplo de URL que deve funcionar
https://www.swingcuritiba.com.br/storage/posts/images/exemplo.jpg
```

## Problemas Específicos

### Assets não carregam (CSS/JS)
**Causa**: Configuração incorreta do Vite ou ASSET_URL

**Solução**:
1. Verificar `vite.config.js` tem base URL correta
2. Executar `npm run build`
3. Verificar se `public/build/manifest.json` existe

### Imagens não aparecem
**Causa**: Link simbólico do storage ou URLs incorretas

**Solução**:
```bash
php artisan storage:link
php artisan storage:fix-production
```

### Redirecionamentos incorretos
**Causa**: APP_URL incorreta

**Solução**:
1. Corrigir APP_URL no .env
2. Executar `php artisan config:clear`

## Monitoramento

### Logs importantes
```bash
# Logs gerais
tail -f storage/logs/laravel.log

# Logs específicos de storage
tail -f storage/logs/laravel.log | grep -i storage

# Logs de erro
tail -f storage/logs/laravel.log | grep -i error
```

### Comandos de diagnóstico
```bash
# Verificar saúde do storage
php artisan storage:diagnose

# Verificar URLs
php artisan production:fix-urls --check

# Verificar configurações
php artisan config:show app.url
php artisan config:show filesystems.disks.public.url
```

## Prevenção

### 1. Usar arquivo .env.production
Mantenha um arquivo `.env.production` no repositório com as configurações corretas.

### 2. Script de deploy
Use o script `deploy-production.sh` para deploys automatizados.

### 3. Verificação automática
Adicione verificações de URL nos testes automatizados.

## Troubleshooting

### Se o problema persistir:

1. **Verificar servidor web**:
   - Apache: verificar `.htaccess`
   - Nginx: verificar configuração de location

2. **Verificar DNS**:
   ```bash
   nslookup www.swingcuritiba.com.br
   ```

3. **Verificar SSL**:
   ```bash
   curl -I https://www.swingcuritiba.com.br
   ```

4. **Verificar permissões**:
   ```bash
   ls -la public/
   ls -la storage/app/public/
   ```

## Contato para Suporte

Se o problema persistir após seguir este guia:

1. Execute o diagnóstico completo:
   ```bash
   php artisan production:fix-urls --check > url-diagnosis.txt
   php artisan storage:diagnose >> url-diagnosis.txt
   ```

2. Envie o arquivo `url-diagnosis.txt` junto com:
   - Descrição do problema
   - URLs que não funcionam
   - Screenshots dos erros
   - Logs relevantes
