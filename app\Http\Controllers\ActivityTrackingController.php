<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\UserActivity;
use Illuminate\Http\JsonResponse;

class ActivityTrackingController extends Controller
{
    /**
     * Registra um clique de usuário
     */
    public function trackClick(Request $request): JsonResponse
    {
        try {
            $data = $request->validate([
                'element_type' => 'nullable|string|max:50',
                'element_id' => 'nullable|string|max:100',
                'element_class' => 'nullable|string|max:200',
                'element_text' => 'nullable|string|max:500',
                'page_title' => 'nullable|string|max:200',
                'url' => 'nullable|string|max:500',
                'viewport_size' => 'nullable|array',
                'viewport_size.width' => 'nullable|integer',
                'viewport_size.height' => 'nullable|integer',
                'additional_data' => 'nullable|array',
            ]);

            UserActivity::logClick($data);

            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            \Log::error('Erro ao registrar clique: ' . $e->getMessage());
            return response()->json(['success' => false], 500);
        }
    }

    /**
     * Registra tempo na página e profundidade de scroll
     */
    public function trackPageMetrics(Request $request): JsonResponse
    {
        try {
            $data = $request->validate([
                'time_on_page' => 'required|integer|min:0',
                'scroll_depth' => 'nullable|integer|min:0|max:100',
                'url' => 'required|string|max:500',
                'page_title' => 'nullable|string|max:200',
            ]);

            // Atualiza a última atividade de page_view para esta URL
            $lastActivity = UserActivity::where('user_id', auth()->id())
                ->where('activity_type', 'page_view')
                ->where('url', $data['url'])
                ->orderBy('created_at', 'desc')
                ->first();

            if ($lastActivity) {
                $lastActivity->update([
                    'time_on_page' => $data['time_on_page'],
                    'scroll_depth' => $data['scroll_depth'] ?? null,
                ]);
            }

            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            \Log::error('Erro ao registrar métricas da página: ' . $e->getMessage());
            return response()->json(['success' => false], 500);
        }
    }

    /**
     * Registra evento customizado
     */
    public function trackEvent(Request $request): JsonResponse
    {
        try {
            $data = $request->validate([
                'event_type' => 'required|string|max:50',
                'event_data' => 'nullable|array',
            ]);

            UserActivity::logEvent($data['event_type'], $data['event_data'] ?? []);

            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            \Log::error('Erro ao registrar evento: ' . $e->getMessage());
            return response()->json(['success' => false], 500);
        }
    }
}
