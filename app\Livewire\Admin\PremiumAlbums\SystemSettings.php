<?php

namespace App\Livewire\Admin\PremiumAlbums;

use App\Models\PremiumAlbumSetting;
use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;

class SystemSettings extends Component
{
    // Configurações de comissão e preços
    public $commission_rate = 15;
    public $min_price = 5;
    public $max_price = 500;
    
    // Configurações de arquivos
    public $max_files_per_album = 50;
    public $max_file_size = 50; // MB
    public $allowed_file_types = 'jpg,jpeg,png,gif,webp,mp4,mov,avi,webm';
    
    // Configurações de moderação
    public $auto_approve = false;
    public $require_verification = true;
    
    // Configurações de notificações
    public $notify_new_albums = true;
    public $notify_sales = true;
    public $notify_reviews = true;
    
    public $isLoading = false;

    protected $rules = [
        'commission_rate' => 'required|numeric|min:0|max:50',
        'min_price' => 'required|numeric|min:1|max:100',
        'max_price' => 'required|numeric|min:10|max:1000',
        'max_files_per_album' => 'required|integer|min:1|max:100',
        'max_file_size' => 'required|integer|min:1|max:100',
        'allowed_file_types' => 'required|string',
        'auto_approve' => 'boolean',
        'require_verification' => 'boolean',
        'notify_new_albums' => 'boolean',
        'notify_sales' => 'boolean',
        'notify_reviews' => 'boolean',
    ];

    protected $messages = [
        'commission_rate.required' => 'Taxa de comissão é obrigatória.',
        'commission_rate.min' => 'Taxa de comissão deve ser no mínimo 0%.',
        'commission_rate.max' => 'Taxa de comissão deve ser no máximo 50%.',
        'min_price.required' => 'Preço mínimo é obrigatório.',
        'max_price.required' => 'Preço máximo é obrigatório.',
        'max_files_per_album.required' => 'Limite de arquivos é obrigatório.',
        'max_file_size.required' => 'Tamanho máximo de arquivo é obrigatório.',
        'allowed_file_types.required' => 'Tipos de arquivo permitidos são obrigatórios.',
    ];

    public function mount()
    {
        // Verificar se é admin
        if (Auth::user()->role !== 'administrador') {
            abort(403, 'Acesso negado.');
        }

        $this->loadSettings();
    }

    public function loadSettings()
    {
        $this->commission_rate = PremiumAlbumSetting::getValue('commission_rate', 15);
        $this->min_price = PremiumAlbumSetting::getValue('min_price', 5);
        $this->max_price = PremiumAlbumSetting::getValue('max_price', 500);
        $this->max_files_per_album = PremiumAlbumSetting::getValue('max_files_per_album', 50);
        $this->max_file_size = PremiumAlbumSetting::getValue('max_file_size', 50);
        $this->allowed_file_types = implode(',', PremiumAlbumSetting::getValue('allowed_file_types', ['jpg','jpeg','png','gif','webp','mp4','mov','avi','webm']));
        $this->auto_approve = PremiumAlbumSetting::getValue('auto_approve', false);
        $this->require_verification = PremiumAlbumSetting::getValue('require_verification', true);
        $this->notify_new_albums = PremiumAlbumSetting::getValue('notify_new_albums', true);
        $this->notify_sales = PremiumAlbumSetting::getValue('notify_sales', true);
        $this->notify_reviews = PremiumAlbumSetting::getValue('notify_reviews', true);
    }

    public function saveSettings()
    {
        $this->validate();

        $this->isLoading = true;

        try {
            // Salvar configurações
            PremiumAlbumSetting::setValue('commission_rate', $this->commission_rate);
            PremiumAlbumSetting::setValue('min_price', $this->min_price);
            PremiumAlbumSetting::setValue('max_price', $this->max_price);
            PremiumAlbumSetting::setValue('max_files_per_album', $this->max_files_per_album);
            PremiumAlbumSetting::setValue('max_file_size', $this->max_file_size);
            PremiumAlbumSetting::setValue('allowed_file_types', explode(',', $this->allowed_file_types));
            PremiumAlbumSetting::setValue('auto_approve', $this->auto_approve);
            PremiumAlbumSetting::setValue('require_verification', $this->require_verification);
            PremiumAlbumSetting::setValue('notify_new_albums', $this->notify_new_albums);
            PremiumAlbumSetting::setValue('notify_sales', $this->notify_sales);
            PremiumAlbumSetting::setValue('notify_reviews', $this->notify_reviews);

            // Limpar cache
            Cache::forget('premium_album_settings');

            session()->flash('success', 'Configurações salvas com sucesso!');

        } catch (\Exception $e) {
            session()->flash('error', 'Erro ao salvar configurações: ' . $e->getMessage());
        } finally {
            $this->isLoading = false;
        }
    }

    public function resetToDefaults()
    {
        $this->commission_rate = 15;
        $this->min_price = 5;
        $this->max_price = 500;
        $this->max_files_per_album = 50;
        $this->max_file_size = 50;
        $this->allowed_file_types = 'jpg,jpeg,png,gif,webp,mp4,mov,avi,webm';
        $this->auto_approve = false;
        $this->require_verification = true;
        $this->notify_new_albums = true;
        $this->notify_sales = true;
        $this->notify_reviews = true;

        session()->flash('info', 'Configurações restauradas para os valores padrão. Clique em "Salvar" para aplicar.');
    }

    public function getSystemStatsProperty()
    {
        return [
            'total_settings' => PremiumAlbumSetting::count(),
            'cache_status' => Cache::has('premium_album_settings') ? 'Ativo' : 'Inativo',
            'last_updated' => PremiumAlbumSetting::latest('updated_at')->first()?->updated_at?->diffForHumans() ?? 'Nunca',
        ];
    }

    public function clearCache()
    {
        Cache::forget('premium_album_settings');
        session()->flash('success', 'Cache limpo com sucesso!');
    }

    public function render()
    {
        return view('livewire.admin.premium-albums.system-settings', [
            'systemStats' => $this->systemStats,
        ])->layout('layouts.app');
    }
}
