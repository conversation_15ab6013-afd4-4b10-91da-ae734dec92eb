@props([
    'variant' => 'default',
    'padding' => true,
])

@php
    $classes = match($variant) {
        'outline' => 'border border-zinc-200 dark:border-zinc-700 bg-white dark:bg-zinc-800',
        'ghost' => 'bg-zinc-50 dark:bg-zinc-800/50',
        'elevated' => 'shadow-lg bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700',
        default => 'bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700',
    };
    
    $paddingClasses = $padding ? 'p-6' : '';
@endphp

<div {{ $attributes->merge(['class' => "rounded-lg {$classes} {$paddingClasses}"]) }}>
    {{ $slot }}
</div>
