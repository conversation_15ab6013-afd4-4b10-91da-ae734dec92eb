<?php

namespace App\Services;

use App\Models\User;
use App\Models\Event;
use App\Models\EventAttendee;
use App\Models\UserLabel;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class EventLabelService
{
    /**
     * Adicionar label de participante de evento para um usuário
     */
    public function addEventParticipantLabel(User $user, Event $event): void
    {
        try {
            // Verificar se o usuário já tem uma label para este evento
            $existingLabel = UserLabel::where('user_id', $user->id)
                ->where('name', 'LIKE', "Participante: {$event->name}%")
                ->first();

            if ($existingLabel) {
                Log::info('User already has event participant label', [
                    'user_id' => $user->id,
                    'event_id' => $event->id,
                    'label_id' => $existingLabel->id,
                ]);
                return;
            }

            // Criar nova label de participante
            $label = UserLabel::create([
                'user_id' => $user->id,
                'name' => "Participante: {$event->name}",
                'color' => 'purple',
                'icon' => 'ticket',
                'description' => "Participante confirmado do evento '{$event->name}' em {$event->formatted_date}",
                'is_active' => true,
                'expires_at' => $this->calculateLabelExpiration($event),
                'assigned_by' => null, // Sistema automático
            ]);

            Log::info('Event participant label created', [
                'user_id' => $user->id,
                'event_id' => $event->id,
                'label_id' => $label->id,
                'event_name' => $event->name,
            ]);

        } catch (\Exception $e) {
            Log::error('Error creating event participant label', [
                'user_id' => $user->id,
                'event_id' => $event->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Adicionar label de "Presente no Evento" após check-in
     */
    public function addEventAttendedLabel(User $user, Event $event): void
    {
        try {
            // Verificar se o usuário já tem uma label de "presente" para este evento
            $existingLabel = UserLabel::where('user_id', $user->id)
                ->where('name', 'LIKE', "Presente: {$event->name}%")
                ->first();

            if ($existingLabel) {
                Log::info('User already has event attended label', [
                    'user_id' => $user->id,
                    'event_id' => $event->id,
                    'label_id' => $existingLabel->id,
                ]);
                return;
            }

            // Criar nova label de "presente"
            $label = UserLabel::create([
                'user_id' => $user->id,
                'name' => "Presente: {$event->name}",
                'color' => 'green',
                'icon' => 'check-circle',
                'description' => "Esteve presente no evento '{$event->name}' em {$event->formatted_date}",
                'is_active' => true,
                'expires_at' => null, // Label permanente
                'assigned_by' => null, // Sistema automático
            ]);

            Log::info('Event attended label created', [
                'user_id' => $user->id,
                'event_id' => $event->id,
                'label_id' => $label->id,
                'event_name' => $event->name,
            ]);

        } catch (\Exception $e) {
            Log::error('Error creating event attended label', [
                'user_id' => $user->id,
                'event_id' => $event->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Adicionar label de "Organizador de Eventos" para criadores de eventos
     */
    public function addEventOrganizerLabel(User $user): void
    {
        try {
            // Verificar se o usuário já tem a label de organizador
            $existingLabel = UserLabel::where('user_id', $user->id)
                ->where('name', 'Organizador de Eventos')
                ->first();

            if ($existingLabel) {
                // Atualizar a label existente para garantir que está ativa
                $existingLabel->update([
                    'is_active' => true,
                    'expires_at' => null,
                ]);
                return;
            }

            // Verificar se o usuário tem pelo menos um evento criado
            $eventCount = Event::where('created_by', $user->id)->count();

            if ($eventCount > 0) {
                $label = UserLabel::create([
                    'user_id' => $user->id,
                    'name' => 'Organizador de Eventos',
                    'color' => 'blue',
                    'icon' => 'calendar',
                    'description' => "Organizador de eventos da plataforma ({$eventCount} eventos criados)",
                    'is_active' => true,
                    'expires_at' => null, // Label permanente
                    'assigned_by' => null, // Sistema automático
                ]);

                Log::info('Event organizer label created', [
                    'user_id' => $user->id,
                    'label_id' => $label->id,
                    'event_count' => $eventCount,
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Error creating event organizer label', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Remover label de participante se o usuário cancelar a inscrição
     */
    public function removeEventParticipantLabel(User $user, Event $event): void
    {
        try {
            $label = UserLabel::where('user_id', $user->id)
                ->where('name', 'LIKE', "Participante: {$event->name}%")
                ->first();

            if ($label) {
                $label->delete();

                Log::info('Event participant label removed', [
                    'user_id' => $user->id,
                    'event_id' => $event->id,
                    'label_id' => $label->id,
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Error removing event participant label', [
                'user_id' => $user->id,
                'event_id' => $event->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Calcular quando a label de participante deve expirar
     */
    private function calculateLabelExpiration(Event $event): ?Carbon
    {
        // Label expira 30 dias após o evento
        return Carbon::parse($event->date)->addDays(30);
    }

    /**
     * Processar labels para um participante confirmado
     */
    public function processParticipantLabels(EventAttendee $attendee): void
    {
        $user = $attendee->user;
        $event = $attendee->event;

        // Adicionar label de participante quando pagamento for confirmado
        if ($attendee->payment_status === 'completed') {
            $this->addEventParticipantLabel($user, $event);
        }

        // Adicionar label de "presente" quando fizer check-in
        if ($attendee->checked_in_at) {
            $this->addEventAttendedLabel($user, $event);
        }
    }

    /**
     * Limpar labels expiradas de eventos
     */
    public function cleanupExpiredEventLabels(): void
    {
        try {
            $expiredLabels = UserLabel::where('name', 'LIKE', 'Participante:%')
                ->where('expires_at', '<', now())
                ->where('is_active', true);

            $count = $expiredLabels->count();
            $expiredLabels->update(['is_active' => false]);

            Log::info('Expired event participant labels cleaned up', [
                'count' => $count,
            ]);

        } catch (\Exception $e) {
            Log::error('Error cleaning up expired event labels', [
                'error' => $e->getMessage(),
            ]);
        }
    }
}
