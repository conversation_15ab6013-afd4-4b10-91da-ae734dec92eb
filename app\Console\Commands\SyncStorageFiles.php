<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;

class SyncStorageFiles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'storage:sync {--force : Forçar sincronização de todos os arquivos}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sincroniza arquivos de storage/app/public para public/storage (KingHost)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔄 Sincronizando arquivos de storage...');
        $this->newLine();

        $force = $this->option('force');
        $sourceBase = storage_path('app/public');
        $targetBase = public_path('storage');

        if (!is_dir($sourceBase)) {
            $this->error('❌ Diretório source não encontrado: ' . $sourceBase);
            return 1;
        }

        // Criar diretório target se não existir
        if (!is_dir($targetBase)) {
            File::makeDirectory($targetBase, 0755, true);
            $this->info('✅ Diretório target criado: ' . $targetBase);
        }

        $syncedFiles = 0;
        $skippedFiles = 0;
        $errors = 0;

        // Sincronizar recursivamente
        $this->syncDirectory($sourceBase, $targetBase, $force, $syncedFiles, $skippedFiles, $errors);

        $this->newLine();
        $this->info("📊 Resumo da sincronização:");
        $this->line("  ✅ Arquivos sincronizados: {$syncedFiles}");
        $this->line("  ⏭️ Arquivos ignorados: {$skippedFiles}");
        $this->line("  ❌ Erros: {$errors}");

        if ($errors > 0) {
            $this->warn("⚠️ Alguns arquivos não puderam ser sincronizados. Verifique as permissões.");
            return 1;
        }

        $this->info("🎉 Sincronização concluída com sucesso!");
        return 0;
    }

    private function syncDirectory($sourceDir, $targetDir, $force, &$syncedFiles, &$skippedFiles, &$errors)
    {
        $items = scandir($sourceDir);

        foreach ($items as $item) {
            if ($item === '.' || $item === '..') {
                continue;
            }

            $sourcePath = $sourceDir . DIRECTORY_SEPARATOR . $item;
            $targetPath = $targetDir . DIRECTORY_SEPARATOR . $item;

            if (is_dir($sourcePath)) {
                // Criar diretório se não existir
                if (!is_dir($targetPath)) {
                    if (File::makeDirectory($targetPath, 0755, true)) {
                        $this->line("📁 Diretório criado: " . str_replace(public_path(), '', $targetPath));
                    } else {
                        $this->error("❌ Erro ao criar diretório: " . $targetPath);
                        $errors++;
                        continue;
                    }
                }

                // Sincronizar recursivamente
                $this->syncDirectory($sourcePath, $targetPath, $force, $syncedFiles, $skippedFiles, $errors);
            } else {
                // Verificar se precisa sincronizar o arquivo
                $needsSync = $force || 
                           !file_exists($targetPath) || 
                           filemtime($sourcePath) > filemtime($targetPath);

                if ($needsSync) {
                    if (copy($sourcePath, $targetPath)) {
                        $relativePath = str_replace(storage_path('app/public'), '', $sourcePath);
                        $this->line("✅ Sincronizado: " . $relativePath);
                        $syncedFiles++;
                    } else {
                        $this->error("❌ Erro ao copiar: " . $sourcePath);
                        $errors++;
                    }
                } else {
                    $skippedFiles++;
                }
            }
        }
    }
}
