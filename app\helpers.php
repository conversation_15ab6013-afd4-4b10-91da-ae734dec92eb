<?php

use App\Helpers\RankingHelper;
use App\Models\User;

if (!function_exists('getRankingBorderClass')) {
    /**
     * Get the ranking border class for a user
     *
     * @param User $user
     * @return string
     */
    function getRankingBorderClass(User $user): string
    {
        return RankingHelper::getRankingBorderClass($user);
    }
}

if (!function_exists('getRankingBorderClassById')) {
    /**
     * Get the ranking border class by user ID
     *
     * @param int $userId
     * @return string
     */
    function getRankingBorderClassById(int $userId): string
    {
        return RankingHelper::getRankingBorderClassById($userId);
    }
}

if (!function_exists('getUserRankingPosition')) {
    /**
     * Get the ranking position for a user
     *
     * @param User $user
     * @return int|null
     */
    function getUserRankingPosition(User $user): ?int
    {
        return RankingHelper::getUserRankingPosition($user);
    }
}

if (!function_exists('getRankingInfo')) {
    /**
     * Get complete ranking info for a user
     *
     * @param User $user
     * @return array
     */
    function getRankingInfo(User $user): array
    {
        return RankingHelper::getRankingInfo($user);
    }
}

if (!function_exists('getAvatarWithRankingBorder')) {
    /**
     * Get avatar HTML with ranking border
     *
     * @param User $user
     * @param string $avatarUrl
     * @param string $additionalClasses
     * @param string $size
     * @return string
     */
    function getAvatarWithRankingBorder(User $user, string $avatarUrl, string $additionalClasses = '', string $size = 'md'): string
    {
        return RankingHelper::getAvatarWithRankingBorder($user, $avatarUrl, $additionalClasses, $size);
    }
}
