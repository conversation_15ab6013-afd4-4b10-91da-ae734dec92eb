<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithFileUploads;
use App\Models\JobVacancy;
use App\Models\JobApplication as JobApplicationModel;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use App\Notifications\JobApplicationSubmitted;
use App\Notifications\JobApplicationConfirmation;
use App\Models\User;

class JobApplication extends Component
{
    use WithFileUploads;

    // Propriedades públicas
    public $job;
    public $resume;
    public $cover_letter = '';
    public $name = '';
    public $email = '';
    public $phone = '';
    public $experience = '';

    // Estados do componente
    public $isSubmitting = false;
    public $uploadProgress = 0;
    public $showSuccess = false;
    public $errorMessage = '';

    // Regras de validação
    protected function rules()
    {
        $rules = [
            'name' => 'required|string|min:2|max:100',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|min:10|max:20',
            'experience' => 'nullable|string|max:1000',
        ];

        if ($this->job->requires_resume) {
            $rules['resume'] = 'required|file|mimes:pdf,doc,docx|max:2048';
        } else {
            $rules['resume'] = 'nullable|file|mimes:pdf,doc,docx|max:2048';
        }

        if ($this->job->requires_cover_letter) {
            $rules['cover_letter'] = 'required|string|min:100|max:2000';
        } else {
            $rules['cover_letter'] = 'nullable|string|max:2000';
        }

        return $rules;
    }

    // Mensagens de validação customizadas
    protected $messages = [
        'name.required' => 'O nome é obrigatório.',
        'name.min' => 'O nome deve ter pelo menos 2 caracteres.',
        'name.max' => 'O nome não pode ter mais de 100 caracteres.',
        'email.required' => 'O email é obrigatório.',
        'email.email' => 'Digite um email válido.',
        'phone.required' => 'O telefone é obrigatório.',
        'phone.min' => 'O telefone deve ter pelo menos 10 dígitos.',
        'phone.max' => 'O telefone não pode ter mais de 20 caracteres.',
        'experience.max' => 'A experiência não pode ter mais de 1000 caracteres.',
        'resume.required' => 'O currículo é obrigatório para esta vaga.',
        'resume.file' => 'O arquivo deve ser válido.',
        'resume.mimes' => 'O currículo deve ser um arquivo PDF, DOC ou DOCX.',
        'resume.max' => 'O currículo não pode ter mais de 2MB.',
        'cover_letter.required' => 'A carta de apresentação é obrigatória para esta vaga.',
        'cover_letter.min' => 'A carta de apresentação deve ter pelo menos 100 caracteres.',
        'cover_letter.max' => 'A carta de apresentação não pode ter mais de 2000 caracteres.',
    ];

    public function mount(JobVacancy $job)
    {
        // Usar file_put_contents para garantir que o log seja escrito
        file_put_contents(storage_path('logs/livewire-test.log'),
            '[' . now() . '] JobApplication component mounted - Job ID: ' . $job->id . ' - User: ' . (Auth::check() ? Auth::id() : 'guest') . PHP_EOL,
            FILE_APPEND | LOCK_EX
        );

        $this->job = $job;

        // Pré-preencher dados do usuário se estiver logado
        if (Auth::check()) {
            $user = Auth::user();
            $this->name = $user->name;
            $this->email = $user->email;
            $this->phone = ''; // Campo telefone não existe na tabela users
        }
    }

    public function updatedResume()
    {
        $this->validateOnly('resume');
        $this->uploadProgress = 100;
    }

    public function apply()
    {
        // Verificar se o usuário está autenticado
        if (!Auth::check()) {
            $this->errorMessage = 'Você precisa estar logado para se candidatar.';
            return;
        }

        // Verificar se a vaga ainda está aceitando candidaturas
        if (!$this->job->isAcceptingApplications()) {
            $this->errorMessage = 'Esta vaga não está mais aceitando candidaturas.';
            return;
        }

        // Verificar se o usuário já se candidatou
        if ($this->job->hasUserApplied(Auth::id())) {
            $this->errorMessage = 'Você já se candidatou a esta vaga.';
            return;
        }

        $this->isSubmitting = true;
        $this->errorMessage = '';

        try {
            // Validar dados
            $this->validate();

            // Processar upload do currículo
            $resumePath = null;
            $resumeFilename = null;

            if ($this->resume) {
                $filename = 'resume_' . Auth::id() . '_' . $this->job->id . '_' . time() . '.' . $this->resume->getClientOriginalExtension();
                $resumePath = $this->resume->storeAs('job-applications', $filename, 'private');
                $resumeFilename = $this->resume->getClientOriginalName();
            }

            // Criar candidatura
            $application = JobApplicationModel::create([
                'job_id' => $this->job->id,
                'job_vacancy_id' => $this->job->id,
                'user_id' => Auth::id(),
                'cover_letter' => $this->cover_letter ?: '',
                'resume_path' => $resumePath,
                'resume_filename' => $resumeFilename,
                'is_vip_priority' => Auth::user()->role === 'vip',
                'candidate_name' => $this->name,
                'candidate_email' => $this->email,
                'candidate_phone' => $this->phone ?: '',
                'candidate_experience' => $this->experience ?: '',
                'priority_level' => Auth::user()->role === 'vip' ? 'alta' : 'normal',
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]);

            // Incrementar contador de candidaturas
            $this->job->increment('applications_count');

            // Enviar notificações por email
            $this->sendNotificationEmails($application);

            // Resetar formulário
            $this->resetForm();

            // Mostrar sucesso
            $this->showSuccess = true;
            $this->isSubmitting = false;

            // Dispatch evento de sucesso
            $this->dispatch('application-submitted', [
                'type' => 'success',
                'message' => 'Candidatura enviada com sucesso! ' .
                    (Auth::user()->role === 'vip' ? 'Sua candidatura VIP terá prioridade na análise.' : '')
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            $this->isSubmitting = false;
            throw $e;
        } catch (\Exception $e) {
            $this->isSubmitting = false;

            Log::error('Erro ao enviar candidatura', [
                'user_id' => Auth::id(),
                'job_id' => $this->job->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            // Mostrar erro mais específico em desenvolvimento
            if (config('app.debug')) {
                $this->errorMessage = 'Erro ao enviar candidatura: ' . $e->getMessage();
            } else {
                $this->errorMessage = 'Erro ao enviar candidatura. Tente novamente.';
            }
        }

        $this->isSubmitting = false;
    }



    private function sendNotificationEmails($application)
    {
        try {
            // Enviar notificação para administradores
            $adminUsers = User::where('role', 'admin')->get();
            foreach ($adminUsers as $admin) {
                try {
                    $admin->notify(new JobApplicationSubmitted($application));
                    $application->logEmailSent('admin_notification', $admin->email, true);
                } catch (\Exception $e) {
                    $application->logEmailSent('admin_notification', $admin->email, false, $e->getMessage());
                }
            }

            // Enviar confirmação para o candidato
            try {
                Auth::user()->notify(new JobApplicationConfirmation($application));
                $application->logEmailSent('candidate_confirmation', Auth::user()->email, true);
            } catch (\Exception $e) {
                $application->logEmailSent('candidate_confirmation', Auth::user()->email, false, $e->getMessage());
            }

            // Enviar cópia para email de contato
            try {
                $contactEmail = '<EMAIL>';
                \Illuminate\Support\Facades\Notification::route('mail', $contactEmail)
                    ->notify(new JobApplicationSubmitted($application));
                $application->logEmailSent('contact_copy', $contactEmail, true);
            } catch (\Exception $e) {
                $application->logEmailSent('contact_copy', '<EMAIL>', false, $e->getMessage());
            }

        } catch (\Exception $e) {
            Log::error('Erro geral ao enviar emails de candidatura', [
                'application_id' => $application->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    private function resetForm()
    {
        $this->resume = null;
        $this->cover_letter = '';
        $this->experience = '';
        $this->uploadProgress = 0;
    }

    public function getFileSize()
    {
        if ($this->resume) {
            $bytes = $this->resume->getSize();
            $units = ['B', 'KB', 'MB', 'GB'];

            for ($i = 0; $bytes > 1024; $i++) {
                $bytes /= 1024;
            }

            return round($bytes, 2) . ' ' . $units[$i];
        }

        return null;
    }

    public function removeFile()
    {
        $this->resume = null;
        $this->uploadProgress = 0;
    }

    public function render()
    {
        return view('livewire.job-application');
    }
}
