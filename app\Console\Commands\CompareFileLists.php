<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class CompareFileLists extends Command
{
    protected $signature = 'files:compare {local-file} {ftp-file} {--output=differences.txt}';
    protected $description = 'Compara duas listas de arquivos e identifica diferenças';

    public function handle()
    {
        $localFile = $this->argument('local-file');
        $ftpFile = $this->argument('ftp-file');
        $outputFile = $this->option('output');
        
        if (!File::exists($localFile)) {
            $this->error("❌ Arquivo local não encontrado: {$localFile}");
            return 1;
        }
        
        if (!File::exists($ftpFile)) {
            $this->error("❌ Arquivo FTP não encontrado: {$ftpFile}");
            return 1;
        }
        
        $this->info('🔍 Comparando listas de arquivos...');
        
        $localFiles = $this->parseFileList($localFile);
        $ftpFiles = $this->parseFileList($ftpFile);
        
        $this->info("📊 Local: " . count($localFiles) . " arquivos");
        $this->info("📊 FTP: " . count($ftpFiles) . " arquivos");
        
        // Encontrar diferenças
        $onlyLocal = array_diff_key($localFiles, $ftpFiles);
        $onlyFtp = array_diff_key($ftpFiles, $localFiles);
        $modified = [];
        
        // Verificar arquivos modificados
        foreach ($localFiles as $path => $localInfo) {
            if (isset($ftpFiles[$path])) {
                if ($localInfo['modified'] !== $ftpFiles[$path]['modified']) {
                    $modified[$path] = [
                        'local' => $localInfo,
                        'ftp' => $ftpFiles[$path]
                    ];
                }
            }
        }
        
        // Gerar relatório
        $report = $this->generateReport($onlyLocal, $onlyFtp, $modified);
        
        // Salvar relatório
        File::put($outputFile, $report);
        
        $this->info("✅ Relatório gerado: {$outputFile}");
        $this->info("📊 Apenas local: " . count($onlyLocal));
        $this->info("📊 Apenas FTP: " . count($onlyFtp));
        $this->info("📊 Modificados: " . count($modified));
        
        // Mostrar resumo
        if (count($onlyFtp) > 0) {
            $this->warn("⚠️ Arquivos que existem apenas no FTP (podem estar quebrando o layout):");
            foreach (array_slice($onlyFtp, 0, 10) as $path => $info) {
                $this->line("  - {$path}");
            }
            if (count($onlyFtp) > 10) {
                $this->line("  ... e mais " . (count($onlyFtp) - 10) . " arquivos");
            }
        }
        
        return 0;
    }
    
    private function parseFileList($filePath)
    {
        $content = File::get($filePath);
        $lines = explode("\n", $content);
        $files = [];
        
        foreach ($lines as $line) {
            $line = trim($line);
            
            // Pular comentários e linhas vazias
            if (empty($line) || str_starts_with($line, '#')) {
                continue;
            }
            
            // Formato: data | tamanho | caminho
            $parts = explode(' | ', $line, 3);
            if (count($parts) === 3) {
                $files[$parts[2]] = [
                    'modified' => $parts[0],
                    'size' => $parts[1]
                ];
            }
        }
        
        return $files;
    }
    
    private function generateReport($onlyLocal, $onlyFtp, $modified)
    {
        $report = "# Relatório de Diferenças entre Local e FTP\n";
        $report .= "# Gerado em: " . date('Y-m-d H:i:s') . "\n\n";
        
        $report .= "## 📊 Resumo\n";
        $report .= "- Apenas no Local: " . count($onlyLocal) . " arquivos\n";
        $report .= "- Apenas no FTP: " . count($onlyFtp) . " arquivos\n";
        $report .= "- Modificados: " . count($modified) . " arquivos\n\n";
        
        if (count($onlyFtp) > 0) {
            $report .= "## ⚠️ Arquivos que existem APENAS no FTP (podem estar quebrando o layout)\n";
            $report .= "Estes arquivos devem ser removidos do FTP:\n\n";
            foreach ($onlyFtp as $path => $info) {
                $report .= "- {$path} ({$info['modified']} | {$info['size']})\n";
            }
            $report .= "\n";
        }
        
        if (count($onlyLocal) > 0) {
            $report .= "## 📤 Arquivos que existem APENAS no Local\n";
            $report .= "Estes arquivos precisam ser enviados para o FTP:\n\n";
            foreach ($onlyLocal as $path => $info) {
                $report .= "- {$path} ({$info['modified']} | {$info['size']})\n";
            }
            $report .= "\n";
        }
        
        if (count($modified) > 0) {
            $report .= "## 🔄 Arquivos Modificados\n";
            $report .= "Estes arquivos têm versões diferentes:\n\n";
            foreach ($modified as $path => $info) {
                $report .= "- {$path}\n";
                $report .= "  Local: {$info['local']['modified']} | {$info['local']['size']}\n";
                $report .= "  FTP:   {$info['ftp']['modified']} | {$info['ftp']['size']}\n\n";
            }
        }
        
        return $report;
    }
}
