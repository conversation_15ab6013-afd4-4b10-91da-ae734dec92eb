<?php

namespace App\Livewire;

use App\Models\LiveStream;
use App\Models\LiveStreamMessage;
use App\Models\LiveStreamDonation;
use App\Models\Payment;
use App\Models\Wallet;
use App\Models\WalletTransaction;
use App\Models\Notification;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Livewire\Component;

class LiveStreamViewer extends Component
{
    public $liveStream;
    public $messages = [];
    public $newMessage = '';
    public $showDonationModal = false;
    public $selectedCharm = null;
    public $donationMessage = '';
    public $walletBalance = 0.00;
    public $processing = false;

    // Define charm types with their prices and icons (mesmo do SendCharm)
    public $charms = [
        'flower' => [
            'name' => 'Flor',
            'price' => 10.00,
            'icon' => 'sparkles',
            'description' => 'Uma linda flor para alegrar o dia'
        ],
        'kiss' => [
            'name' => 'Beijo',
            'price' => 20.00,
            'icon' => 'heart',
            'description' => 'Um beijo especial'
        ],
        'drink' => [
            'name' => 'Drink',
            'price' => 30.00,
            'icon' => 'beaker',
            'description' => 'Um drink para refrescar'
        ],
        'ticket' => [
            'name' => 'Ingresso',
            'price' => 250.00,
            'icon' => 'star',
            'description' => 'Um convite especial'
        ]
    ];

    protected $listeners = [
        'live-stream-message' => 'addMessage',
        'live-stream-donation' => 'addDonation',
        'live-stream-ended' => 'handleStreamEnded',
    ];

    public function mount(LiveStream $liveStream)
    {
        $this->liveStream = $liveStream;
        $this->loadMessages();
        $this->refreshWalletBalance();

        // Incrementar contador de viewers se a live estiver ativa
        if ($liveStream->isLive()) {
            $liveStream->incrementViewers();
            $this->dispatch('viewer-joined')->to('live-stream-broadcast');
        }
    }

    public function sendMessage()
    {
        if (!$this->liveStream || empty(trim($this->newMessage))) {
            return;
        }

        $message = LiveStreamMessage::create([
            'live_stream_id' => $this->liveStream->id,
            'user_id' => Auth::id(),
            'message' => $this->newMessage,
            'type' => 'message',
        ]);

        $this->newMessage = '';
        $this->loadMessages();

        // Broadcast para o broadcaster e outros viewers
        $this->dispatch('live-stream-message', [
            'stream_id' => $this->liveStream->id,
            'message' => $message->toArray(),
            'user' => Auth::user()->toArray(),
        ]);
    }

    public function selectCharm($charmType)
    {
        $this->selectedCharm = $charmType;
    }

    public function sendDonation()
    {
        if (!$this->selectedCharm || $this->processing) {
            return;
        }

        $this->processing = true;

        try {
            $sender = Auth::user();
            $wallet = $sender->wallet;
            $charmPrice = $this->charms[$this->selectedCharm]['price'];

            // Refresh wallet balance
            $wallet->refresh();
            $this->walletBalance = $wallet->balance;

            // Check if user has enough balance
            if ($wallet->balance < $charmPrice) {
                throw new \Exception('Saldo insuficiente na carteira. Adicione fundos para enviar este charm.');
            }

            DB::beginTransaction();

            // Create donation record
            $donation = LiveStreamDonation::create([
                'live_stream_id' => $this->liveStream->id,
                'user_id' => $this->liveStream->user_id,
                'sender_id' => Auth::id(),
                'amount' => $charmPrice,
                'charm_type' => $this->selectedCharm,
                'message' => $this->donationMessage ?: null,
                'status' => 'completed',
            ]);

            // Create payment record
            $payment = Payment::create([
                'user_id' => $this->liveStream->user_id,
                'sender_id' => Auth::id(),
                'amount' => $charmPrice,
                'message' => $this->donationMessage ?: null,
                'status' => 'completed',
                'payment_id' => 'LIVE-' . uniqid(),
                'payment_date' => now(),
                'payment_method' => 'wallet',
                'charm_type' => $this->selectedCharm,
            ]);

            // Process wallet transaction for sender (subtract funds)
            $transaction = $wallet->subtractFunds(
                $charmPrice,
                'live_donation_sent',
                $this->charms[$this->selectedCharm]['name'] . ' para ' . $this->liveStream->user->name . ' (Live)',
                $donation->id,
                LiveStreamDonation::class
            );

            if (!$transaction) {
                throw new \Exception('Falha ao processar a transação da carteira.');
            }

            // Process wallet transaction for receiver (add funds)
            $receiverWallet = $this->liveStream->user->wallet;
            $receiverTransaction = $receiverWallet->addFunds(
                $charmPrice,
                'live_donation_received',
                $this->charms[$this->selectedCharm]['name'] . ' de ' . $sender->name . ' (Live)',
                $donation->id,
                LiveStreamDonation::class
            );

            if (!$receiverTransaction) {
                throw new \Exception('Falha ao adicionar fundos à carteira do destinatário.');
            }

            // Update live stream total donations
            $this->liveStream->addDonation($charmPrice);

            // Create donation message in chat
            $donationMessage = LiveStreamMessage::create([
                'live_stream_id' => $this->liveStream->id,
                'user_id' => Auth::id(),
                'message' => $this->donationMessage ?: 'Enviou um ' . $this->charms[$this->selectedCharm]['name'],
                'type' => 'donation',
                'data' => [
                    'charm_type' => $this->selectedCharm,
                    'amount' => $charmPrice,
                    'charm_name' => $this->charms[$this->selectedCharm]['name']
                ]
            ]);

            // Create notification for recipient
            $this->liveStream->user->notifications()->create([
                'sender_id' => $sender->id,
                'type' => 'live_donation_received',
                'message' => "{$sender->name} enviou um charm '{$this->charms[$this->selectedCharm]['name']}' na sua live.",
                'read' => false,
                'data' => json_encode([
                    'charm_type' => $this->selectedCharm,
                    'amount' => $charmPrice,
                    'message' => $this->donationMessage,
                    'live_stream_id' => $this->liveStream->id
                ])
            ]);

            DB::commit();

            // Broadcast donation
            $this->dispatch('live-stream-donation', [
                'stream_id' => $this->liveStream->id,
                'donation' => $donation->toArray(),
                'user' => $sender->toArray(),
            ]);

            $this->loadMessages();
            $this->refreshWalletBalance();
            $this->closeDonationModal();

            $this->dispatch(
                'toast',
                message: 'Doação enviada com sucesso!',
                type: 'success'
            )->to('toast-notification');

        } catch (\Exception $e) {
            DB::rollBack();
            $this->addError('donation', 'Erro ao processar doação: ' . $e->getMessage());
        }

        $this->processing = false;
    }

    public function addMessage($data)
    {
        if ($data['stream_id'] == $this->liveStream->id) {
            $this->loadMessages();
        }
    }

    public function addDonation($data)
    {
        if ($data['stream_id'] == $this->liveStream->id) {
            $this->loadMessages();
        }
    }

    public function handleStreamEnded($data)
    {
        if ($data == $this->liveStream->id) {
            $this->liveStream = $this->liveStream->fresh();
            $this->dispatch(
                'toast',
                message: 'A live foi encerrada pelo streamer.',
                type: 'info'
            )->to('toast-notification');
        }
    }

    public function refreshWalletBalance()
    {
        $user = Auth::user();
        if ($user) {
            $user->refresh();
            $wallet = $user->wallet()->first();
            if ($wallet) {
                $wallet->refresh();
                $this->walletBalance = $wallet->balance;
            } else {
                $wallet = $user->wallet()->create([
                    'balance' => 0.00,
                    'active' => true,
                ]);
                $this->walletBalance = 0.00;
            }
        }
    }

    private function loadMessages()
    {
        $this->messages = LiveStreamMessage::where('live_stream_id', $this->liveStream->id)
            ->with('user.userPhotos')
            ->latest()
            ->take(50)
            ->get()
            ->reverse()
            ->values()
            ->toArray();
    }

    public function openDonationModal()
    {
        $this->showDonationModal = true;
        $this->refreshWalletBalance();
    }

    public function closeDonationModal()
    {
        $this->showDonationModal = false;
        $this->reset(['selectedCharm', 'donationMessage', 'processing']);
    }

    public function render()
    {
        return view('livewire.live-stream-viewer');
    }
}
