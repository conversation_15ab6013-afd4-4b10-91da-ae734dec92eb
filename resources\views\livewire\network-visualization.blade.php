<div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md p-6 overflow-hidden">
    <flux:heading size="lg" class="flex items-center mb-2">
        <flux:icon.share class="w-5 h-5 mr-2 text-indigo-500" />
        Visualização da Rede
    </flux:heading>

    @if(Auth::check() && Auth::id() !== $user->id)
        <flux:text class="mb-6 text-gray-600 dark:text-gray-400">
            Conexões em comum com {{ $user->name }}
        </flux:text>
    @else
        <flux:text class="mb-6 text-gray-600 dark:text-gray-400">
            Sua rede de conexões e relacionamentos
        </flux:text>
    @endif

    @if(!empty($networkData))
        {{-- Área de Visualização da Rede --}}
        <div class="relative bg-gray-50 dark:bg-zinc-700 rounded-lg p-4 sm:p-8 min-h-80 sm:min-h-96 overflow-hidden">
            {{-- Usuário Central --}}
            <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10">
                <div class="relative group">
                    <div class="w-16 h-16 sm:w-20 sm:h-20 rounded-full border-4 {{ $this->getConnectionColor($networkData['center']['type']) }} bg-white dark:bg-zinc-800 overflow-hidden shadow-lg">
                        <img
                            src="{{ $networkData['center']['avatar'] }}"
                            alt="{{ $networkData['center']['name'] }}"
                            class="w-full h-full object-cover"
                        >
                    </div>
                    <div class="absolute -bottom-8 sm:-bottom-10 left-1/2 transform -translate-x-1/2 text-center min-w-0 max-w-24 sm:max-w-32">
                        <flux:heading size="xs" class="truncate" title="{{ $networkData['center']['name'] }}">
                            {{ $networkData['center']['name'] }}
                        </flux:heading>
                        <flux:text size="xs" class="text-gray-600 dark:text-gray-400 truncate">
                            {{ $this->getConnectionLabel($networkData['center']['type']) }}
                        </flux:text>
                    </div>
                </div>
            </div>

            {{-- Usuário Atual (se visualizando outro perfil) --}}
            @if(isset($networkData['current_user']))
                <div class="absolute top-2 left-2 sm:top-4 sm:left-4">
                    <div class="relative group">
                        <div class="w-12 h-12 sm:w-16 sm:h-16 rounded-full border-4 {{ $this->getConnectionColor($networkData['current_user']['type']) }} bg-white dark:bg-zinc-800 overflow-hidden shadow-lg">
                            <img
                                src="{{ $networkData['current_user']['avatar'] }}"
                                alt="{{ $networkData['current_user']['name'] }}"
                                class="w-full h-full object-cover"
                            >
                        </div>
                        <div class="absolute -bottom-5 sm:-bottom-6 left-1/2 transform -translate-x-1/2 text-center">
                            <flux:text size="xs" class="font-medium text-gray-900 dark:text-white">
                                Você
                            </flux:text>
                        </div>
                    </div>
                </div>
            @endif

            {{-- Conexões em Círculo --}}
            @if(!empty($networkData['connections']))
                @foreach($networkData['connections'] as $index => $connection)
                    @php
                        $angle = ($index / count($networkData['connections'])) * 360;
                        $radius = min(120, 100); // Radius responsivo
                        $x = 50 + ($radius * cos(deg2rad($angle))) / 4; // Ajuste para porcentagem
                        $y = 50 + ($radius * sin(deg2rad($angle))) / 4; // Ajuste para porcentagem

                        // Garantir que as conexões fiquem dentro dos limites
                        $x = max(15, min(85, $x));
                        $y = max(15, min(85, $y));
                    @endphp

                    <div
                        class="absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer group"
                        style="left: {{ $x }}%; top: {{ $y }}%;"
                        wire:click="showConnectionDetails({{ $connection['id'] }})"
                    >
                        {{-- Avatar da Conexão --}}
                        <div class="relative">
                            <div class="w-10 h-10 sm:w-12 sm:h-12 rounded-full border-2 {{ $this->getConnectionColor($connection['type']) }} bg-white dark:bg-zinc-800 overflow-hidden shadow-md group-hover:shadow-lg transition-all duration-200 group-hover:scale-110">
                                <img
                                    src="{{ $connection['avatar'] }}"
                                    alt="{{ $connection['name'] }}"
                                    class="w-full h-full object-cover"
                                >
                            </div>

                            {{-- Indicador de Força da Conexão --}}
                            <div class="absolute -bottom-1 -right-1 w-3 h-3 sm:w-4 sm:h-4 rounded-full border-2 border-white dark:border-zinc-800
                                {{ $connection['connection_strength'] >= 70 ? 'bg-green-500' :
                                   ($connection['connection_strength'] >= 40 ? 'bg-yellow-500' : 'bg-gray-400') }}">
                            </div>

                            {{-- Tooltip usando Flux --}}
                            <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-black text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-20 max-w-32 sm:max-w-none">
                                <flux:text size="xs" class="text-white font-medium">
                                    {{ $connection['name'] }}
                                </flux:text>
                                <flux:text size="xs" class="text-gray-300">
                                    {{ $this->getConnectionLabel($connection['type']) }}
                                </flux:text>
                                {{-- Seta do tooltip --}}
                                <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-black"></div>
                            </div>
                        </div>
                    </div>
                @endforeach
            @endif
        </div>

        {{-- Estatísticas --}}
        <div class="mt-6 grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
            @if(isset($networkData['stats']['followers_count']))
                <div class="text-center p-3 sm:p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg overflow-hidden">
                    <flux:heading size="lg" class="text-blue-600 dark:text-blue-400 mb-1">
                        {{ $networkData['stats']['followers_count'] }}
                    </flux:heading>
                    <flux:text size="sm" class="text-blue-700 dark:text-blue-300">
                        Seguidores
                    </flux:text>
                </div>

                <div class="text-center p-3 sm:p-4 bg-green-50 dark:bg-green-900/20 rounded-lg overflow-hidden">
                    <flux:heading size="lg" class="text-green-600 dark:text-green-400 mb-1">
                        {{ $networkData['stats']['following_count'] }}
                    </flux:heading>
                    <flux:text size="sm" class="text-green-700 dark:text-green-300">
                        Seguindo
                    </flux:text>
                </div>
            @endif

            @if(isset($networkData['stats']['mutual_followers']))
                <div class="text-center p-3 sm:p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg overflow-hidden">
                    <flux:heading size="lg" class="text-purple-600 dark:text-purple-400 mb-1">
                        {{ $networkData['stats']['mutual_followers'] }}
                    </flux:heading>
                    <flux:text size="sm" class="text-purple-700 dark:text-purple-300">
                        Seguidores Mútuos
                    </flux:text>
                </div>

                <div class="text-center p-3 sm:p-4 bg-indigo-50 dark:bg-indigo-900/20 rounded-lg overflow-hidden">
                    <flux:heading size="lg" class="text-indigo-600 dark:text-indigo-400 mb-1">
                        {{ $networkData['stats']['total_mutual'] }}
                    </flux:heading>
                    <flux:text size="sm" class="text-indigo-700 dark:text-indigo-300">
                        Total em Comum
                    </flux:text>
                </div>
            @endif
        </div>

        {{-- Legenda --}}
        <div class="mt-4 p-3 bg-gray-50 dark:bg-zinc-700 rounded-lg">
            <flux:heading size="sm" class="mb-3 text-gray-900 dark:text-white">
                Legenda das Conexões
            </flux:heading>
            <div class="flex flex-wrap gap-4 sm:gap-6">
                <div class="flex items-center">
                    <div class="w-3 h-3 rounded-full bg-green-500 mr-2 flex-shrink-0"></div>
                    <flux:text size="sm" class="text-gray-600 dark:text-gray-400">
                        Conexão Forte (70%+)
                    </flux:text>
                </div>
                <div class="flex items-center">
                    <div class="w-3 h-3 rounded-full bg-yellow-500 mr-2 flex-shrink-0"></div>
                    <flux:text size="sm" class="text-gray-600 dark:text-gray-400">
                        Conexão Média (40-69%)
                    </flux:text>
                </div>
                <div class="flex items-center">
                    <div class="w-3 h-3 rounded-full bg-gray-400 mr-2 flex-shrink-0"></div>
                    <flux:text size="sm" class="text-gray-600 dark:text-gray-400">
                        Conexão Fraca (0-39%)
                    </flux:text>
                </div>
            </div>
        </div>
    @else
        <div class="text-center py-12">
            <flux:icon.users class="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <flux:heading size="lg" class="mb-2 text-gray-900 dark:text-white">
                Nenhuma conexão encontrada
            </flux:heading>
            <flux:text class="text-gray-600 dark:text-gray-400 max-w-md mx-auto">
                @if(Auth::check() && Auth::id() !== $user->id)
                    Vocês não têm conexões em comum no momento. Que tal começar a seguir pessoas em comum?
                @else
                    Comece a seguir pessoas para ver sua rede de conexões e descobrir novas oportunidades de networking.
                @endif
            </flux:text>
        </div>
    @endif

    {{-- Modal de Detalhes da Conexão --}}
    @if($showModal && $selectedConnection)
        <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4" wire:click="closeModal">
            <div class="bg-white dark:bg-zinc-800 rounded-lg p-6 max-w-md w-full overflow-hidden" wire:click.stop>
                <div class="flex items-center justify-between mb-4">
                    <flux:heading size="lg" class="text-gray-900 dark:text-white">
                        Detalhes da Conexão
                    </flux:heading>
                    <flux:button wire:click="closeModal" variant="ghost" size="sm">
                        <flux:icon.x-mark class="w-5 h-5" />
                    </flux:button>
                </div>
                
                <div class="flex items-center mb-6">
                    <img
                        src="{{ $selectedConnection['avatar'] }}"
                        alt="{{ $selectedConnection['name'] }}"
                        class="w-16 h-16 rounded-full border-2 {{ $this->getConnectionColor($selectedConnection['type']) }} mr-4 flex-shrink-0"
                    >
                    <div class="min-w-0 flex-1">
                        <flux:heading size="md" class="text-gray-900 dark:text-white truncate" title="{{ $selectedConnection['name'] }}">
                            {{ $selectedConnection['name'] }}
                        </flux:heading>
                        <flux:text size="sm" class="text-gray-600 dark:text-gray-400 truncate">
                            {{ '@' . $selectedConnection['username'] }}
                        </flux:text>
                        <flux:text size="sm" class="text-purple-600 dark:text-purple-400">
                            {{ $this->getConnectionLabel($selectedConnection['type']) }}
                        </flux:text>
                    </div>
                </div>
                
                <div class="mb-6">
                    <div class="flex items-center justify-between mb-3">
                        <flux:text size="sm" class="text-gray-600 dark:text-gray-400">
                            Força da Conexão
                        </flux:text>
                        <div class="flex items-center gap-2">
                            <div class="w-3 h-3 rounded-full {{ $selectedConnection['connection_strength'] >= 70 ? 'bg-green-500' : ($selectedConnection['connection_strength'] >= 40 ? 'bg-yellow-500' : 'bg-gray-400') }}"></div>
                            <flux:text size="sm" class="font-medium text-gray-900 dark:text-white">
                                {{ $selectedConnection['connection_strength'] }}%
                            </flux:text>
                        </div>
                    </div>
                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                        <div
                            class="h-3 rounded-full transition-all duration-300 {{ $selectedConnection['connection_strength'] >= 70 ? 'bg-green-500' :
                                ($selectedConnection['connection_strength'] >= 40 ? 'bg-yellow-500' : 'bg-gray-400') }}"
                            style="width: {{ $selectedConnection['connection_strength'] }}%"
                        ></div>
                    </div>
                </div>
                
                <div class="flex flex-col sm:flex-row gap-3">
                    <flux:button
                        href="/{{ $selectedConnection['username'] }}"
                        wire:navigate
                        variant="primary"
                        size="sm"
                        class="flex-1"
                    >
                        <flux:icon.eye class="w-4 h-4 mr-2" />
                        Ver Perfil
                    </flux:button>

                    <flux:button
                        href="{{ route('caixa_de_mensagens', ['user' => $selectedConnection['username']]) }}"
                        wire:navigate
                        variant="ghost"
                        size="sm"
                        class="flex-1"
                    >
                        <flux:icon.chat-bubble-left class="w-4 h-4 mr-2" />
                        Mensagem
                    </flux:button>
                </div>
            </div>
        </div>
    @endif
</div>
