<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>[ADMIN] Status do Pedido Atualizado</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f4f4f4;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #fff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #dc3545, #6c757d);
            color: white;
            padding: 30px;
            text-align: center;
            border-radius: 10px 10px 0 0;
            margin: -30px -30px 30px -30px;
        }
        .admin-badge {
            background-color: #dc3545;
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 20px;
        }
        .status-update {
            background-color: #FFE600;
            color: #333;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
            font-size: 18px;
            font-weight: bold;
        }
        .info-box {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }
        .info-box h3 {
            margin-top: 0;
            color: #dc3545;
        }
        .user-info {
            background-color: #e9ecef;
            border-left: 4px solid #dc3545;
            padding: 15px;
            margin: 15px 0;
        }
        .status-badge {
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
            display: inline-block;
            margin: 10px 5px;
        }
        .status-pending { background-color: #ffc107; color: #333; }
        .status-processing { background-color: #17a2b8; color: white; }
        .status-completed { background-color: #28a745; color: white; }
        .status-shipped { background-color: #007bff; color: white; }
        .status-delivered { background-color: #28a745; color: white; }
        .status-cancelled { background-color: #dc3545; color: white; }
        .status-refunded { background-color: #6c757d; color: white; }
        .status-failed { background-color: #dc3545; color: white; }
        .order-items {
            background-color: #f8f9fa;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .item {
            border-bottom: 1px solid #dee2e6;
            padding: 10px 0;
        }
        .item:last-child {
            border-bottom: none;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 12px;
        }
        .status-timeline {
            background-color: #f0f8ff;
            border-left: 4px solid #dc3545;
            padding: 15px;
            margin: 15px 0;
        }
        .admin-actions {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="admin-badge">🔧 CÓPIA ADMINISTRATIVA</div>
            <h1>📦 Status do Pedido Atualizado</h1>
            <p>Notificação para administradores</p>
        </div>

        <div class="status-update">
            🔄 Status do pedido #{{ $order->id }} foi atualizado
        </div>

        <div class="user-info">
            <h3>👤 Informações do Cliente</h3>
            <p><strong>Nome:</strong> {{ $user->name }}</p>
            <p><strong>Email:</strong> {{ $user->email }}</p>
            <p><strong>ID do Usuário:</strong> #{{ $user->id }}</p>
            @if($user->phone)
                <p><strong>Telefone:</strong> {{ $user->phone }}</p>
            @endif
        </div>

        <div class="info-box">
            <h3>📋 Detalhes do Pedido</h3>
            <p><strong>Número do Pedido:</strong> #{{ $order->id }}</p>
            <p><strong>Data do Pedido:</strong> {{ $order->created_at->format('d/m/Y H:i:s') }}</p>
            <p><strong>Valor Total:</strong> R$ {{ number_format($order->total, 2, ',', '.') }}</p>
            @if($order->payment_method)
                <p><strong>Método de Pagamento:</strong> {{ $order->payment_method }}</p>
            @endif
            @if($order->payment_status)
                <p><strong>Status do Pagamento:</strong> {{ $order->payment_status }}</p>
            @endif
        </div>

        <div class="status-timeline">
            <h3>📊 Mudança de Status</h3>
            <p>
                <strong>Status Anterior:</strong> 
                <span class="status-badge status-{{ $oldStatus }}">
                    @switch($oldStatus)
                        @case('pending') Pendente @break
                        @case('processing') Processando @break
                        @case('completed') Concluído @break
                        @case('shipped') Enviado @break
                        @case('delivered') Entregue @break
                        @case('cancelled') Cancelado @break
                        @case('refunded') Reembolsado @break
                        @case('failed') Falhou @break
                        @default {{ ucfirst($oldStatus) }}
                    @endswitch
                </span>
            </p>
            <p style="text-align: center; font-size: 24px; margin: 10px 0;">⬇️</p>
            <p>
                <strong>Status Atual:</strong> 
                <span class="status-badge status-{{ $newStatus }}">
                    @switch($newStatus)
                        @case('pending') Pendente @break
                        @case('processing') Processando @break
                        @case('completed') Concluído @break
                        @case('shipped') Enviado @break
                        @case('delivered') Entregue @break
                        @case('cancelled') Cancelado @break
                        @case('refunded') Reembolsado @break
                        @case('failed') Falhou @break
                        @default {{ ucfirst($newStatus) }}
                    @endswitch
                </span>
            </p>
            <p><strong>Data da Mudança:</strong> {{ now()->format('d/m/Y H:i:s') }}</p>
        </div>

        @if($order->items && $order->items->count() > 0)
        <div class="info-box">
            <h3>🛍️ Itens do Pedido</h3>
            <div class="order-items">
                @foreach($order->items as $item)
                <div class="item">
                    <strong>{{ $item->product->name ?? 'Produto' }}</strong><br>
                    <small>ID do Produto: #{{ $item->product_id ?? 'N/A' }}</small><br>
                    Quantidade: {{ $item->quantity }} | 
                    Preço Unitário: R$ {{ number_format($item->price, 2, ',', '.') }} |
                    Subtotal: R$ {{ number_format($item->quantity * $item->price, 2, ',', '.') }}
                </div>
                @endforeach
            </div>
        </div>
        @endif

        @if($order->shipping_address)
        <div class="info-box">
            <h3>📍 Endereço de Entrega</h3>
            @if(is_array($order->shipping_address))
                @foreach($order->shipping_address as $key => $value)
                    <p><strong>{{ ucfirst($key) }}:</strong> {{ $value }}</p>
                @endforeach
            @else
                <p>{{ $order->shipping_address }}</p>
            @endif
        </div>
        @endif

        <div class="admin-actions">
            <h3>⚡ Ações Administrativas</h3>
            <p>• Verificar se o cliente foi notificado por email</p>
            <p>• Acompanhar o progresso do pedido</p>
            @if($newStatus === 'shipped')
                <p>• Confirmar código de rastreamento se aplicável</p>
            @endif
            @if($newStatus === 'cancelled' || $newStatus === 'refunded')
                <p>• Verificar motivo do cancelamento/reembolso</p>
                <p>• Processar reembolso se necessário</p>
            @endif
            @if($newStatus === 'failed')
                <p>• Investigar causa da falha</p>
                <p>• Entrar em contato com o cliente se necessário</p>
            @endif
        </div>

        <div class="footer">
            <p><strong>{{ config('app.name') }} - Sistema Administrativo</strong></p>
            <p>Esta é uma cópia automática para administradores.</p>
            <p>Destinatário: {{ $adminRecipient->name ?? 'Administração' }} ({{ $adminRecipient->email ?? '<EMAIL>' }})</p>
            <p>Data: {{ now()->format('d/m/Y H:i:s') }}</p>
        </div>
    </div>
</body>
</html>
