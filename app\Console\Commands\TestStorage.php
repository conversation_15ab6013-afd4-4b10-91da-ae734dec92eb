<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;

class TestStorage extends Command
{
    protected $signature = 'storage:test';
    protected $description = 'Testa configuração de storage e URLs';

    public function handle()
    {
        $this->info('🔍 Testando configuração de storage...');
        $this->newLine();

        // 1. Verificar configuração
        $this->testConfiguration();
        
        // 2. Verificar link simbólico
        $this->testSymbolicLink();
        
        // 3. Verificar arquivos
        $this->testFiles();
        
        // 4. Testar URLs
        $this->testUrls();
        
        // 5. Soluções
        $this->provideSolutions();
    }

    private function testConfiguration()
    {
        $this->info('⚙️ Configuração:');

        $appUrl = config('app.url');
        $publicDiskUrl = config('filesystems.disks.public.url');
        $livewireDisk = config('livewire.temporary_file_upload.disk');

        $this->line("🌐 APP_URL: {$appUrl}");
        $this->line("💾 Public Disk URL: {$publicDiskUrl}");
        $this->line("⚡ Livewire Temp Disk: {$livewireDisk}");

        if ($appUrl && $publicDiskUrl) {
            $this->line("✅ Configuração OK");
        } else {
            $this->line("❌ Configuração com problema");
        }

        if ($livewireDisk === 'public') {
            $this->line("✅ Livewire usando disk público");
        } else {
            $this->line("⚠️  Livewire usando disk privado - pode causar 404 em previews");
        }

        $this->newLine();
    }

    private function testSymbolicLink()
    {
        $this->info('🔗 Link Simbólico:');
        
        $publicStorage = public_path('storage');
        
        if (is_link($publicStorage)) {
            $target = readlink($publicStorage);
            $this->line("✅ Link existe: {$publicStorage} -> {$target}");
            
            if (is_dir($publicStorage)) {
                $this->line("✅ Link funciona");
            } else {
                $this->line("❌ Link não funciona");
            }
        } else {
            $this->line("❌ Link não existe");
        }
        
        $this->newLine();
    }

    private function testFiles()
    {
        $this->info('📁 Diretórios de Storage:');

        $directories = [
            'posts/videos' => storage_path('app/public/posts/videos'),
            'posts/images' => storage_path('app/public/posts/images'),
            'livewire-tmp' => storage_path('app/public/livewire-tmp'),
        ];

        foreach ($directories as $name => $path) {
            if (is_dir($path)) {
                $files = File::files($path);
                $this->line("✅ {$name}: " . count($files) . " arquivos");
            } else {
                $this->line("❌ {$name}: Diretório não existe");
            }
        }

        $this->newLine();

        // Testar os vídeos específicos do erro
        $this->info('🎬 Vídeos Específicos:');
        $videoPath = storage_path('app/public/posts/videos');
        $testFiles = [
            '1753090219_687e08abd44b1.mp4',
            '1752496395_lv_7255523106410777861_20250707100143.mp4'
        ];

        foreach ($testFiles as $filename) {
            $filePath = $videoPath . '/' . $filename;
            if (file_exists($filePath)) {
                $size = $this->formatBytes(filesize($filePath));
                $this->line("✅ {$filename} ({$size})");
            } else {
                $this->line("❌ {$filename} - não encontrado");
            }
        }

        $this->newLine();
    }

    private function testUrls()
    {
        $this->info('🌐 URLs Geradas:');
        
        $testPaths = [
            'posts/videos/1753090219_687e08abd44b1.mp4',
            'posts/videos/1752496395_lv_7255523106410777861_20250707100143.mp4'
        ];
        
        foreach ($testPaths as $path) {
            $url = Storage::url($path);
            $this->line("📁 {$path}");
            $this->line("   URL: {$url}");
            
            if (str_starts_with($url, 'https://')) {
                $this->line("   ✅ URL absoluta");
            } else {
                $this->line("   ❌ URL relativa");
            }
        }
        
        $this->newLine();
    }

    private function provideSolutions()
    {
        $this->info('🔧 Comandos para Corrigir:');
        $this->newLine();
        
        $this->line('1. Recriar link simbólico:');
        $this->line('   rm -f public/storage');
        $this->line('   php artisan storage:link');
        $this->newLine();
        
        $this->line('2. Corrigir permissões:');
        $this->line('   chmod -R 755 storage/');
        $this->line('   chmod -R 755 public/storage/');
        $this->newLine();
        
        $this->line('3. Testar acesso direto:');
        $this->line('   curl -I https://www.swingcuritiba.com.br/storage/posts/videos/1753090219_687e08abd44b1.mp4');
        $this->newLine();
        
        $this->line('4. Limpar cache:');
        $this->line('   php artisan config:clear');
        $this->newLine();
    }

    private function formatBytes($size, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
            $size /= 1024;
        }
        
        return round($size, $precision) . ' ' . $units[$i];
    }
}
