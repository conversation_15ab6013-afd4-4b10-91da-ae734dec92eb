<?php

namespace App\Livewire\Groups;

use App\Models\Group;
use App\Models\UserPoint;
use App\Models\Notification;
use App\Models\User;
use App\Notifications\GroupJoinRequestReceived;
use App\Notifications\GroupJoinRequestConfirmation;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Livewire\Component;
use Livewire\WithPagination;

class GroupDetail extends Component
{
    use WithPagination;

    public $group;
    public $tab = 'posts';
    public $showJoinConfirmation = false;
    public $showLeaveConfirmation = false;

    public function mount(Group $group)
    {
        $this->group = $group;

        // Check if the user can view this group
        if ($this->group->privacy === 'secret' && !Auth::check()) {
            abort(404);
        }

        if ($this->group->privacy === 'secret' && Auth::check() && !Auth::user()->isMemberOf($this->group)) {
            abort(403, 'Você não tem permissão para visualizar este grupo.');
        }
    }

    public function hasPendingRequest()
    {
        if (!Auth::check()) {
            return false;
        }

        return Auth::user()->groups()
            ->where('group_id', $this->group->id)
            ->wherePivot('is_approved', false)
            ->exists();
    }

    public function changeTab($tab)
    {
        $this->tab = $tab;
        $this->resetPage();
    }

    public function confirmJoin()
    {
        $this->showJoinConfirmation = true;
    }

    public function confirmLeave()
    {
        $this->showLeaveConfirmation = true;
    }

    public function join()
    {
        try {
            $user = Auth::user();

            // Check if the user is already a member
            if ($user->isMemberOf($this->group)) {
                session()->flash('info', 'Você já é membro deste grupo.');
                $this->showJoinConfirmation = false;
                return;
            }

            // Check if the group is private and requires approval
            if ($this->group->privacy === 'private') {
                $user->groups()->attach($this->group->id, [
                    'role' => 'member',
                    'is_approved' => false,
                    'joined_at' => now(),
                ]);

                // Create notification for group admins and send emails
                $groupAdmins = $this->group->admins;
                foreach ($groupAdmins as $admin) {
                    Notification::create([
                        'user_id' => $admin->id,
                        'sender_id' => $user->id,
                        'type' => 'group_join_request',
                        'group_id' => $this->group->id,
                        'message' => $user->name . ' solicitou entrada no grupo ' . $this->group->name . '.'
                    ]);

                    // Send email notification to admin
                    try {
                        $admin->notify(new GroupJoinRequestReceived($this->group, $user));
                    } catch (\Exception $e) {
                        \Log::error('Failed to send group join request email to admin ' . $admin->id . ': ' . $e->getMessage());
                    }
                }

                // Send confirmation email to user
                try {
                    $user->notify(new GroupJoinRequestConfirmation($this->group));
                } catch (\Exception $e) {
                    \Log::error('Failed to send group join request confirmation email to user ' . $user->id . ': ' . $e->getMessage());
                }

                // Send copy to admin users and contact email
                $this->sendCopyToAdmins($this->group, $user);

                session()->flash('info', 'Sua solicitação para entrar no grupo foi enviada e está aguardando aprovação.');
                $this->showJoinConfirmation = false;
                return;
            }

            // For public groups, join immediately
            $user->groups()->attach($this->group->id, [
                'role' => 'member',
                'is_approved' => true,
                'joined_at' => now(),
            ]);

            // Increment the members count
            $this->group->increment('members_count');

            // Add points for joining a group
            try {
                UserPoint::addPoints(
                    $user->id,
                    'group_joined',
                    5,
                    "Entrou no grupo: {$this->group->name}",
                    $this->group->id,
                    Group::class
                );
            } catch (\Exception $e) {
                \Log::error('Erro ao adicionar pontos para entrada no grupo', [
                    'user_id' => $user->id,
                    'group_id' => $this->group->id,
                    'error' => $e->getMessage()
                ]);
                // Continue mesmo se falhar ao adicionar pontos
            }

            session()->flash('success', 'Você entrou no grupo com sucesso!');
            $this->showJoinConfirmation = false;

            // Refresh the group data
            $this->group = Group::find($this->group->id);
        } catch (\Exception $e) {
            \Log::error('Erro ao entrar no grupo', [
                'user_id' => Auth::id(),
                'group_id' => $this->group->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            session()->flash('error', 'Erro ao entrar no grupo. Tente novamente.');
            $this->showJoinConfirmation = false;
        }
    }

    public function leave()
    {
        try {
            $user = Auth::user();

            // Check if the user is a member
            if (!$user->isMemberOf($this->group)) {
                session()->flash('error', 'Você não é membro deste grupo.');
                $this->showLeaveConfirmation = false;
                return;
            }

            // Check if the user is the creator
            if ($this->group->creator_id === $user->id) {
                session()->flash('error', 'Você é o criador deste grupo e não pode sair. Transfira a propriedade para outro membro ou exclua o grupo.');
                $this->showLeaveConfirmation = false;
                return;
            }

            // Remove the user from the group
            $user->groups()->detach($this->group->id);

            // Decrement the members count
            $this->group->decrement('members_count');

            session()->flash('success', 'Você saiu do grupo com sucesso!');
            $this->showLeaveConfirmation = false;

            // Redirect to groups index
            return redirect()->route('grupos.index');
        } catch (\Exception $e) {
            \Log::error('Erro ao sair do grupo', [
                'user_id' => Auth::id(),
                'group_id' => $this->group->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            session()->flash('error', 'Erro ao sair do grupo. Tente novamente.');
            $this->showLeaveConfirmation = false;
        }
    }

    /**
     * Send copy of group join request to system admins and contact email
     */
    protected function sendCopyToAdmins($group, $requestingUser)
    {
        // Get admin users
        $adminUsers = User::where('role', 'admin')->get();

        // Send to admin users (simplified notification)
        foreach ($adminUsers as $admin) {
            try {
                Notification::create([
                    'user_id' => $admin->id,
                    'sender_id' => $requestingUser->id,
                    'type' => 'group_join_request_admin_copy',
                    'group_id' => $group->id,
                    'message' => '[ADMIN COPY] ' . $requestingUser->name . ' solicitou entrada no grupo ' . $group->name . '.'
                ]);
            } catch (\Exception $e) {
                \Log::error('Failed to send admin notification copy to user ' . $admin->id . ': ' . $e->getMessage());
            }
        }
    }

    public function render()
    {
        $isMember = Auth::check() ? Auth::user()->isMemberOf($this->group) : false;
        $isAdmin = Auth::check() ? Auth::user()->isAdminOf($this->group) : false;
        $isModerator = Auth::check() ? Auth::user()->isModeratorOf($this->group) : false;

        $posts = $this->group->posts()
            ->with(['user', 'likes', 'comments'])
            ->latest()
            ->paginate(10);

        $members = $this->group->members()
            ->take(8)
            ->get();

        return view('livewire.groups.group-detail', [
            'posts' => $posts,
            'members' => $members,
            'isMember' => $isMember,
            'isAdmin' => $isAdmin,
            'isModerator' => $isModerator,
        ]);
    }
}
