<?php

namespace App\Observers;

use App\Models\User;
use App\Models\Achievement;
use App\Models\UserPoint;

class UserObserver
{
    /**
     * Handle the User "created" event.
     */
    public function created(User $user): void
    {
        //
    }

    /**
     * Handle the User "updated" event.
     */
    public function updated(User $user): void
    {
        //
    }

    /**
     * Handle the User "deleted" event.
     */
    public function deleted(User $user): void
    {
        //
    }

    /**
     * Handle the User "restored" event.
     */
    public function restored(User $user): void
    {
        //
    }

    /**
     * Handle the User "force deleted" event.
     */
    public function forceDeleted(User $user): void
    {
        //
    }

    /**
     * Handle the User "pivotAttached" event.
     * Concede conquistas baseadas em relacionamentos muitos-para-muitos.
     */
    // REMOVIDO: Método pivotAttached incorreto, lógica movida para listener em EventServiceProvider
    // public function pivotAttached(User $user, $relationName, $pivotIds, $pivotAttributes)
    // {
    //     // Verifica se o pivot foi anexado ao relacionamento 'followers'
    //     if ($relationName === 'followers') {
    //         // $user é o modelo User que ganhou o seguidor (o 'following')
    //         // $pivotIds contém o(s) ID(s) do(s) follower(s) que foram anexados

    //         // Verificar se a conquista 'Influenciador Inicial' existe
    //         $influencerAchievement = Achievement::where('name', 'Influenciador Inicial')->first();

    //         // TODO: Obter o limiar de seguidores da configuração da conquista (precisa de coluna na tabela achievements)
    //         // Por enquanto, usando um valor fixo
    //         $followersThreshold = 50; // Exemplo de limiar de seguidores

    //         // Recarregar o relacionamento followers para obter a contagem atualizada
    //         $user->load('followers');
    //         $followersCount = $user->followers->count();

    //         if ($influencerAchievement && $followersCount >= $followersThreshold) {
    //             // Verificar se o usuário já tem a conquista
    //             if (!$user->achievements->contains($influencerAchievement->id)) {
    //                 // Conceder a conquista
    //                 $user->achievements()->attach($influencerAchievement->id);

    //                 // Adicionar os pontos da conquista
    //                 UserPoint::addPoints(
    //                     $user->id,
    //                     'achievement',
    //                     $influencerAchievement->points,
    //                     "Ganhou a conquista \"" . $influencerAchievement->name . "\"",
    //                     $influencerAchievement->id,
    //                     Achievement::class
    //                 );

    //                 // Opcional: disparar evento para notificar o usuário no frontend
    //                 // $user->notify(new AchievementUnlocked($influencerAchievement));
    //             }
    //         }
    //     }

    //     // TODO: Adicionar lógica para outras conquistas baseadas em pivots, se necessário
    //     // Ex: participação em eventos (event_attendees), entrada em grupos (group_user)
    //     if ($relationName === 'attendingEvents') {
    //          // Lógica para conquista de eventos
    //     }

    //     if ($relationName === 'groups') {
    //          // Lógica para conquista de grupos
    //     }
    // }

    // TODO: Considerar adicionar lógica para pivotDetached se remover seguidores ou sair de grupos puder afetar conquistas/níveis

}
