<?php

namespace App\Http\Controllers;

use App\Models\JobVacancy;
use App\Models\JobApplication;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use App\Notifications\JobApplicationSubmitted;
use App\Notifications\JobApplicationConfirmation;

class JobApplicationController extends Controller
{
    /**
     * Aplicar para uma vaga
     */
    public function store(Request $request, $jobId)
    {
        // Verificar se o usuário está autenticado
        if (!Auth::check()) {
            return redirect()->route('login')->with('error', 'Você precisa estar logado para se candidatar.');
        }

        $job = JobVacancy::active()->findOrFail($jobId);

        // Verificar se a vaga ainda está aceitando candidaturas
        if (!$job->isAcceptingApplications()) {
            return back()->with('error', 'Esta vaga não está mais aceitando candidaturas.');
        }

        // Verificar se o usuário já se candidatou
        if ($job->hasUserApplied(Auth::id())) {
            return back()->with('error', 'Você já se candidatou para esta vaga.');
        }

        // Validação aprimorada
        $rules = [
            'name' => 'required|string|min:2|max:100',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|min:10|max:20',
            'experience' => 'nullable|string|max:1000',
        ];

        if ($job->requires_resume) {
            $rules['resume'] = 'required|file|mimes:pdf,doc,docx|max:2048';
        }

        if ($job->requires_cover_letter) {
            $rules['cover_letter'] = 'required|string|min:100|max:2000';
        } else {
            $rules['cover_letter'] = 'nullable|string|max:2000';
        }

        $validator = Validator::make($request->all(), $rules, [
            'name.required' => 'O nome é obrigatório.',
            'name.min' => 'O nome deve ter pelo menos 2 caracteres.',
            'name.max' => 'O nome não pode ter mais de 100 caracteres.',
            'email.required' => 'O email é obrigatório.',
            'email.email' => 'Digite um email válido.',
            'phone.required' => 'O telefone é obrigatório.',
            'phone.min' => 'O telefone deve ter pelo menos 10 dígitos.',
            'phone.max' => 'O telefone não pode ter mais de 20 caracteres.',
            'experience.max' => 'A experiência não pode ter mais de 1000 caracteres.',
            'resume.required' => 'O currículo é obrigatório para esta vaga.',
            'resume.file' => 'O arquivo deve ser válido.',
            'resume.mimes' => 'O currículo deve ser um arquivo PDF, DOC ou DOCX.',
            'resume.max' => 'O currículo não pode ter mais de 2MB.',
            'cover_letter.required' => 'A carta de apresentação é obrigatória para esta vaga.',
            'cover_letter.min' => 'A carta de apresentação deve ter pelo menos 100 caracteres.',
            'cover_letter.max' => 'A carta de apresentação não pode ter mais de 2000 caracteres.',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // Processar upload do currículo
        $resumePath = null;
        $resumeFilename = null;
        
        if ($request->hasFile('resume')) {
            $file = $request->file('resume');
            $filename = 'resume_' . Auth::id() . '_' . time() . '.' . $file->getClientOriginalExtension();
            $resumePath = $file->storeAs('job-applications', $filename, 'private');
            $resumeFilename = $file->getClientOriginalName();
        }

        // Criar candidatura com dados de auditoria
        $application = JobApplication::create([
            'job_id' => $job->id,
            'job_vacancy_id' => $job->id, // Para compatibilidade com a migration
            'user_id' => Auth::id(),
            'cover_letter' => $request->cover_letter,
            'resume_path' => $resumePath,
            'resume_filename' => $resumeFilename,
            'is_vip_priority' => Auth::user()->role === 'vip',
            // Dados de auditoria
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'form_data' => [
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
                'experience' => $request->experience,
                'cover_letter_length' => strlen($request->cover_letter ?? ''),
                'has_resume' => !is_null($resumePath),
                'submitted_at' => now()->toISOString(),
                'browser_info' => [
                    'user_agent' => $request->userAgent(),
                    'ip' => $request->ip(),
                    'referer' => $request->header('referer'),
                ],
            ],
            // Dados adicionais do candidato
            'candidate_name' => $request->name,
            'candidate_email' => $request->email,
            'candidate_phone' => $request->phone,
            'candidate_experience' => $request->experience,
            // Prioridade baseada no tipo de usuário
            'priority_level' => Auth::user()->role === 'vip' ? 'alta' : 'normal',
        ]);

        // Incrementar contador de candidaturas da vaga
        $job->increment('applications_count');

        // Enviar notificações por email
        $this->sendNotificationEmails($application);

        // Log da candidatura
        Log::info('Nova candidatura enviada', [
            'user_id' => Auth::id(),
            'job_id' => $job->id,
            'application_id' => $application->id,
            'is_vip' => Auth::user()->role === 'vip',
            'user_email' => Auth::user()->email,
            'job_title' => $job->title,
        ]);

        return back()->with('success', 'Candidatura enviada com sucesso! ' .
            (Auth::user()->role === 'vip' ? 'Sua candidatura VIP terá prioridade na análise.' : ''));
    }

    /**
     * Exibir candidatura do usuário
     */
    public function show($applicationId)
    {
        $application = JobApplication::with(['job', 'user'])
            ->where('user_id', Auth::id())
            ->findOrFail($applicationId);

        return view('job-applications.show', compact('application'));
    }

    /**
     * Cancelar candidatura
     */
    public function destroy($applicationId)
    {
        $application = JobApplication::where('user_id', Auth::id())
            ->findOrFail($applicationId);

        // Verificar se pode cancelar (apenas se ainda estiver pendente)
        if ($application->status !== 'Pendente') {
            return back()->with('error', 'Não é possível cancelar uma candidatura que já foi analisada.');
        }

        // Remover arquivo do currículo se existir
        if ($application->resume_path) {
            Storage::disk('private')->delete($application->resume_path);
        }

        // Decrementar contador de candidaturas da vaga
        $application->job->decrement('applications_count');

        $application->delete();

        return back()->with('success', 'Candidatura cancelada com sucesso.');
    }

    /**
     * Download do currículo (apenas para admin ou próprio usuário)
     */
    public function downloadResume($applicationId)
    {
        $application = JobApplication::with('user')->findOrFail($applicationId);

        // Verificar permissão
        if (!Auth::user()->isAdmin() && Auth::id() !== $application->user_id) {
            abort(403, 'Acesso negado.');
        }

        if (!$application->resume_path) {
            abort(404, 'Currículo não encontrado.');
        }

        $path = Storage::disk('private')->path($application->resume_path);
        
        return response()->download($path, $application->resume_filename);
    }

    /**
     * Listar candidaturas do usuário
     */
    public function myApplications()
    {
        $applications = JobApplication::with(['job.category'])
            ->where('user_id', Auth::id())
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('job-applications.my-applications', compact('applications'));
    }

    /**
     * Enviar notificações por email sobre nova candidatura
     */
    private function sendNotificationEmails($application)
    {
        try {
            // Carregar relacionamentos necessários
            $application->load(['job.category', 'user']);

            // Enviar notificação para administradores
            $adminUsers = User::where('role', 'administrador')->get();
            foreach ($adminUsers as $admin) {
                try {
                    $admin->notify(new JobApplicationSubmitted($application));
                    Log::info('Email de candidatura enviado para admin', [
                        'admin_id' => $admin->id,
                        'admin_email' => $admin->email,
                        'application_id' => $application->id,
                    ]);
                } catch (\Exception $e) {
                    Log::error('Erro ao enviar email para admin', [
                        'admin_id' => $admin->id,
                        'admin_email' => $admin->email,
                        'application_id' => $application->id,
                        'error' => $e->getMessage(),
                    ]);
                }
            }

            // Enviar confirmação para o candidato
            try {
                $application->user->notify(new JobApplicationConfirmation($application));
                Log::info('Email de confirmação enviado para candidato', [
                    'user_id' => $application->user->id,
                    'user_email' => $application->user->email,
                    'application_id' => $application->id,
                ]);
            } catch (\Exception $e) {
                Log::error('Erro ao enviar email de confirmação para candidato', [
                    'user_id' => $application->user->id,
                    'user_email' => $application->user->email,
                    'application_id' => $application->id,
                    'error' => $e->getMessage(),
                ]);
            }

            // Enviar cópia para email de contato
            try {
                $contactEmail = '<EMAIL>';
                Notification::route('mail', $contactEmail)
                    ->notify(new JobApplicationSubmitted($application));
                Log::info('Email de candidatura enviado para contato', [
                    'contact_email' => $contactEmail,
                    'application_id' => $application->id,
                ]);
            } catch (\Exception $e) {
                Log::error('Erro ao enviar email para contato', [
                    'contact_email' => '<EMAIL>',
                    'application_id' => $application->id,
                    'error' => $e->getMessage(),
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Erro geral ao enviar emails de candidatura', [
                'application_id' => $application->id,
                'error' => $e->getMessage(),
            ]);
        }
    }
}
