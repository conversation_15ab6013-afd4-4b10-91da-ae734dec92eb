<?php

namespace App\Http\Controllers\Shop;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Cart;
use App\Services\VipPurchaseService;
use App\Services\WalletCreditService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Stripe\Stripe;
use Stripe\Checkout\Session;

class OrderController extends Controller
{
    protected $vipPurchaseService;
    protected $walletCreditService;

    public function __construct(VipPurchaseService $vipPurchaseService, WalletCreditService $walletCreditService)
    {
        $this->vipPurchaseService = $vipPurchaseService;
        $this->walletCreditService = $walletCreditService;
    }
    /**
     * Mostra a página de sucesso do pedido.
     */
    public function showSuccess(Order $order)
    {
        // Garante que o usuário só possa ver seus próprios pedidos
        if ($order->user_id !== Auth::id()) {
            abort(404);
        }

        return view('shop.order-success', compact('order'));
    }

    /**
     * Mostra os detalhes de um pedido.
     */
    public function show(Order $order)
    {
        // Garante que o usuário só possa ver seus próprios pedidos
        if ($order->user_id !== Auth::id()) {
            abort(403, "Você não tem permissão para ver este pedido.");
        }

        return view('shop.order-show', compact('order'));
    }

    /**
     * Lida com o retorno bem-sucedido do Stripe.
     */
    public function paymentSuccess(Request $request, Order $order)
    {
        if ($order->user_id !== Auth::id()) {
            abort(404);
        }

        if ($order->isPaid()) {
            return redirect()->route('shop.order.success', $order)
                ->with('info', 'O pagamento para este pedido já foi confirmado.');
        }

        try {
            Stripe::setApiKey(config('cashier.secret'));
            $sessionId = $request->get('session_id');

            if (!$sessionId) {
                return redirect()->route('shop.order.show', $order)->with('error', 'ID da sessão de pagamento não encontrado.');
            }

            $session = Session::retrieve($sessionId);

            if ($session->metadata->order_id != $order->id) {
                return redirect()->route('shop.order.show', $order)->with('error', 'Sessão de pagamento inválida.');
            }

            if ($session->payment_status == 'paid') {
                $order->markAsPaid('stripe', $session->id);

                // Process VIP purchase if order contains VIP products
                if ($this->vipPurchaseService->orderContainsVipProducts($order)) {
                    $vipProcessed = $this->vipPurchaseService->processVipPurchase($order);

                    if ($vipProcessed) {
                        Log::info('VIP purchase processed successfully for order', ['order_id' => $order->id]);
                    } else {
                        Log::error('Failed to process VIP purchase for order', ['order_id' => $order->id]);
                        // Continue with normal flow even if VIP processing fails
                    }
                }

                // Process wallet credit if order contains wallet credit products
                if ($this->walletCreditService->orderContainsWalletCreditProducts($order)) {
                    $walletCreditProcessed = $this->walletCreditService->processWalletCredit($order);

                    if ($walletCreditProcessed) {
                        Log::info('Wallet credit processed successfully for order', ['order_id' => $order->id]);
                    } else {
                        Log::error('Failed to process wallet credit for order', ['order_id' => $order->id]);
                        // Continue with normal flow even if wallet credit processing fails
                    }
                }

                $cart = Cart::where('user_id', Auth::id())->first();
                if ($cart) {
                    $cart->clear();
                }

                // Check if VIP was activated or wallet credit was added to show appropriate message
                $successMessage = 'Pagamento confirmado com sucesso!';
                $hasVipProducts = $this->vipPurchaseService->orderContainsVipProducts($order);
                $hasWalletCreditProducts = $this->walletCreditService->orderContainsWalletCreditProducts($order);

                if ($hasVipProducts && $hasWalletCreditProducts) {
                    $successMessage = 'Pagamento confirmado! Acesso VIP ativado e crédito adicionado à carteira! 🎉';
                } elseif ($hasVipProducts) {
                    $successMessage = 'Pagamento confirmado e acesso VIP ativado com sucesso! 🎉';
                } elseif ($hasWalletCreditProducts) {
                    $successMessage = 'Pagamento confirmado e crédito adicionado à carteira! 💰';
                }

                return redirect()->route('shop.order.success', $order)->with('success', $successMessage);
            }

            return redirect()->route('shop.order.show', $order)->with('error', 'O pagamento não foi concluído. Tente novamente.');
        } catch (\Exception $e) {
            logger()->error('Erro ao verificar sessão do Stripe: ' . $e->getMessage(), ['order_id' => $order->id]);
            return redirect()->route('shop.order.show', $order)->with('error', 'Ocorreu um erro ao verificar seu pagamento. Entre em contato com o suporte.');
        }
    }

    /**
     * Lida com o cancelamento do pagamento no Stripe.
     */
    public function paymentCancel(Request $request, Order $order)
    {
        if ($order->user_id !== Auth::id()) {
            abort(404);
        }

        $order->update(['status' => Order::STATUS_FAILED]);

        return redirect()->route('shop.order.show', $order)->with('info', 'O processo de pagamento foi cancelado. Você pode tentar novamente a partir desta página.');
    }
}
