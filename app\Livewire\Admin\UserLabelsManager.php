<?php

namespace App\Livewire\Admin;

use App\Models\User;
use App\Models\UserLabel;
use App\Helpers\LabelHelper;
use Livewire\Component;
use Livewire\WithPagination;

class UserLabelsManager extends Component
{
    use WithPagination;

    public $search = '';
    public $selectedUser = null;
    public $showModal = false;

    // Form fields
    public $selectedLabelName = '';
    public $expiresAt = '';
    public $editingLabel = null;

    protected $rules = [
        'selectedLabelName' => 'required|string',
        'expiresAt' => 'nullable|date|after:now'
    ];

    public function selectUser($userId)
    {
        $this->selectedUser = User::with('activeLabels')->find($userId);
        $this->resetForm();
    }

    public function openModal()
    {
        $this->showModal = true;
        $this->resetForm();
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->resetForm();
    }

    public function resetForm()
    {
        $this->selectedLabelName = '';
        $this->expiresAt = '';
        $this->editingLabel = null;
        $this->resetValidation();
    }

    public function addLabel()
    {
        $this->validate();

        if (!$this->selectedUser) {
            session()->flash('error', 'Selecione um usuário primeiro.');
            return;
        }

        // Verificar se o usuário já possui esta label
        $existingLabel = $this->selectedUser->labels()
            ->where('name', $this->selectedLabelName)
            ->where('is_active', true)
            ->first();

        if ($existingLabel) {
            session()->flash('error', 'O usuário já possui esta label ativa.');
            return;
        }

        // Buscar dados da label pré-definida
        $labelData = LabelHelper::getLabelByName($this->selectedLabelName);

        if (!$labelData) {
            session()->flash('error', 'Label não encontrada.');
            return;
        }

        UserLabel::create([
            'user_id' => $this->selectedUser->id,
            'name' => $labelData['name'],
            'color' => $labelData['color'],
            'icon' => $labelData['icon'],
            'description' => $labelData['description'],
            'expires_at' => $this->expiresAt ? \Carbon\Carbon::parse($this->expiresAt) : null,
            'assigned_by' => auth()->id()
        ]);

        $this->selectedUser->refresh();
        $this->selectedUser->load('activeLabels');

        session()->flash('success', 'Label aplicada com sucesso!');
        $this->closeModal();
    }

    public function editLabel($labelId)
    {
        $this->editingLabel = UserLabel::find($labelId);

        if ($this->editingLabel) {
            $this->selectedLabelName = $this->editingLabel->name;
            $this->expiresAt = $this->editingLabel->expires_at ? $this->editingLabel->expires_at->format('Y-m-d\TH:i') : '';
            $this->showModal = true;
        }
    }

    public function updateLabel()
    {
        $this->validate();

        if ($this->editingLabel) {
            // Buscar dados da nova label selecionada
            $labelData = LabelHelper::getLabelByName($this->selectedLabelName);

            if (!$labelData) {
                session()->flash('error', 'Label não encontrada.');
                return;
            }

            $this->editingLabel->update([
                'name' => $labelData['name'],
                'color' => $labelData['color'],
                'icon' => $labelData['icon'],
                'description' => $labelData['description'],
                'expires_at' => $this->expiresAt ? \Carbon\Carbon::parse($this->expiresAt) : null,
            ]);

            $this->selectedUser->refresh();
            $this->selectedUser->load('activeLabels');

            session()->flash('success', 'Label atualizada com sucesso!');
            $this->closeModal();
        }
    }

    public function removeLabel($labelId)
    {
        $label = UserLabel::find($labelId);

        if ($label) {
            $label->delete();
            $this->selectedUser->refresh();
            $this->selectedUser->load('activeLabels');
            session()->flash('success', 'Label removida com sucesso!');
        }
    }

    public function toggleLabelStatus($labelId)
    {
        $label = UserLabel::find($labelId);

        if ($label) {
            $label->update(['is_active' => !$label->is_active]);
            $this->selectedUser->refresh();
            $this->selectedUser->load('activeLabels');
            session()->flash('success', 'Status da label alterado com sucesso!');
        }
    }

    public function render()
    {
        $users = User::where('name', 'like', '%' . $this->search . '%')
                    ->orWhere('username', 'like', '%' . $this->search . '%')
                    ->orWhere('email', 'like', '%' . $this->search . '%')
                    ->paginate(10);

        $availableLabels = LabelHelper::getAvailableLabels();

        return view('livewire.admin.user-labels-manager', [
            'users' => $users,
            'availableLabels' => $availableLabels
        ]);
    }
}
