<?php

namespace App\Livewire\PremiumAlbums;

use App\Models\PremiumAlbumSetting;
use App\Services\PremiumAlbumService;
use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Auth;

class AlbumCreator extends Component
{
    use WithFileUploads;

    public $name = '';
    public $description = '';
    public $price = '';
    public $preview_limit = 3;
    public $tags = '';
    public $files = [];
    
    public $isLoading = false;
    public $step = 1; // 1: Dados básicos, 2: Upload de arquivos, 3: Revisão

    protected $rules = [
        'name' => 'required|string|max:255',
        'description' => 'nullable|string|max:1000',
        'price' => 'required|numeric|min:5|max:500',
        'preview_limit' => 'required|integer|min:1|max:10',
        'tags' => 'nullable|string|max:500',
        'files.*' => 'file|max:51200|mimes:jpg,jpeg,png,gif,webp,mp4,mov,avi,webm',
    ];

    protected $messages = [
        'name.required' => 'Nome do álbum é obrigatório.',
        'price.required' => 'Preço é obrigatório.',
        'price.min' => 'Preço mínimo é R$ 5,00.',
        'price.max' => 'Preço máximo é R$ 500,00.',
        'files.*.max' => 'Cada arquivo deve ter no máximo 50MB.',
        'files.*.mimes' => 'Tipos permitidos: JPG, PNG, GIF, WebP, MP4, MOV, AVI, WebM.',
    ];

    public function mount()
    {
        // Verificar permissões
        if (Auth::user()->role === 'visitante') {
            session()->flash('error', 'Apenas usuários VIP podem criar álbuns premium.');
            return redirect()->route('premium-albums.marketplace');
        }

        // Configurar valores padrão
        $this->price = PremiumAlbumSetting::getMinPrice();
    }

    public function nextStep()
    {
        if ($this->step === 1) {
            $this->validateOnly('name');
            $this->validateOnly('description');
            $this->validateOnly('price');
            $this->validateOnly('preview_limit');
            $this->validateOnly('tags');
        }

        if ($this->step === 2) {
            if (empty($this->files)) {
                $this->addError('files', 'Adicione pelo menos um arquivo ao álbum.');
                return;
            }
        }

        $this->step++;
    }

    public function previousStep()
    {
        $this->step--;
    }

    public function updatedFiles()
    {
        $this->validateOnly('files.*');
        
        // Verificar limite de arquivos
        $maxFiles = PremiumAlbumSetting::getMaxFilesPerAlbum();
        if (count($this->files) > $maxFiles) {
            $this->addError('files', "Máximo de {$maxFiles} arquivos por álbum.");
            $this->files = array_slice($this->files, 0, $maxFiles);
        }
    }

    public function removeFile($index)
    {
        unset($this->files[$index]);
        $this->files = array_values($this->files);
    }

    public function createAlbum()
    {
        $this->validate();

        $this->isLoading = true;

        try {
            $albumService = new PremiumAlbumService();
            
            $data = [
                'name' => $this->name,
                'description' => $this->description,
                'price' => $this->price,
                'preview_limit' => $this->preview_limit,
                'tags' => $this->tags,
            ];

            $result = $albumService->createPremiumAlbum(Auth::user(), $data, $this->files);

            if ($result['success']) {
                session()->flash('success', $result['message']);
                return redirect()->route('premium-albums.my-albums');
            } else {
                session()->flash('error', 'Erro ao criar álbum.');
            }

        } catch (\Exception $e) {
            session()->flash('error', $e->getMessage());
        } finally {
            $this->isLoading = false;
        }
    }

    public function getFilePreview($file)
    {
        if (!$file) return null;
        
        $extension = strtolower($file->getClientOriginalExtension());
        $isImage = in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp']);
        
        return [
            'name' => $file->getClientOriginalName(),
            'size' => $this->formatFileSize($file->getSize()),
            'type' => $isImage ? 'image' : 'video',
            'url' => $isImage ? $file->temporaryUrl() : null,
        ];
    }

    private function formatFileSize($bytes)
    {
        if ($bytes >= 1073741824) {
            return number_format($bytes / 1073741824, 2) . ' GB';
        } elseif ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }

    public function render()
    {
        $settings = [
            'min_price' => PremiumAlbumSetting::getMinPrice(),
            'max_price' => PremiumAlbumSetting::getMaxPrice(),
            'max_files' => PremiumAlbumSetting::getMaxFilesPerAlbum(),
            'max_file_size' => PremiumAlbumSetting::getMaxFileSize(),
            'allowed_types' => PremiumAlbumSetting::getAllowedFileTypes(),
        ];

        return view('livewire.premium-albums.album-creator', compact('settings'));
    }
}
