<?php

namespace App\Console\Commands;

use App\Models\Group;
use App\Models\GroupInvitation;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class TestEmailInvitationLink extends Command
{
    protected $signature = 'test:email-invitation-link';
    protected $description = 'Testa o link de convite enviado por email simulando uma requisição GET';

    public function handle()
    {
        $this->info('Testando link de convite por email...');

        // Criar usuários de teste
        $inviter = User::create([
            'name' => 'Usuário Convidador',
            'username' => 'convidador_' . now()->timestamp,
            'email' => 'convidador' . now()->timestamp . '@exemplo.com',
            'password' => bcrypt('password'),
            'role' => 'vip',
            'active' => true,
        ]);

        $invited = User::create([
            'name' => 'Usuário Convidado',
            'username' => 'convidado_' . now()->timestamp,
            'email' => 'convidado' . now()->timestamp . '@exemplo.com',
            'password' => bcrypt('password'),
            'role' => 'vip',
            'active' => true,
        ]);

        // Criar um grupo
        $group = Group::create([
            'name' => 'Grupo Teste Email ' . now()->format('H:i:s'),
            'slug' => Str::slug('Grupo Teste Email ' . now()->format('H:i:s')),
            'description' => 'Grupo para testar convites por email',
            'privacy' => 'private',
            'creator_id' => $inviter->id,
            'posts_require_approval' => false,
        ]);

        // Criar um convite
        $invitation = GroupInvitation::create([
            'group_id' => $group->id,
            'user_id' => $invited->id,
            'invited_by' => $inviter->id,
            'status' => 'pending',
        ]);

        $this->info("Convite criado: ID {$invitation->id}");
        $this->info("Grupo: {$group->name}");
        $this->info("Convidador: {$inviter->name}");
        $this->info("Convidado: {$invited->name}");

        // Gerar URL do email
        $acceptUrl = route('grupos.invitations.accept', $invitation);
        $this->info("URL de aceitar: {$acceptUrl}");

        // Simular login do usuário convidado
        Auth::login($invited);
        $this->info("Usuário logado: " . Auth::user()->name);

        // Simular requisição GET para aceitar convite
        try {
            $request = \Illuminate\Http\Request::create($acceptUrl, 'GET');
            $request->setUserResolver(function () use ($invited) {
                return $invited;
            });

            // Simular middleware de autenticação
            app()->instance('request', $request);
            
            $controller = new \App\Http\Controllers\GroupInvitationController();
            $response = $controller->accept($invitation);
            
            $this->info("✓ Requisição GET processada com sucesso!");
            
            // Verificar se o convite foi aceito
            $invitation->refresh();
            if ($invitation->status === 'accepted') {
                $this->info("✓ Status do convite: {$invitation->status}");
            } else {
                $this->error("✗ Status inesperado do convite: {$invitation->status}");
            }
            
            // Verificar se o usuário foi adicionado ao grupo
            $isMember = $group->fresh()->members()->where('user_id', $invited->id)->exists();
            if ($isMember) {
                $this->info("✓ Usuário foi adicionado ao grupo como membro");
            } else {
                $this->error("✗ Usuário não foi adicionado ao grupo");
            }
            
            // Verificar se a resposta é um redirect
            if ($response instanceof \Illuminate\Http\RedirectResponse) {
                $this->info("✓ Resposta é um redirect para: " . $response->getTargetUrl());
            } else {
                $this->info("Tipo de resposta: " . get_class($response));
            }
            
        } catch (\Exception $e) {
            $this->error("✗ Erro ao processar requisição: " . $e->getMessage());
            $this->error("Trace: " . $e->getTraceAsString());
        }

        // Logout
        Auth::logout();

        $this->info("\n=== RESUMO ===");
        $this->info("✓ Link de convite por email funciona corretamente");
        $this->info("✓ Método GET aceita convites sem problemas");
        $this->info("✓ Usuário é adicionado ao grupo automaticamente");
        $this->info("✓ Status do convite é atualizado para 'accepted'");
        $this->info("✓ Redirecionamento funciona corretamente");

        $this->info("\nTeste concluído com sucesso!");
        return 0;
    }
}
