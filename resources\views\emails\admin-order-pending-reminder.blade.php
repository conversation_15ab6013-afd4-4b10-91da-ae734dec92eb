<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>[ADMIN] Pedido Pendente há {{ $daysPending }} dias</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f4f4f4;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #fff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #dc3545, #ffc107);
            color: white;
            padding: 30px;
            text-align: center;
            border-radius: 10px 10px 0 0;
            margin: -30px -30px 30px -30px;
        }
        .admin-badge {
            background-color: #dc3545;
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 20px;
        }
        .urgency-alert {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
            font-size: 18px;
            font-weight: bold;
        }
        .info-box {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }
        .info-box h3 {
            margin-top: 0;
            color: #dc3545;
        }
        .user-info {
            background-color: #e9ecef;
            border-left: 4px solid #dc3545;
            padding: 15px;
            margin: 15px 0;
        }
        .pending-badge {
            background-color: #ffc107;
            color: #333;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
            display: inline-block;
            margin: 10px 0;
        }
        .order-items {
            background-color: #f8f9fa;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .item {
            border-bottom: 1px solid #dee2e6;
            padding: 10px 0;
        }
        .item:last-child {
            border-bottom: none;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 12px;
        }
        .admin-actions {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .priority-high {
            background-color: #f8d7da;
            border-left: 4px solid #dc3545;
            padding: 15px;
            margin: 15px 0;
        }
        .priority-medium {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 15px 0;
        }
        .timeline-info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="admin-badge">🔧 CÓPIA ADMINISTRATIVA</div>
            <h1>⏰ Pedido Pendente - Ação Necessária</h1>
            <p>Alerta para administradores</p>
        </div>

        @if($daysPending >= 7)
        <div class="urgency-alert">
            🚨 ALTA PRIORIDADE: Pedido pendente há {{ $daysPending }} dias
        </div>
        @else
        <div class="priority-medium">
            ⚠️ Pedido pendente há {{ $daysPending }} {{ $daysPending == 1 ? 'dia' : 'dias' }} - Monitoramento necessário
        </div>
        @endif

        <div class="user-info">
            <h3>👤 Informações do Cliente</h3>
            <p><strong>Nome:</strong> {{ $user->name }}</p>
            <p><strong>Email:</strong> {{ $user->email }}</p>
            <p><strong>ID do Usuário:</strong> #{{ $user->id }}</p>
            @if($user->phone)
                <p><strong>Telefone:</strong> {{ $user->phone }}</p>
            @endif
            <p><strong>Data de Cadastro:</strong> {{ $user->created_at->format('d/m/Y') }}</p>
        </div>

        <div class="info-box">
            <h3>📋 Detalhes do Pedido</h3>
            <p><strong>Número do Pedido:</strong> #{{ $order->id }}</p>
            <p><strong>Data do Pedido:</strong> {{ $order->created_at->format('d/m/Y H:i:s') }}</p>
            <p><strong>Valor Total:</strong> R$ {{ number_format($order->total, 2, ',', '.') }}</p>
            <p><strong>Status:</strong> <span class="pending-badge">Pendente há {{ $daysPending }} {{ $daysPending == 1 ? 'dia' : 'dias' }}</span></p>
            @if($order->payment_method)
                <p><strong>Método de Pagamento:</strong> {{ $order->payment_method }}</p>
            @endif
            @if($order->payment_status)
                <p><strong>Status do Pagamento:</strong> {{ $order->payment_status }}</p>
            @endif
        </div>

        <div class="timeline-info">
            <h3>📊 Timeline do Pedido</h3>
            <p><strong>Criado em:</strong> {{ $order->created_at->format('d/m/Y H:i:s') }}</p>
            <p><strong>Tempo pendente:</strong> {{ $daysPending }} {{ $daysPending == 1 ? 'dia' : 'dias' }}</p>
            <p><strong>Última atualização:</strong> {{ $order->updated_at->format('d/m/Y H:i:s') }}</p>
            @if($daysPending >= 7)
                <p style="color: #dc3545;"><strong>⚠️ Risco de cancelamento automático</strong></p>
            @endif
        </div>

        @if($order->items && $order->items->count() > 0)
        <div class="info-box">
            <h3>🛍️ Itens do Pedido</h3>
            <div class="order-items">
                @foreach($order->items as $item)
                <div class="item">
                    <strong>{{ $item->product->name ?? 'Produto' }}</strong><br>
                    <small>ID do Produto: #{{ $item->product_id ?? 'N/A' }}</small><br>
                    Quantidade: {{ $item->quantity }} | 
                    Preço Unitário: R$ {{ number_format($item->price, 2, ',', '.') }} |
                    Subtotal: R$ {{ number_format($item->quantity * $item->price, 2, ',', '.') }}
                </div>
                @endforeach
            </div>
        </div>
        @endif

        @if($order->shipping_address)
        <div class="info-box">
            <h3>📍 Endereço de Entrega</h3>
            @if(is_array($order->shipping_address))
                @foreach($order->shipping_address as $key => $value)
                    <p><strong>{{ ucfirst($key) }}:</strong> {{ $value }}</p>
                @endforeach
            @else
                <p>{{ $order->shipping_address }}</p>
            @endif
        </div>
        @endif

        <div class="admin-actions">
            <h3>⚡ Ações Administrativas Recomendadas</h3>
            
            @if($daysPending >= 7)
            <div class="priority-high">
                <h4>🚨 AÇÕES URGENTES ({{ $daysPending }} dias):</h4>
                <ul>
                    <li>Entrar em contato imediato com o cliente</li>
                    <li>Verificar status do pagamento no gateway</li>
                    <li>Considerar cancelamento manual se necessário</li>
                    <li>Investigar possíveis problemas técnicos</li>
                    <li>Documentar motivo da pendência</li>
                </ul>
            </div>
            @elseif($daysPending >= 3)
            <div class="priority-medium">
                <h4>⚠️ AÇÕES RECOMENDADAS ({{ $daysPending }} dias):</h4>
                <ul>
                    <li>Verificar logs de pagamento</li>
                    <li>Enviar lembrete adicional ao cliente</li>
                    <li>Verificar se há problemas com o método de pagamento</li>
                    <li>Monitorar para possível escalação</li>
                </ul>
            </div>
            @else
            <ul>
                <li>Monitorar evolução do pedido</li>
                <li>Verificar se cliente recebeu notificação</li>
                <li>Aguardar ação do cliente por mais alguns dias</li>
            </ul>
            @endif
        </div>

        <div class="info-box">
            <h3>📈 Estatísticas de Contexto</h3>
            <p>• Cliente notificado automaticamente por email</p>
            <p>• Próxima verificação automática em 24 horas</p>
            @if($daysPending >= 7)
                <p style="color: #dc3545;">• <strong>Pedido em risco de cancelamento automático</strong></p>
            @endif
        </div>

        <div class="footer">
            <p><strong>{{ config('app.name') }} - Sistema Administrativo</strong></p>
            <p>Esta é uma cópia automática para administradores sobre pedidos pendentes.</p>
            <p>Destinatário: {{ $adminRecipient->name ?? 'Administração' }} ({{ $adminRecipient->email ?? '<EMAIL>' }})</p>
            <p>Data: {{ now()->format('d/m/Y H:i:s') }}</p>
            <p><small>Comando executado: orders:check-pending</small></p>
        </div>
    </div>
</body>
</html>
