<?php

namespace App\Livewire\Groups;

use App\Models\Group;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Livewire\Component;
use Livewire\WithFileUploads;

class CreateGroup extends Component
{
    use WithFileUploads;

    public $name = '';
    public $description = '';
    public $privacy = 'public';
    public $image;
    public $coverImage;
    public $postsRequireApproval = false;

    protected $rules = [
        'name' => 'required|string|min:3|max:255|unique:groups,name',
        'description' => 'nullable|string|max:1000',
        'privacy' => 'required|in:public,private,secret',
        'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:204800', // 200MB
        'coverImage' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:204800', // 200MB
        'postsRequireApproval' => 'boolean',
    ];

    protected $messages = [
        'name.required' => 'O nome do grupo é obrigatório.',
        'name.min' => 'O nome do grupo deve ter pelo menos 3 caracteres.',
        'name.max' => 'O nome do grupo não pode ter mais de 255 caracteres.',
        'name.unique' => 'Já existe um grupo com este nome.',
        'description.max' => 'A descrição não pode ter mais de 1000 caracteres.',
        'privacy.required' => 'Selecione o tipo de privacidade do grupo.',
        'privacy.in' => 'Tipo de privacidade inválido.',
        'image.image' => 'O arquivo deve ser uma imagem.',
        'image.mimes' => 'A imagem deve ser do tipo: jpeg, png, jpg, gif ou webp.',
        'image.max' => 'A imagem não pode ser maior que 200MB.',
        'coverImage.image' => 'O arquivo deve ser uma imagem.',
        'coverImage.mimes' => 'A imagem de capa deve ser do tipo: jpeg, png, jpg, gif ou webp.',
        'coverImage.max' => 'A imagem de capa não pode ser maior que 200MB.',
    ];

    public function create()
    {
        try {
            // Verificar se o usuário está autenticado
            if (!Auth::check()) {
                session()->flash('error', 'Você precisa estar logado para criar um grupo.');
                return redirect()->route('login');
            }

            // Validar dados
            $this->validate();

            // Verificar se já existe um grupo com o mesmo nome (case insensitive)
            $existingGroup = Group::whereRaw('LOWER(name) = ?', [strtolower($this->name)])->first();
            if ($existingGroup) {
                $this->addError('name', 'Já existe um grupo com este nome.');
                return;
            }

            $slug = Str::slug($this->name);
            $count = 1;
            $originalSlug = $slug;

            while (Group::where('slug', $slug)->exists()) {
                $slug = $originalSlug . '-' . $count++;
            }

            $data = [
                'name' => trim($this->name),
                'slug' => $slug,
                'description' => $this->description ? trim($this->description) : null,
                'privacy' => $this->privacy,
                'creator_id' => Auth::id(),
                'posts_require_approval' => $this->postsRequireApproval,
            ];

            // Handle image uploads
            if ($this->image) {
                $data['image'] = $this->image->store('groups/images', 'public');
            }

            if ($this->coverImage) {
                $data['cover_image'] = $this->coverImage->store('groups/covers', 'public');
            }

            // Debug information
            logger('Creating group with data:', $data);

            $group = Group::create($data);

            // Debug information
            logger('Group created with ID: ' . $group->id . ' with privacy: ' . $group->privacy);

            // Verificar se o grupo foi criado corretamente
            if (!$group) {
                throw new \Exception('Falha ao criar o grupo no banco de dados.');
            }

            // Verificar se o criador foi adicionado como membro admin (isso é feito no model)
            $membership = $group->members()->where('user_id', Auth::id())->first();
            if (!$membership) {
                logger('Warning: Creator was not automatically added as member for group: ' . $group->id);
            }

            // Limpar os campos do formulário
            $this->reset(['name', 'description', 'privacy', 'image', 'coverImage', 'postsRequireApproval']);
            $this->privacy = 'public'; // Reset to default

            session()->flash('success', 'Grupo criado com sucesso!');

            return redirect()->route('grupos.show', $group->slug);
        } catch (\Illuminate\Validation\ValidationException $e) {
            // Livewire automaticamente mostra os erros de validação
            return;
        } catch (\Exception $e) {
            logger('Error creating group: ' . $e->getMessage());
            logger('Stack trace: ' . $e->getTraceAsString());
            session()->flash('error', 'Erro ao criar grupo: ' . $e->getMessage());
            return null;
        }
    }

    // Validação em tempo real
    public function updated($propertyName)
    {
        // Validar nome em tempo real
        if ($propertyName === 'name') {
            $this->validateOnly('name');

            // Verificar se já existe um grupo com o mesmo nome
            if ($this->name) {
                $existingGroup = Group::whereRaw('LOWER(name) = ?', [strtolower($this->name)])->first();
                if ($existingGroup) {
                    $this->addError('name', 'Já existe um grupo com este nome.');
                }
            }
        }

        // Validar outros campos
        if (in_array($propertyName, ['description', 'privacy'])) {
            $this->validateOnly($propertyName);
        }

        logger('Property updated: ' . $propertyName . ' = ' . $this->$propertyName);
    }

    public function updatedImage()
    {
        $this->validateOnly('image');
    }

    public function updatedCoverImage()
    {
        $this->validateOnly('coverImage');
    }

    public function render()
    {
        return view('livewire.groups.create-group');
    }
}
