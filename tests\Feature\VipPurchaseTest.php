<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Product;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\VipSubscription;
use App\Services\VipPurchaseService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Notification;
use Tests\TestCase;

class VipPurchaseTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $vipProduct;
    protected $vipPurchaseService;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test user
        $this->user = User::factory()->create([
            'role' => 'visitante'
        ]);

        // Create VIP product
        $this->vipProduct = Product::create([
            'name' => 'VIP Test',
            'slug' => 'vip-test',
            'description' => 'Test VIP product',
            'price' => 29.90,
            'stock' => 999,
            'featured' => true,
            'status' => 'active',
            'is_digital' => true,
            'grants_vip_access' => true,
            'vip_duration_days' => 30,
            'vip_plan_type' => 'monthly',
            'vip_benefits' => 'Test benefits'
        ]);

        $this->vipPurchaseService = app(VipPurchaseService::class);
    }

    /** @test */
    public function it_can_detect_vip_products_in_order()
    {
        // Create order with VIP product
        $order = Order::create([
            'user_id' => $this->user->id,
            'total' => $this->vipProduct->price,
            'status' => 'pending',
            'payment_method' => 'stripe'
        ]);

        OrderItem::create([
            'order_id' => $order->id,
            'product_id' => $this->vipProduct->id,
            'quantity' => 1,
            'price' => $this->vipProduct->price
        ]);

        $order->load('items.product');

        $this->assertTrue($this->vipPurchaseService->orderContainsVipProducts($order));
    }

    /** @test */
    public function it_can_process_vip_purchase_successfully()
    {
        Notification::fake();

        // Create order with VIP product
        $order = Order::create([
            'user_id' => $this->user->id,
            'total' => $this->vipProduct->price,
            'status' => 'paid',
            'payment_method' => 'stripe',
            'payment_id' => 'test_session_123'
        ]);

        OrderItem::create([
            'order_id' => $order->id,
            'product_id' => $this->vipProduct->id,
            'quantity' => 1,
            'price' => $this->vipProduct->price
        ]);

        $order->load('items.product');

        // Process VIP purchase
        $result = $this->vipPurchaseService->processVipPurchase($order);

        $this->assertTrue($result);

        // Check if user role was updated
        $this->user->refresh();
        $this->assertEquals('vip', $this->user->role);

        // Check if VIP subscription was created
        $subscription = VipSubscription::where('user_id', $this->user->id)->first();
        $this->assertNotNull($subscription);
        $this->assertEquals('active', $subscription->status);
        $this->assertEquals(30, $subscription->plan_days);
    }

    /** @test */
    public function it_extends_existing_vip_subscription()
    {
        // Create existing VIP subscription
        $existingSubscription = VipSubscription::create([
            'user_id' => $this->user->id,
            'plan_days' => 30,
            'amount' => 29.90,
            'status' => 'active',
            'activated_at' => now(),
            'expires_at' => now()->addDays(15) // 15 days remaining
        ]);

        // Create order with VIP product
        $order = Order::create([
            'user_id' => $this->user->id,
            'total' => $this->vipProduct->price,
            'status' => 'paid',
            'payment_method' => 'stripe',
            'payment_id' => 'test_session_456'
        ]);

        OrderItem::create([
            'order_id' => $order->id,
            'product_id' => $this->vipProduct->id,
            'quantity' => 1,
            'price' => $this->vipProduct->price
        ]);

        $order->load('items.product');

        // Process VIP purchase
        $result = $this->vipPurchaseService->processVipPurchase($order);

        $this->assertTrue($result);

        // Check if subscription was extended
        $existingSubscription->refresh();
        $this->assertEquals(60, $existingSubscription->plan_days); // 30 + 30
        $this->assertTrue($existingSubscription->expires_at->gt(now()->addDays(40))); // Should be extended
    }

    /** @test */
    public function it_handles_non_vip_products_gracefully()
    {
        // Create regular product
        $regularProduct = Product::create([
            'name' => 'Regular Product',
            'slug' => 'regular-product',
            'description' => 'Regular product',
            'price' => 19.90,
            'stock' => 10,
            'status' => 'active',
            'grants_vip_access' => false
        ]);

        // Create order with regular product
        $order = Order::create([
            'user_id' => $this->user->id,
            'total' => $regularProduct->price,
            'status' => 'paid',
            'payment_method' => 'stripe'
        ]);

        OrderItem::create([
            'order_id' => $order->id,
            'product_id' => $regularProduct->id,
            'quantity' => 1,
            'price' => $regularProduct->price
        ]);

        $order->load('items.product');

        // Should not contain VIP products
        $this->assertFalse($this->vipPurchaseService->orderContainsVipProducts($order));

        // Process should return true but not create VIP subscription
        $result = $this->vipPurchaseService->processVipPurchase($order);
        $this->assertTrue($result);

        // User role should remain unchanged
        $this->user->refresh();
        $this->assertEquals('visitante', $this->user->role);

        // No VIP subscription should be created
        $subscription = VipSubscription::where('user_id', $this->user->id)->first();
        $this->assertNull($subscription);
    }
}
