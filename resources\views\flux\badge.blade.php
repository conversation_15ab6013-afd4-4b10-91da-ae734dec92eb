@props([
    'color' => 'gray',
    'size' => 'md',
    'variant' => 'solid',
])

@php
    $sizeClasses = match($size) {
        'sm' => 'px-2 py-1 text-xs',
        'lg' => 'px-3 py-2 text-sm',
        default => 'px-2.5 py-1.5 text-xs',
    };
    
    $colorClasses = match($color) {
        'red' => $variant === 'outline' 
            ? 'text-red-700 dark:text-red-400 border border-red-300 dark:border-red-600 bg-red-50 dark:bg-red-900/20'
            : 'text-red-50 bg-red-600',
        'green' => $variant === 'outline'
            ? 'text-green-700 dark:text-green-400 border border-green-300 dark:border-green-600 bg-green-50 dark:bg-green-900/20'
            : 'text-green-50 bg-green-600',
        'blue' => $variant === 'outline'
            ? 'text-blue-700 dark:text-blue-400 border border-blue-300 dark:border-blue-600 bg-blue-50 dark:bg-blue-900/20'
            : 'text-blue-50 bg-blue-600',
        'purple' => $variant === 'outline'
            ? 'text-purple-700 dark:text-purple-400 border border-purple-300 dark:border-purple-600 bg-purple-50 dark:bg-purple-900/20'
            : 'text-purple-50 bg-purple-600',
        'pink' => $variant === 'outline'
            ? 'text-pink-700 dark:text-pink-400 border border-pink-300 dark:border-pink-600 bg-pink-50 dark:bg-pink-900/20'
            : 'text-pink-50 bg-pink-600',
        'cyan' => $variant === 'outline'
            ? 'text-cyan-700 dark:text-cyan-400 border border-cyan-300 dark:border-cyan-600 bg-cyan-50 dark:bg-cyan-900/20'
            : 'text-cyan-50 bg-cyan-600',
        'yellow' => $variant === 'outline'
            ? 'text-yellow-700 dark:text-yellow-400 border border-yellow-300 dark:border-yellow-600 bg-yellow-50 dark:bg-yellow-900/20'
            : 'text-yellow-50 bg-yellow-600',
        default => $variant === 'outline'
            ? 'text-gray-700 dark:text-gray-400 border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-900/20'
            : 'text-gray-50 bg-gray-600',
    };
@endphp

<span {{ $attributes->merge(['class' => "inline-flex items-center rounded-full font-medium {$sizeClasses} {$colorClasses}"]) }}>
    {{ $slot }}
</span>
