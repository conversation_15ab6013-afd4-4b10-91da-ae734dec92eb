<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

class CleanLivewireUploads extends Command
{
    protected $signature = 'livewire:clean-uploads {--force : Force cleanup without confirmation}';
    protected $description = 'Clean old Livewire temporary upload files';

    public function handle()
    {
        $this->info('Cleaning Livewire temporary uploads...');

        $disk = Storage::disk('public');
        $directory = 'livewire-tmp';

        if (!$disk->exists($directory)) {
            $this->info('No temporary upload directory found.');
            return;
        }

        // Get all files in the livewire-tmp directory
        $files = $disk->allFiles($directory);
        $oldFiles = [];
        $cutoffTime = Carbon::now()->subHours(24); // Files older than 24 hours

        foreach ($files as $file) {
            $lastModified = Carbon::createFromTimestamp($disk->lastModified($file));
            
            if ($lastModified->lt($cutoffTime)) {
                $oldFiles[] = $file;
            }
        }

        if (empty($oldFiles)) {
            $this->info('No old temporary files found.');
            return;
        }

        $this->info('Found ' . count($oldFiles) . ' old temporary files.');

        if (!$this->option('force') && !$this->confirm('Do you want to delete these files?')) {
            $this->info('Cleanup cancelled.');
            return;
        }

        $deletedCount = 0;
        foreach ($oldFiles as $file) {
            try {
                $disk->delete($file);
                $deletedCount++;
                $this->line("Deleted: {$file}");
            } catch (\Exception $e) {
                $this->error("Failed to delete {$file}: " . $e->getMessage());
            }
        }

        $this->info("Cleanup completed. Deleted {$deletedCount} files.");
    }
}
