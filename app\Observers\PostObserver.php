<?php

namespace App\Observers;

use App\Models\Post;
use App\Helpers\StorageHelper;
use Illuminate\Support\Facades\Log;

class PostObserver
{
    /**
     * Handle the Post "created" event.
     */
    public function created(Post $post): void
    {
        $this->syncPostFiles($post);
    }

    /**
     * Handle the Post "updated" event.
     */
    public function updated(Post $post): void
    {
        $this->syncPostFiles($post);
    }

    /**
     * Handle the Post "deleted" event.
     */
    public function deleted(Post $post): void
    {
        $this->cleanupPostFiles($post);
    }

    /**
     * Sincroniza arquivos do post para public/storage em produção
     */
    private function syncPostFiles(Post $post): void
    {
        // Apenas em produção
        if (!app()->environment('production')) {
            return;
        }

        try {
            // Sincronizar imagem
            if ($post->image) {
                $this->syncFile($post->image);
            }

            // Sincronizar vídeo
            if ($post->video) {
                $this->syncFile($post->video);
            }
        } catch (\Exception $e) {
            Log::error('Erro ao sincronizar arquivos do post', [
                'post_id' => $post->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Remove arquivos do post de public/storage
     */
    private function cleanupPostFiles(Post $post): void
    {
        // Apenas em produção
        if (!app()->environment('production')) {
            return;
        }

        try {
            // Remover imagem
            if ($post->image) {
                $this->removeFile($post->image);
            }

            // Remover vídeo
            if ($post->video) {
                $this->removeFile($post->video);
            }
        } catch (\Exception $e) {
            Log::error('Erro ao limpar arquivos do post', [
                'post_id' => $post->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Sincroniza um arquivo específico
     */
    private function syncFile(string $path): void
    {
        $sourcePath = storage_path('app/public/' . $path);
        $publicPath = public_path('storage/' . $path);

        if (!file_exists($sourcePath)) {
            return;
        }

        // Criar diretório se não existir
        $publicDir = dirname($publicPath);
        if (!is_dir($publicDir)) {
            mkdir($publicDir, 0755, true);
        }

        // Copiar arquivo
        if (copy($sourcePath, $publicPath)) {
            Log::info("Arquivo sincronizado automaticamente: {$path}");
        } else {
            Log::error("Falha ao sincronizar arquivo: {$path}");
        }
    }

    /**
     * Remove um arquivo específico
     */
    private function removeFile(string $path): void
    {
        $publicPath = public_path('storage/' . $path);

        if (file_exists($publicPath)) {
            if (unlink($publicPath)) {
                Log::info("Arquivo removido de public/storage: {$path}");
            } else {
                Log::error("Falha ao remover arquivo: {$path}");
            }
        }
    }
}
