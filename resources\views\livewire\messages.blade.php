@php
    use Illuminate\Support\Facades\Storage;
@endphp

<div class="container mx-auto p-2 md:p-4" wire:poll.10s x-data="{ showConversations: true, isMobile: window.innerWidth < 768 }" x-init="
    $watch('isMobile', value => {
        if (!value && selectedConversation) showConversations = true;
    });
    window.addEventListener('resize', () => {
        isMobile = window.innerWidth < 768;
    });
">
    <!-- Mobile Header -->
    <div class="md:hidden mb-4 flex items-center justify-between bg-white dark:bg-zinc-800 rounded-lg p-3 shadow-md">
        <h1 class="text-lg font-semibold text-title">Mensagens</h1>
        @if($selectedConversation)
            <button
                @click="showConversations = !showConversations"
                class="flex items-center gap-2 text-sm text-purple-600 dark:text-purple-400"
            >
                <x-flux::icon name="chat-bubble-left-right" class="w-4 h-4" />
                <span x-text="showConversations ? 'Ver Conversa' : 'Ver Lista'"></span>
            </button>
        @endif
    </div>

    <div class="flex flex-col md:flex-row gap-2 md:gap-4 h-[calc(100vh-8rem)] md:h-[calc(100vh-12rem)]">
        <!-- Sidebar with conversations -->
        <section
            id="conversations"
            class="w-full md:w-1/3 bg-white dark:bg-zinc-800 rounded-lg shadow-md overflow-hidden flex flex-col"
            x-show="showConversations || !isMobile"
            x-transition:enter="transition ease-out duration-200"
            x-transition:enter-start="opacity-0 transform -translate-x-full"
            x-transition:enter-end="opacity-100 transform translate-x-0"
            x-transition:leave="transition ease-in duration-200"
            x-transition:leave-start="opacity-100 transform translate-x-0"
            x-transition:leave-end="opacity-0 transform -translate-x-full"
        >
            <div class="p-3 md:p-4 border-b border-gray-200 dark:border-zinc-700 flex justify-between items-center">
                <h2 class="text-lg md:text-xl font-semibold text-title">Conversas</h2>
                <div class="flex items-center gap-2">
                    <!-- Status selector -->
                    <div class="relative" x-data="{ open: false }">
                        <button
                            @click="open = !open"
                            type="button"
                            class="flex items-center text-sm text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white"
                        >
                            <span class="w-3 h-3 rounded-full mr-1
                                {{ Auth::user()->presence_status === 'online' ? 'bg-green-500' :
                                   (Auth::user()->presence_status === 'away' ? 'bg-yellow-500' :
                                    (Auth::user()->presence_status === 'dnd' ? 'bg-red-600' : 'bg-gray-500')) }}"></span>
                            <span>{{ ucfirst(Auth::user()->presence_status ?: 'offline') }}</span>
                            <x-flux::icon name="chevron-down" class="w-4 h-4 ml-1" />
                        </button>

                        <!-- Dropdown -->
                        <div
                            x-show="open"
                            @click.away="open = false"
                            class="absolute right-0 mt-2 w-48 bg-white dark:bg-zinc-800 rounded-md shadow-lg z-10"
                            x-transition:enter="transition ease-out duration-100"
                            x-transition:enter-start="transform opacity-0 scale-95"
                            x-transition:enter-end="transform opacity-100 scale-100"
                            x-transition:leave="transition ease-in duration-75"
                            x-transition:leave-start="transform opacity-100 scale-100"
                            x-transition:leave-end="transform opacity-0 scale-95"
                            style="display: none;"
                        >
                            <div class="py-1">
                                <button
                                    wire:click="updateUserStatus('online')"
                                    type="button"
                                    @click="open = false"
                                    class="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-zinc-700"
                                >
                                    <span class="w-3 h-3 rounded-full bg-green-500 mr-2"></span>
                                    Online
                                </button>
                                <button
                                    wire:click="updateUserStatus('away')"
                                    type="button"
                                    @click="open = false"
                                    class="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-zinc-700"
                                >
                                    <span class="w-3 h-3 rounded-full bg-yellow-500 mr-2"></span>
                                    Ausente
                                </button>
                                <button
                                    wire:click="updateUserStatus('dnd')"
                                    type="button"
                                    @click="open = false"
                                    class="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-zinc-700"
                                >
                                    <span class="w-3 h-3 rounded-full bg-red-600 mr-2"></span>
                                    Não Perturbe
                                </button>
                                <button
                                    wire:click="updateUserStatus('offline')"
                                    type="button"
                                    @click="open = false"
                                    class="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-zinc-700"
                                >
                                    <span class="w-3 h-3 rounded-full bg-gray-500 mr-2"></span>
                                    Offline
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Search input -->
                    <div class="relative">
                        <input
                            type="text"
                            wire:model.live="searchTerm"
                            placeholder="Buscar usuários..."
                            class="search-input w-full px-2 md:px-3 py-1.5 md:py-2 text-sm border border-gray-300 dark:border-zinc-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-zinc-700 text-gray-900 dark:text-zinc-100 placeholder-gray-500 dark:placeholder-zinc-400 transition-all"
                        >
                        <x-flux::icon name="magnifying-glass" class="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                    </div>
                </div>
            </div>

            <div class="overflow-y-auto flex-grow custom-scrollbar">
                @if($searchTerm)
                    <!-- Search results -->
                    <div class="p-2">
                        <!-- Conversas existentes -->
                        @if(count($conversations) > 0)
                            <h3 class="text-sm font-medium text-body-lighter mb-2">Conversas</h3>
                            @foreach($conversations as $conversation)
                                <div
                                    wire:click="selectConversation({{ $conversation['user']->id }}); if (window.innerWidth < 768) { showConversations = false; }"
                                    class="conversation-item flex items-center p-2 md:p-3 hover:bg-gray-100 dark:hover:bg-zinc-700 rounded-lg cursor-pointer transition-all fade-in {{ $selectedConversation == $conversation['user']->id ? 'bg-purple-50 dark:bg-zinc-700' : '' }}"
                                >
                                    <div class="relative avatar-container">
                                        @php
                                            $conversationAvatar = $conversation['user']->currentPhoto ?? $conversation['user']->userPhotos->first();
                                            $conversationAvatarUrl = $conversationAvatar ? Storage::url($conversationAvatar->photo_path) : asset('images/default-avatar.jpg');
                                            $conversationRankingBorderClass = \App\Helpers\RankingHelper::getRankingBorderClass($conversation['user']);
                                            $conversationShouldHaveWings = in_array($conversationRankingBorderClass, ['crown', 'diamond']);
                                        @endphp
                                        <div class="ranking-avatar {{ $conversationRankingBorderClass }}">
                                            @if($conversationShouldHaveWings)
                                                <div class="wings"></div>
                                            @endif
                                            <img
                                                src="{{ $conversationAvatarUrl }}"
                                                class="w-8 h-8 md:w-10 md:h-10 rounded-full object-cover"
                                            >
                                        </div>
                                        <div class="absolute bottom-0 right-0 w-3 h-3 rounded-full status-indicator {{ $conversation['user']->status === 'online' ? 'bg-green-500' : ($conversation['user']->status === 'away' ? 'bg-yellow-500' : 'bg-gray-500') }}"></div>
                                        @if($conversation['unread_count'] > 0)
                                            <div class="absolute -top-1 -right-1 bg-purple-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                                                {{ $conversation['unread_count'] }}
                                            </div>
                                        @endif
                                    </div>
                                    <div class="ml-2 md:ml-3 flex-grow min-w-0">
                                        <div class="flex justify-between items-center">
                                            <a href="/{{ $conversation['user']->username }}"
                                               wire:navigate
                                               class="font-medium text-sm md:text-base text-title hover:text-purple-600 transition-colors truncate">
                                                {{ $conversation['user']->name }}
                                            </a>
                                            <p class="text-xs text-body-lighter ml-2 flex-shrink-0">
                                                @if($conversation['latest_message'])
                                                    {{ $conversation['latest_message']->created_at->diffForHumans(null, true) }}
                                                @else
                                                    Sem mensagens
                                                @endif
                                            </p>
                                        </div>
                                        <p class="text-xs md:text-sm text-body-lighter truncate">
                                            @if($conversation['latest_message'])
                                                {{ $conversation['latest_message']->body }}
                                            @else
                                                Inicie uma conversa
                                            @endif
                                        </p>
                                    </div>
                                </div>
                            @endforeach
                        @endif

                        <!-- Novos usuários -->
                        @if(count($users) > 0)
                            <h3 class="text-sm font-medium text-body-lighter mb-2 {{ count($conversations) > 0 ? 'mt-4' : '' }}">Novos usuários</h3>
                            @foreach($users as $user)
                            <div
                                wire:click="startNewConversation({{ $user->id }})"
                                class="flex items-center p-2 md:p-3 hover:bg-gray-100 dark:hover:bg-zinc-700 rounded-lg cursor-pointer"
                            >
                                <div class="relative">
                                    @php
                                        $searchAvatar = $user->currentPhoto ?? $user->userPhotos->first();
                                        $searchAvatarUrl = $searchAvatar ? Storage::url($searchAvatar->photo_path) : asset('images/default-avatar.jpg');
                                        $searchRankingBorderClass = \App\Helpers\RankingHelper::getRankingBorderClass($user);
                                        $searchShouldHaveWings = in_array($searchRankingBorderClass, ['crown', 'diamond']);
                                    @endphp
                                    <div class="ranking-avatar {{ $searchRankingBorderClass }}">
                                        @if($searchShouldHaveWings)
                                            <div class="wings"></div>
                                        @endif
                                        <img
                                            src="{{ $searchAvatarUrl }}"
                                            class="w-8 h-8 md:w-10 md:h-10 rounded-full object-cover"
                                        >
                                    </div>
                                    <div class="absolute bottom-0 right-0 w-3 h-3 rounded-full
                                {{ $user->presence_status === 'online' ? 'bg-green-500 animate-pulse' :
                                   ($user->presence_status === 'away' ? 'bg-yellow-500' :
                                    ($user->presence_status === 'dnd' ? 'bg-red-600' : 'bg-gray-500')) }}"></div>
                                </div>
                                <div class="ml-3">
                                    <a href="/{{ $user->username }}" wire:navigate class="font-medium text-title hover:text-purple-600 transition-colors">
                                        {{ $user->name }}
                                    </a>
                                    <p class="text-xs text-body-lighter">{{'@' . $user->username }}</p>
                                </div>
                            </div>
                            @endforeach
                        @endif

                        @if(count($conversations) == 0 && count($users) == 0)
                            <p class="text-center text-body-lighter py-4">Nenhum resultado encontrado</p>
                        @endif
                    </div>
                @else
                    <!-- Conversation list -->
                    @forelse($conversations as $conversation)
                        <div
                            wire:click="selectConversation({{ $conversation['user']->id }}); if (window.innerWidth < 768) { showConversations = false; }"
                            class="conversation-item flex items-center p-2 md:p-3 hover:bg-gray-100 dark:hover:bg-zinc-700 cursor-pointer transition-all fade-in {{ $selectedConversation == $conversation['user']->id ? 'bg-purple-50 dark:bg-zinc-700' : '' }}"
                        >
                            <div class="relative avatar-container">
                                @php
                                    $conversationAvatar = $conversation['user']->currentPhoto ?? $conversation['user']->userPhotos->first();
                                    $conversationAvatarUrl = $conversationAvatar ? Storage::url($conversationAvatar->photo_path) : asset('images/default-avatar.jpg');
                                    $conversationRankingBorderClass = \App\Helpers\RankingHelper::getRankingBorderClass($conversation['user']);
                                    $conversationShouldHaveWings = in_array($conversationRankingBorderClass, ['crown', 'diamond']);
                                @endphp
                                <div class="ranking-avatar {{ $conversationRankingBorderClass }}">
                                    @if($conversationShouldHaveWings)
                                        <div class="wings"></div>
                                    @endif
                                    <img
                                        src="{{ $conversationAvatarUrl }}"
                                        class="w-8 h-8 md:w-10 md:h-10 rounded-full object-cover"
                                    >
                                </div>
                                <div class="absolute bottom-0 right-0 w-3 h-3 rounded-full status-indicator {{ $conversation['user']->status === 'online' ? 'bg-green-500' : ($conversation['user']->status === 'away' ? 'bg-yellow-500' : 'bg-gray-500') }}"></div>
                                @if($conversation['unread_count'] > 0)
                                    <div class="absolute -top-1 -right-1 bg-purple-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                                        {{ $conversation['unread_count'] }}
                                    </div>
                                @endif
                            </div>
                            <div class="ml-2 md:ml-3 flex-grow min-w-0">
                                <div class="flex justify-between items-center">
                                    <a href="/{{ $conversation['user']->username }}"
                                       wire:navigate
                                       class="font-medium text-sm md:text-base text-title hover:text-purple-600 transition-colors truncate">
                                        {{ $conversation['user']->name }}
                                    </a>
                                    <p class="text-xs text-body-lighter ml-2 flex-shrink-0">
                                        @if($conversation['latest_message'])
                                            {{ $conversation['latest_message']->created_at->diffForHumans(null, true) }}
                                        @else
                                            Sem mensagens
                                        @endif
                                    </p>
                                </div>
                                <p class="text-xs md:text-sm text-body-lighter truncate">
                                    @if($conversation['latest_message'])
                                        {{ $conversation['latest_message']->body }}
                                    @else
                                        Inicie uma conversa
                                    @endif
                                </p>
                            </div>
                        </div>
                    @empty
                        <div class="text-center text-body-lighter py-8">
                            <p>Nenhuma conversa encontrada</p>
                            <p class="text-sm mt-2">Use a busca para iniciar uma conversa</p>
                        </div>
                    @endforelse
                @endif
            </div>
        </section>

        <!-- Message area -->
        <div
            class="w-full md:w-2/3 bg-white dark:bg-zinc-800 rounded-lg shadow-md overflow-hidden flex flex-col"
            x-show="!showConversations || !isMobile"
            x-transition:enter="transition ease-out duration-200"
            x-transition:enter-start="opacity-0 transform translate-x-full"
            x-transition:enter-end="opacity-100 transform translate-x-0"
            x-transition:leave="transition ease-in duration-200"
            x-transition:leave-start="opacity-100 transform translate-x-0"
            x-transition:leave-end="opacity-0 transform translate-x-full"
        >
            @if($selectedConversation)
                @php
                    $selectedUser = $users->firstWhere('id', $selectedConversation) ??
                        collect($conversations)->firstWhere('user.id', $selectedConversation)['user'] ?? null;
                @endphp

                @if($selectedUser)
                    <!-- Conversation header -->
                    <div class="p-3 md:p-4 border-b border-gray-200 dark:border-zinc-700 flex items-center">
                        <!-- Mobile back button -->
                        <button
                            @click="showConversations = true"
                            class="md:hidden mr-3 p-1 text-gray-600 dark:text-gray-300 hover:text-purple-600"
                        >
                            <x-flux::icon name="arrow-left" class="w-5 h-5" />
                        </button>
                        <div class="relative">
                            @php
                                $selectedAvatar = $selectedUser->currentPhoto ?? $selectedUser->userPhotos->first();
                                $selectedAvatarUrl = $selectedAvatar ? Storage::url($selectedAvatar->photo_path) : asset('images/default-avatar.jpg');
                                $selectedRankingBorderClass = \App\Helpers\RankingHelper::getRankingBorderClass($selectedUser);
                                $selectedShouldHaveWings = in_array($selectedRankingBorderClass, ['crown', 'diamond']);
                            @endphp
                            <div class="ranking-avatar {{ $selectedRankingBorderClass }}">
                                @if($selectedShouldHaveWings)
                                    <div class="wings"></div>
                                @endif
                                <img
                                    src="{{ $selectedAvatarUrl }}"
                                    class="w-8 h-8 md:w-10 md:h-10 rounded-full object-cover"
                                >
                            </div>
                            <div class="absolute bottom-0 right-0 w-3 h-3 rounded-full
                                {{ $selectedUser->presence_status === 'online' ? 'bg-green-500 animate-pulse' :
                                   ($selectedUser->presence_status === 'away' ? 'bg-yellow-500' :
                                    ($selectedUser->presence_status === 'dnd' ? 'bg-red-600' : 'bg-gray-500')) }}"></div>
                        </div>
                        <div class="ml-2 md:ml-3">
                            <a href="/{{ $selectedUser->username }}"
                               wire:navigate
                               class="font-medium text-sm md:text-base text-title hover:text-purple-600 transition-colors">
                                {{ $selectedUser->name }}
                            </a>
                            <p class="text-xs text-body-lighter">
                                @if($selectedUser->presence_status === 'online')
                                    <span class="text-success">Online</span>
                                @elseif($selectedUser->presence_status === 'away')
                                    <span class="text-warning">Ausente</span>
                                @elseif($selectedUser->presence_status === 'dnd')
                                    <span class="text-danger">Não Perturbe</span>
                                @else
                                    <span class="text-body-lighter">Offline</span>
                                    @if($selectedUser->last_seen)
                                        · Visto por último {{ $selectedUser->last_seen->diffForHumans() }}
                                    @endif
                                @endif
                            </p>
                        </div>
                    </div>

                    <!-- Messages -->
                    <div class="flex-grow overflow-y-auto p-2 md:p-4 space-y-2 md:space-y-4 relative h-[400px] custom-scrollbar" id="message-container" style="height: 400px; overflow-y: auto;">
                        <!-- Botão de scroll para o final -->
                        <button
                            id="scroll-to-bottom-btn"
                            class="fixed bottom-16 md:bottom-20 right-4 md:right-8 bg-purple-600 text-white rounded-full p-2 md:p-3 shadow-lg hover:bg-purple-700 focus:outline-none z-50"
                            type="button"
                            onclick="simpleScrollToBottom();"
                        >
                            <svg class="w-4 h-4 md:w-5 md:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                            </svg>
                        </button>



                        <div id="messages-wrapper">
                            @foreach($messages as $message)
                                <div class="flex {{ $message->sender_id == Auth::id() ? 'justify-end' : 'justify-start' }} mb-2 md:mb-4">
                                    <div class="message-bubble {{ $message->sender_id == Auth::id() ? 'bg-purple-500 text-white hover-lift' : 'bg-gray-200 dark:bg-zinc-700 text-gray-800 dark:text-white hover-lift' }} rounded-lg p-2 md:p-3 max-w-[85%] md:max-w-xs lg:max-w-md transition-all">
                                        <p class="text-sm md:text-base break-words">{{ $message->body }}</p>
                                        <p class="text-xs {{ $message->sender_id == Auth::id() ? 'text-purple-100' : 'text-gray-500 dark:text-gray-400' }} text-right mt-1">
                                            {{ $message->created_at->format('H:i') }}
                                        </p>
                                    </div>
                                </div>
                            @endforeach

                            <!-- Elemento âncora para scroll -->
                            <div id="scroll-anchor" style="height: 1px; margin-top: 20px;"></div>
                        </div>

                        <!-- Indicador de digitação -->
                        @if(count($typingUsers) > 0)
                            <div class="px-2 md:px-4 py-2 text-xs md:text-sm text-gray-500 dark:text-gray-400 italic">
                                @if(count($typingUsers) == 1)
                                    {{ $typingUsers[0]['name'] }} está digitando
                                    <span class="typing-dots">
                                        <span>.</span><span>.</span><span>.</span>
                                    </span>
                                @elseif(count($typingUsers) == 2)
                                    {{ $typingUsers[0]['name'] }} e {{ $typingUsers[1]['name'] }} estão digitando
                                    <span class="typing-dots">
                                        <span>.</span><span>.</span><span>.</span>
                                    </span>
                                @else
                                    {{ $typingUsers[0]['name'] }} e mais {{ count($typingUsers) - 1 }} pessoas estão digitando
                                    <span class="typing-dots">
                                        <span>.</span><span>.</span><span>.</span>
                                    </span>
                                @endif
                            </div>
                        @endif

                    </div>

                    <!-- Message input -->
                    <div class="p-2 md:p-4 border-t border-gray-200 dark:border-zinc-700">
                        <form wire:submit.prevent="sendMessage" class="flex items-center gap-2">
                            <div x-data="mentionHashtagHandler()" x-id="['message-input']" class="flex-grow relative">
                                <input
                                    type="text"
                                    wire:model="messageBody"
                                    x-ref="textarea"
                                    :id="$id('message-input')"
                                    x-on:input="handleInput($event)"
                                    x-on:keydown="handleKeydown($event)"
                                    class="mention-hashtag-input message-input w-full px-3 md:px-4 py-2 text-sm md:text-base border border-gray-300 dark:border-zinc-600 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-zinc-700 text-gray-900 dark:text-zinc-100 placeholder-gray-500 dark:placeholder-zinc-400 transition-all"
                                    placeholder="Digite sua mensagem..."
                                >
                                <!-- Sugestões de menções -->
                                <div
                                    x-show="showMentions"
                                    x-transition
                                    :style="mentionStyle"
                                    class="bg-neutral-800 border border-neutral-700 rounded-md shadow-lg max-h-48 overflow-auto z-50"
                                    :id="$id('mentions-message')"
                                >
                                    <template x-for="user in mentionSuggestions" :key="user.id">
                                        <div
                                            @click="selectMention(user.username)"
                                            class="px-4 py-2 hover:bg-neutral-700 cursor-pointer text-gray-300 flex items-center"
                                        >
                                            <img :src="user.avatar" :alt="user.username" class="w-6 h-6 rounded-full mr-2">
                                            <span x-text="user.name + ' (@' + user.username + ')'"></span>
                                        </div>
                                    </template>
                                </div>

                                <!-- Sugestões de hashtags -->
                                <div
                                    x-show="showHashtags"
                                    x-transition
                                    :style="hashtagStyle"
                                    class="bg-neutral-800 border border-neutral-700 rounded-md shadow-lg max-h-48 overflow-auto z-50"
                                    :id="$id('hashtags-message')"
                                >
                                    <template x-for="hashtag in hashtagSuggestions" :key="hashtag.id">
                                        <div
                                            @click="selectHashtag(hashtag.name)"
                                            class="px-4 py-2 hover:bg-neutral-700 cursor-pointer text-gray-300"
                                        >
                                            <span x-text="`#${hashtag.name}`"></span>
                                        </div>
                                    </template>
                                </div>
                            </div>
                            <button
                                type="submit"
                                class="btn-primary bg-purple-500 hover:bg-purple-600 text-white px-3 md:px-4 py-2 rounded-r-lg flex-shrink-0 transition-all"
                            >
                                <x-flux::icon name="paper-airplane" class="w-4 h-4 md:w-5 md:h-5" />
                            </button>
                        </form>
                    </div>
                @endif
            @else
                <div class="flex-grow flex items-center justify-center p-4">
                    <div class="text-center text-body-lighter">
                        <x-flux::icon name="chat-bubble-left-right" class="w-12 h-12 md:w-16 md:h-16 mx-auto mb-4" />
                        <h3 class="text-lg md:text-xl font-medium mb-2 text-title">Suas mensagens</h3>
                        <p class="text-sm md:text-base">Selecione uma conversa ou inicie uma nova</p>

                        <!-- Mensagem de sessão (se houver) -->
                        @if(session('message'))
                            <div class="mt-4 p-2 bg-green-100 text-green-800 rounded-md">
                                {{ session('message') }}
                            </div>
                        @endif
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>

@push('styles')
<style>
    /* Animação dos pontos de digitação */
    .typing-dots {
        display: inline-block;
    }

    .typing-dots span {
        animation: typing-dots 1.4s infinite;
        animation-fill-mode: both;
    }

    .typing-dots span:nth-child(2) {
        animation-delay: 0.2s;
    }

    .typing-dots span:nth-child(3) {
        animation-delay: 0.4s;
    }

    @keyframes typing-dots {
        0%, 80%, 100% {
            opacity: 0.3;
        }
        40% {
            opacity: 1;
        }
    }

    /* Melhorias para mobile */
    @media (max-width: 768px) {
        /* Ajustar altura do container de mensagens */
        #message-container {
            height: calc(100vh - 16rem) !important;
        }

        /* Melhorar scroll em dispositivos touch */
        .overflow-y-auto {
            -webkit-overflow-scrolling: touch;
        }

        /* Ajustar tamanho dos avatares ranking */
        .ranking-avatar {
            transform: scale(0.8);
        }

        /* Melhorar toque em botões */
        button, .cursor-pointer {
            min-height: 44px;
            min-width: 44px;
        }

        /* Ajustar sugestões de menções/hashtags */
        .bg-neutral-800 {
            max-height: 200px;
        }
    }

    /* Melhorias para desktop */
    @media (min-width: 768px) {
        /* Hover states melhorados */
        .conversation-item:hover {
            background-color: rgba(147, 51, 234, 0.05);
            transform: translateX(2px);
            transition: all 0.2s ease-in-out;
        }

        .conversation-item:hover .avatar-container {
            transform: scale(1.05);
            transition: transform 0.2s ease-in-out;
        }

        /* Melhorar área de mensagens */
        #message-container {
            height: calc(100vh - 14rem);
        }

        /* Hover states para mensagens */
        .message-bubble:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease-in-out;
        }

        /* Input de mensagem melhorado */
        .message-input:focus {
            box-shadow: 0 0 0 3px rgba(147, 51, 234, 0.1);
            border-color: #9333ea;
        }

        /* Botões com hover states */
        .btn-primary:hover {
            background-color: #7c3aed;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(147, 51, 234, 0.3);
        }

        /* Scroll personalizado */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }

        .custom-scrollbar::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 3px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: rgba(147, 51, 234, 0.3);
            border-radius: 3px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: rgba(147, 51, 234, 0.5);
        }

        /* Animações de entrada */
        .fade-in {
            animation: fadeIn 0.3s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Status indicators melhorados */
        .status-indicator {
            box-shadow: 0 0 0 2px white;
        }

        /* Search input melhorado */
        .search-input:focus {
            box-shadow: 0 0 0 3px rgba(147, 51, 234, 0.1);
            border-color: #9333ea;
        }
    }

    /* Transições suaves */
    .transition-all {
        transition: all 0.2s ease-in-out;
    }

    /* Classes utilitárias */
    .hover-lift:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
</style>
@endpush

@push('scripts')
<script>
    // Variáveis removidas temporariamente

    // Função simples para scroll
    function simpleScrollToBottom() {
        try {
            console.log('=== SCROLL DEBUG ===');
            var container = document.getElementById('message-container');
            if (container) {
                console.log('Container encontrado');
                console.log('scrollHeight:', container.scrollHeight);
                console.log('scrollTop antes:', container.scrollTop);
                console.log('clientHeight:', container.clientHeight);

                container.scrollTop = container.scrollHeight;

                console.log('scrollTop depois:', container.scrollTop);

                // Tentar novamente após um delay
                setTimeout(() => {
                    container.scrollTop = container.scrollHeight;
                    console.log('scrollTop final:', container.scrollTop);
                }, 100);
            } else {
                console.log('❌ Container message-container NÃO encontrado');
                // Listar todos os elementos com ID para debug
                console.log('Elementos com ID na página:');
                document.querySelectorAll('[id]').forEach(el => {
                    console.log('- ID:', el.id);
                });
            }
        } catch (e) {
            console.error('Erro ao fazer scroll:', e);
        }
    }

    // Função para scroll direto (compatibilidade)
    function forceScrollToBottom() {
        simpleScrollToBottom();
    }

    // Funções de typing removidas temporariamente para evitar erros

    // Função de teste global
    window.testScroll = function() {
        console.log('=== TESTE DE SCROLL MANUAL ===');
        simpleScrollToBottom();
    };

    // Função simples para configurar scroll inicial
    function setupInitialScroll() {
        setTimeout(() => {
            simpleScrollToBottom();
        }, 100);

        setTimeout(() => {
            simpleScrollToBottom();
        }, 500);

        setTimeout(() => {
            simpleScrollToBottom();
        }, 1000);
    }

    // Executar scroll em vários momentos
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM carregado, configurando scroll');
        setupInitialScroll();

        // Melhorias para mobile
        if (window.innerWidth < 768) {
            // Ajustar viewport para mobile
            const viewport = document.querySelector('meta[name=viewport]');
            if (viewport) {
                viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
            }

            // Prevenir zoom ao focar em inputs
            document.addEventListener('touchstart', function() {}, true);
        }
    });

    // Scroll após cada atualização do Livewire (atualizado para Livewire 3)
    document.addEventListener('livewire:init', function() {
        console.log('Livewire inicializado');
        Livewire.hook('commit', ({ component, commit, respond, succeed, fail }) => {
            succeed(({ snapshot, effect }) => {
                console.log('Livewire commit executado');
                setTimeout(() => {
                    simpleScrollToBottom();
                }, 100);
                setTimeout(() => {
                    simpleScrollToBottom();
                }, 300);
            });
        });
    });

    // Scroll após enviar uma mensagem (sempre forçar)
    document.addEventListener('submit', function(e) {
        if (e.target.closest('form[wire\\:submit\\.prevent="sendMessage"]')) {
            console.log('Formulário enviado, fazendo scroll');
            setTimeout(() => {
                simpleScrollToBottom();
            }, 200);
            setTimeout(() => {
                simpleScrollToBottom();
            }, 500);
            setTimeout(() => {
                simpleScrollToBottom();
            }, 1000);
        }
    });

    // Adicionar evento de clique ao botão de scroll
    window.addEventListener('load', function() {
        console.log('Window carregado');
        var scrollButton = document.getElementById('scroll-to-bottom-btn');
        if (scrollButton) {
            scrollButton.addEventListener('click', function() {
                simpleScrollToBottom();
            });
        }
        setupInitialScroll();
    });

    // Adicionar listener para o evento scrollToBottom
    window.addEventListener('scrollToBottom', function() {
        console.log('Evento scrollToBottom recebido');
        simpleScrollToBottom();
    });

    // Adicionar listener para eventos do browser via Livewire 3
    document.addEventListener('livewire:init', function() {
        Livewire.on('scrollToBottom', function() {
            console.log('Livewire scrollToBottom recebido');
            simpleScrollToBottom();
        });

        Livewire.on('browser-event', function(data) {
            if (data.name === 'scrollToBottom') {
                console.log('Browser event scrollToBottom recebido');
                simpleScrollToBottom();
            }
        });
    });

    // Observer para detectar novas mensagens
    document.addEventListener('DOMContentLoaded', function() {
        const messagesWrapper = document.getElementById('messages-wrapper');
        if (messagesWrapper) {
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        console.log('Nova mensagem detectada pelo observer');
                        setTimeout(() => {
                            simpleScrollToBottom();
                        }, 50);
                    }
                });
            });

            observer.observe(messagesWrapper, {
                childList: true,
                subtree: true
            });
        }
    });
</script>
@endpush

<!-- Script inline para garantir o scroll -->
<script>
    // Função simples para scroll automático
    function simpleScrollToBottom() {
        try {
            var container = document.getElementById('message-container');
            if (container) {
                container.scrollTop = container.scrollHeight;

                // Tentar novamente após um delay para garantir
                setTimeout(() => {
                    container.scrollTop = container.scrollHeight;
                }, 100);
            }
        } catch (e) {
            console.error('Erro ao fazer scroll:', e);
        }
    }

    // Função de teste global
    window.testScroll = function() {
        console.log('=== TESTE DE SCROLL MANUAL ===');
        simpleScrollToBottom();
    };

    // Executar scroll quando a página terminar de carregar
    window.onload = function() {
        console.log('Window onload executado');
        setTimeout(() => {
            simpleScrollToBottom();
        }, 200);
    };

    // Listener para eventos do Livewire
    document.addEventListener('livewire:init', () => {
        Livewire.on('scrollToBottom', () => {
            console.log('Evento scrollToBottom recebido');
            simpleScrollToBottom();
        });
    });
</script>
