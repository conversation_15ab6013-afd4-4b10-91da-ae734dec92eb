<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AlbumReview extends Model
{
    use HasFactory;

    protected $fillable = [
        'album_id',
        'user_id',
        'rating',
        'comment',
        'is_verified_purchase',
    ];

    protected $casts = [
        'rating' => 'integer',
        'is_verified_purchase' => 'boolean',
    ];

    /**
     * Álbum avaliado.
     */
    public function album()
    {
        return $this->belongsTo(Album::class);
    }

    /**
     * Usuário que fez a avaliação.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope para reviews verificadas (de compradores).
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified_purchase', true);
    }

    /**
     * Scope para reviews por rating.
     */
    public function scopeByRating($query, $rating)
    {
        return $query->where('rating', $rating);
    }

    /**
     * Scope para reviews com comentários.
     */
    public function scopeWithComments($query)
    {
        return $query->whereNotNull('comment')->where('comment', '!=', '');
    }

    /**
     * Boot do modelo para atualizar rating do álbum.
     */
    protected static function boot()
    {
        parent::boot();

        static::created(function ($review) {
            $review->album->updateRating();
        });

        static::updated(function ($review) {
            $review->album->updateRating();
        });

        static::deleted(function ($review) {
            $review->album->updateRating();
        });
    }

    /**
     * Verifica se é uma compra verificada.
     */
    public function isVerifiedPurchase()
    {
        return $this->is_verified_purchase;
    }

    /**
     * Retorna o rating em formato de estrelas.
     */
    public function getStarsAttribute()
    {
        return str_repeat('★', $this->rating) . str_repeat('☆', 5 - $this->rating);
    }
}
