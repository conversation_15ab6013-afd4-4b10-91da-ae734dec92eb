<x-layouts.app.sidebar :title="'#' . $hashtag->name">
    <flux:main>
        <div class="container mx-auto px-4 py-8">
            <div class="mb-8">
                <h1 class="text-4xl font-bold text-gray-800 dark:text-white">#{{ $hashtag->name }}</h1>
                <p class="text-gray-600 dark:text-gray-400">{{ $hashtag->posts_count }} {{ Str::plural('post', $hashtag->posts_count) }}</p>
            </div>

            @if($posts->isEmpty())
                <div class="text-center py-12">
                    <x-flux::icon icon="tag" class="w-16 h-16 mx-auto text-gray-400 dark:text-gray-500 mb-4" />
                    <p class="text-xl text-gray-700 dark:text-gray-300">Nenhuma postagem encontrada para esta hashtag ainda.</p>
                </div>
            @else
                <div class="space-y-6">
                    @foreach($posts as $post)
                        <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-md overflow-hidden">
                            <div class="p-4 flex items-start justify-between">
                                <div class="flex items-center">
                                    <a href="{{ route('user.profile', $post->user->username) }}" class="flex-shrink-0">
                                        <img
                                            src="{{ $post->user->userPhotos->first() ? Storage::url($post->user->userPhotos->first()->photo_path) : asset('images/default-avatar.jpg') }}"
                                            alt="{{ $post->user->name }}"
                                            class="w-10 h-10 rounded-full object-cover"
                                        >
                                    </a>
                                    <div class="ml-3">
                                        <a href="{{ route('user.profile', $post->user->username) }}" class="text-sm font-medium text-gray-900 dark:text-white hover:underline">
                                            {{ $post->user->name }}
                                        </a>
                                        <div class="text-xs text-gray-500 dark:text-gray-400">
                                            {{ $post->created_at->diffForHumans() }}
                                            @if($post->group)
                                                em <a href="{{ route('groups.show', $post->group->id) }}" class="hover:underline">{{ $post->group->name }}</a>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>

                            @if($post->processed_content)
                                <div class="px-4 pb-3 prose dark:prose-invert max-w-none">
                                    {!! $post->processed_content !!}
                                </div>
                            @endif

                            @if($post->image)
                                <div class="pb-3">
                                    <img src="{{ Storage::url($post->image) }}" alt="Imagem da postagem" class="w-full h-auto">
                                </div>
                            @endif

                            @if($post->video)
                                <div class="pb-3">
                                    <video src="{{ Storage::url($post->video) }}" controls class="w-full h-auto"></video>
                                </div>
                            @endif

                            <div class="px-4 py-3 border-t border-gray-200 dark:border-gray-700">
                                <div class="flex items-center justify-between text-sm">
                                    <div class="text-gray-500 dark:text-gray-400">
                                        @if($post->likes_count > 0)
                                            <span>{{ $post->likes_count }} {{ Str::plural('curtida', $post->likes_count) }}</span>
                                        @endif
                                        @if($post->likes_count > 0 && $post->comments->count() > 0)
                                            <span class="mx-1">•</span>
                                        @endif
                                        @if($post->comments->count() > 0)
                                            <span>{{ $post->comments->count() }} {{ Str::plural('comentário', $post->comments->count()) }}</span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            <div class="px-4 py-2 border-t border-gray-200 dark:border-gray-700">
                                <a href="{{ route('post.show', $post->id) }}" class="text-sm text-blue-600 dark:text-blue-400 hover:underline">
                                    Ver postagem completa e comentários
                                </a>
                            </div>
                        </div>
                    @endforeach
                </div>

                <div class="mt-8">
                    {{ $posts->links() }}
                </div>
            @endif
        </div>
    </flux:main>
</x-layouts.app.sidebar>
