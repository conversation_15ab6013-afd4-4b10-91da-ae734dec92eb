<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class JobCategory extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'icon',
        'color',
        'is_active',
        'sort_order'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Boot do modelo
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($category) {
            if (empty($category->slug)) {
                $category->slug = Str::slug($category->name);
            }
        });
    }

    /**
     * Vagas desta categoria
     */
    public function jobs()
    {
        return $this->hasMany(JobVacancy::class, 'category_id');
    }

    /**
     * Vagas ativas desta categoria
     */
    public function activeJobs()
    {
        return $this->hasMany(JobVacancy::class, 'category_id')->where('is_active', true);
    }

    /**
     * Scope para categorias ativas
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope para ordenar por ordem de classificação
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc')->orderBy('name', 'asc');
    }

    /**
     * Obter o número total de vagas nesta categoria
     */
    public function getJobsCountAttribute()
    {
        return $this->jobs()->where('is_active', true)->count();
    }
}
