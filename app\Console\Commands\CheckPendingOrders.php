<?php

namespace App\Console\Commands;

use App\Models\Order;
use App\Mail\OrderPendingReminder;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class CheckPendingOrders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'orders:check-pending {--days=7 : Número de dias para considerar pedido pendente}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Verificar pedidos pendentes há X dias e enviar notificações por email';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = (int) $this->option('days');

        $this->info("🔍 Verificando pedidos pendentes há {$days} dias...");

        // Buscar pedidos pendentes há X dias
        $cutoffDate = Carbon::now()->subDays($days);

        $pendingOrders = Order::where('status', Order::STATUS_PENDING)
            ->where('created_at', '<=', $cutoffDate)
            ->with('user')
            ->get();

        if ($pendingOrders->isEmpty()) {
            $this->info("✅ Nenhum pedido pendente há {$days} dias encontrado.");
            return 0;
        }

        $this->info("📧 Encontrados {$pendingOrders->count()} pedidos pendentes. Enviando notificações...");

        $successCount = 0;
        $errorCount = 0;

        foreach ($pendingOrders as $order) {
            try {
                $user = $order->user;

                if (!$user) {
                    $this->warn("⚠️  Usuário não encontrado para o pedido #{$order->id}");
                    $errorCount++;
                    continue;
                }

                $daysPending = Carbon::parse($order->created_at)->diffInDays(Carbon::now());

                // Email para o usuário
                Mail::to($user->email)->send(new OrderPendingReminder($order, $user, $daysPending, false));

                // Email para administradores
                $adminEmails = $this->getAdminEmails();
                foreach ($adminEmails as $adminEmail) {
                    Mail::to($adminEmail)->send(new OrderPendingReminder($order, $user, $daysPending, true));
                }

                $this->line("📧 Notificações enviadas para pedido #{$order->id} - {$user->email}");

                Log::info('Notificações de pedido pendente enviadas', [
                    'order_id' => $order->id,
                    'user_email' => $user->email,
                    'days_pending' => $daysPending,
                    'admin_emails' => $adminEmails
                ]);

                $successCount++;
            } catch (\Exception $e) {
                $this->error("❌ Erro ao processar pedido #{$order->id}: {$e->getMessage()}");

                Log::error('Erro ao enviar notificação de pedido pendente', [
                    'order_id' => $order->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                $errorCount++;
            }
        }

        $this->info("✅ Processamento concluído:");
        $this->info("   📧 {$successCount} notificações enviadas com sucesso");

        if ($errorCount > 0) {
            $this->warn("   ❌ {$errorCount} erros encontrados");
        }

        return 0;
    }

    /**
     * Obter emails dos administradores
     */
    protected function getAdminEmails(): array
    {
        return [
            '<EMAIL>'
        ];
    }
}
