<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class NormalizeUrl
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Em produção, garantir que sempre use www.swingcuritiba.com.br
        if (app()->environment('production')) {
            $host = $request->getHost();
            $expectedHost = 'www.swingcuritiba.com.br';

            // Se não for o host correto, redirecionar
            if ($host !== $expectedHost) {
                $newUrl = $request->getScheme() . '://' . $expectedHost . $request->getRequestUri();
                return redirect($newUrl, 301);
            }
        }

        return $next($request);
    }
}
