<?php

namespace App\Mail;

use App\Models\Order;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class VipPurchaseAdminCopy extends Mailable
{
    use Queueable, SerializesModels;

    protected $order;
    protected $user;
    protected $vipProducts;

    /**
     * Create a new message instance.
     */
    public function __construct(Order $order, User $user, array $vipProducts)
    {
        $this->order = $order;
        $this->user = $user;
        $this->vipProducts = $vipProducts;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: '[ADMIN COPY] Nova Compra VIP - Pedido #' . $this->order->id,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        $totalVipDays = collect($this->vipProducts)->sum('vip_duration_days');
        $productNames = collect($this->vipProducts)->pluck('name')->implode(', ');

        return new Content(
            view: 'emails.admin-vip-purchase',
            with: [
                'order' => $this->order,
                'user' => $this->user,
                'vipProducts' => $this->vipProducts,
                'totalVipDays' => $totalVipDays,
                'productNames' => $productNames,
                'adminRecipient' => (object) [
                    'name' => 'Administração',
                    'email' => '<EMAIL>'
                ],
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
