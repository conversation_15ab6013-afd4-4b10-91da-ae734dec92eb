<?php

namespace App\Mail;

use App\Models\Order;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class OrderStatusChangedAdminCopy extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    protected $order;
    protected $user;
    protected $oldStatus;
    protected $newStatus;

    /**
     * Create a new message instance.
     */
    public function __construct(Order $order, User $user, string $oldStatus, string $newStatus)
    {
        $this->order = $order;
        $this->user = $user;
        $this->oldStatus = $oldStatus;
        $this->newStatus = $newStatus;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $statusMessages = [
            'pending' => 'Pedido Pendente',
            'processing' => 'Pedido em Processamento',
            'completed' => 'Pedido Concluído',
            'shipped' => 'Pedido Enviado',
            'delivered' => 'Pedido Entregue',
            'cancelled' => 'Pedido Cancelado',
            'refunded' => 'Pedido Reembolsado',
            'failed' => 'Falha no Pedido'
        ];

        $statusText = $statusMessages[$this->newStatus] ?? 'Status Atualizado';

        return new Envelope(
            subject: '[ADMIN COPY] ' . $statusText . ' - Pedido #' . $this->order->id . ' - ' . $this->user->name,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.admin-order-status-changed',
            with: [
                'order' => $this->order,
                'user' => $this->user,
                'oldStatus' => $this->oldStatus,
                'newStatus' => $this->newStatus,
                'adminRecipient' => (object) [
                    'name' => 'Administração',
                    'email' => '<EMAIL>'
                ],
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
