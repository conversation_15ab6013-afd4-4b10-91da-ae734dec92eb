<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;

class FixProductionStorage extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'storage:fix-production {--check : Only check without fixing}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix storage issues in production environment';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Verificando configuração de storage para produção...');
        $this->newLine();

        $checkOnly = $this->option('check');
        $issues = [];
        $fixes = [];

        // 1. Verificar se o link simbólico existe
        $this->checkSymbolicLink($issues, $fixes, $checkOnly);

        // 2. Verificar permissões dos diretórios
        $this->checkPermissions($issues, $fixes, $checkOnly);

        // 3. Verificar configuração do APP_URL
        $this->checkAppUrl($issues, $fixes);

        // 4. Verificar se os diretórios necessários existem
        $this->checkDirectories($issues, $fixes, $checkOnly);

        // 5. Testar URLs de imagem
        $this->testImageUrls($issues, $fixes);

        // Resumo
        $this->newLine();
        if (empty($issues)) {
            $this->info('✅ Nenhum problema encontrado!');
        } else {
            $this->error('❌ Problemas encontrados:');
            foreach ($issues as $issue) {
                $this->warn('  • ' . $issue);
            }
        }

        if (!$checkOnly && !empty($fixes)) {
            $this->newLine();
            $this->info('🔧 Correções aplicadas:');
            foreach ($fixes as $fix) {
                $this->info('  ✓ ' . $fix);
            }
        }

        if ($checkOnly && !empty($issues)) {
            $this->newLine();
            $this->comment('💡 Execute sem --check para aplicar as correções automaticamente.');
        }

        return empty($issues) ? 0 : 1;
    }

    private function checkSymbolicLink(&$issues, &$fixes, $checkOnly)
    {
        $publicStorage = public_path('storage');
        $storagePublic = storage_path('app/public');

        if (!is_link($publicStorage)) {
            $issues[] = 'Link simbólico storage não existe';
            
            if (!$checkOnly) {
                if (is_dir($publicStorage)) {
                    // Remove diretório existente se não for um link
                    File::deleteDirectory($publicStorage);
                }
                
                // Criar link simbólico
                if (symlink($storagePublic, $publicStorage)) {
                    $fixes[] = 'Link simbólico criado com sucesso';
                } else {
                    $issues[] = 'Falha ao criar link simbólico - verifique permissões';
                }
            }
        } else {
            $this->info('✅ Link simbólico storage: OK');
        }
    }

    private function checkPermissions(&$issues, &$fixes, $checkOnly)
    {
        $storageDir = storage_path('app/public');
        $publicStorage = public_path('storage');

        // Verificar se o diretório storage é gravável
        if (!is_writable($storageDir)) {
            $issues[] = 'Diretório storage não é gravável: ' . $storageDir;
            
            if (!$checkOnly) {
                if (chmod($storageDir, 0755)) {
                    $fixes[] = 'Permissões do diretório storage corrigidas';
                } else {
                    $issues[] = 'Falha ao corrigir permissões - execute manualmente: chmod 755 ' . $storageDir;
                }
            }
        } else {
            $this->info('✅ Permissões storage: OK');
        }

        // Verificar permissões do link público
        if (is_link($publicStorage) && !is_readable($publicStorage)) {
            $issues[] = 'Link público não é legível: ' . $publicStorage;
        } else if (is_link($publicStorage)) {
            $this->info('✅ Permissões link público: OK');
        }
    }

    private function checkAppUrl(&$issues, &$fixes)
    {
        $appUrl = config('app.url');
        $expectedUrl = 'https://www.swingcuritiba.com.br';

        if ($appUrl !== $expectedUrl) {
            $issues[] = "APP_URL incorreta: {$appUrl} (esperado: {$expectedUrl})";
        } else {
            $this->info('✅ APP_URL: OK');
        }

        // Verificar configuração do disco público
        $diskUrl = config('filesystems.disks.public.url');
        $expectedDiskUrl = $expectedUrl . '/storage';
        
        if ($diskUrl !== $expectedDiskUrl) {
            $issues[] = "URL do disco público incorreta: {$diskUrl} (esperado: {$expectedDiskUrl})";
        } else {
            $this->info('✅ URL disco público: OK');
        }
    }

    private function checkDirectories(&$issues, &$fixes, $checkOnly)
    {
        $directories = [
            'posts/images',
            'posts/videos',
            'avatars',
            'covers',
            'albums',
            'groups',
            'events',
            'products'
        ];

        foreach ($directories as $dir) {
            $fullPath = storage_path('app/public/' . $dir);
            
            if (!is_dir($fullPath)) {
                $issues[] = "Diretório não existe: {$dir}";
                
                if (!$checkOnly) {
                    if (File::makeDirectory($fullPath, 0755, true)) {
                        $fixes[] = "Diretório criado: {$dir}";
                    } else {
                        $issues[] = "Falha ao criar diretório: {$dir}";
                    }
                }
            }
        }

        if (empty($issues)) {
            $this->info('✅ Diretórios necessários: OK');
        }
    }

    private function testImageUrls(&$issues, &$fixes)
    {
        // Testar se conseguimos gerar URLs válidas
        $testPath = 'posts/images/test.jpg';
        $url = Storage::disk('public')->url($testPath);
        
        $this->info("🔗 URL de teste gerada: {$url}");
        
        // Verificar se a URL está no formato correto
        if (!str_starts_with($url, 'https://www.swingcuritiba.com.br/storage/')) {
            $issues[] = "URL gerada incorreta: {$url}";
        } else {
            $this->info('✅ Geração de URLs: OK');
        }
    }
}
